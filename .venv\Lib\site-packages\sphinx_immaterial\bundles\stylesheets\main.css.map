{"version": 3, "sources": ["src/templates/assets/stylesheets/main/components/_meta.scss", "../../../src/templates/assets/stylesheets/main.scss", "src/templates/assets/stylesheets/main/_resets.scss", "src/templates/assets/stylesheets/main/_colors.scss", "src/templates/assets/stylesheets/main/_icons.scss", "src/templates/assets/stylesheets/main/_typeset.scss", "src/templates/assets/stylesheets/utilities/_break.scss", "src/templates/assets/stylesheets/main/components/_author.scss", "src/templates/assets/stylesheets/main/components/_banner.scss", "src/templates/assets/stylesheets/main/components/_base.scss", "src/templates/assets/stylesheets/main/components/_clipboard.scss", "src/templates/assets/stylesheets/main/components/_code.scss", "src/templates/assets/stylesheets/main/components/_consent.scss", "src/templates/assets/stylesheets/main/components/_content.scss", "src/templates/assets/stylesheets/main/components/_dialog.scss", "src/templates/assets/stylesheets/main/components/_feedback.scss", "src/templates/assets/stylesheets/main/components/_footer.scss", "src/templates/assets/stylesheets/main/components/_form.scss", "src/templates/assets/stylesheets/main/components/_header.scss", "src/templates/assets/stylesheets/main/components/_hero.scss", "node_modules/material-design-color/material-color.scss", "src/templates/assets/stylesheets/main/components/_nav.scss", "src/templates/assets/stylesheets/main/components/_pagination.scss", "src/templates/assets/stylesheets/main/components/_post.scss", "src/templates/assets/stylesheets/main/components/_progress.scss", "src/templates/assets/stylesheets/main/components/_search.scss", "src/templates/assets/stylesheets/main/components/_select.scss", "src/templates/assets/stylesheets/main/components/_sidebar.scss", "src/templates/assets/stylesheets/main/components/_source.scss", "src/templates/assets/stylesheets/main/components/_status.scss", "src/templates/assets/stylesheets/main/components/_tabs.scss", "src/templates/assets/stylesheets/main/components/_tag.scss", "src/templates/assets/stylesheets/main/components/_tooltip.scss", "src/templates/assets/stylesheets/main/components/_tooltip2.scss", "src/templates/assets/stylesheets/main/components/_top.scss", "src/templates/assets/stylesheets/main/components/_version.scss", "src/templates/assets/stylesheets/main/extensions/markdown/_admonition.scss", "src/templates/assets/stylesheets/main/extensions/markdown/_footnotes.scss", "src/templates/assets/stylesheets/main/extensions/markdown/_toc.scss", "src/templates/assets/stylesheets/main/extensions/pymdownx/_arithmatex.scss", "src/templates/assets/stylesheets/main/extensions/pymdownx/_critic.scss", "src/templates/assets/stylesheets/main/extensions/pymdownx/_details.scss", "src/templates/assets/stylesheets/main/extensions/pymdownx/_emoji.scss", "src/templates/assets/stylesheets/main/extensions/pymdownx/_highlight.scss", "src/templates/assets/stylesheets/main/extensions/pymdownx/_keys.scss", "src/templates/assets/stylesheets/main/extensions/pymdownx/_tabbed.scss", "src/templates/assets/stylesheets/main/extensions/pymdownx/_tasklist.scss", "src/templates/assets/stylesheets/main/extensions/readthedocs/_readthedocs.scss", "src/templates/assets/stylesheets/main/integrations/_giscus.scss", "src/templates/assets/stylesheets/main/integrations/_mermaid.scss", "src/templates/assets/stylesheets/main/integrations/_graphviz.scss", "src/templates/assets/stylesheets/main/modifiers/_grid.scss", "src/templates/assets/stylesheets/main/modifiers/_inline.scss", "src/templates/assets/stylesheets/main/_sphinx.scss", "src/templates/assets/stylesheets/main/_api.scss"], "names": [], "mappings": "AA0CE,gBC01CF,CCx2CA,KAEE,6BAAA,CAAA,0BAAA,CAAA,qBAAA,CADA,qBDzBF,CC8BA,iBAGE,kBD3BF,CC8BE,gCANF,iBAOI,yBDzBF,CACF,CC6BA,KACE,QD1BF,CC8BA,qBAIE,uCD3BF,CC+BA,EACE,aAAA,CACA,oBD5BF,CCgCA,GAME,QAAA,CALA,sBAAA,CACA,aAAA,CACA,aAAA,CAEA,gBAAA,CADA,SD3BF,CCiCA,MACE,aD9BF,CCkCA,QAEE,eD/BF,CCmCA,IACE,iBDhCF,CCoCA,MAEE,wBAAA,CADA,gBDhCF,CCqCA,MAEE,eAAA,CACA,kBDlCF,CCsCA,OAKE,sBAAA,CACA,QAAA,CAHA,mBAAA,CACA,iBAAA,CAFA,QAAA,CADA,SD9BF,CCuCA,MACE,QAAA,CACA,YDpCF,CErDA,MAIE,6BAAA,CACA,oCAAA,CACA,mCAAA,CACA,0BAAA,CACA,+CAAA,CAGA,4BAAA,CACA,qDAAA,CACA,yBAAA,CACA,8CFmDF,CE7CA,+BAIE,kBF6CF,CE1CE,oHAEE,YF4CJ,CEnCA,qCAIE,eAAA,CAGA,qCAAA,CACA,4CAAA,CACA,8CAAA,CACA,+CAAA,CACA,0BAAA,CACA,+CAAA,CACA,iDAAA,CACA,mDAAA,CAGA,0BAAA,CACA,0BAAA,CAGA,0BAAA,CACA,6CAAA,CAGA,iCAAA,CACA,kCAAA,CACA,mCAAA,CACA,mCAAA,CACA,kCAAA,CACA,iCAAA,CACA,+CAAA,CACA,6DAAA,CACA,gEAAA,CACA,4DAAA,CACA,4DAAA,CACA,6DAAA,CAGA,6CAAA,CAGA,+CAAA,CAGA,0CAAA,CACA,2CAAA,CAGA,8BAAA,CACA,kCAAA,CACA,qCAAA,CAGA,0CAAA,CAGA,wCAAA,CACA,gDAAA,CAGA,mDAAA,CACA,mDAAA,CAGA,qCAAA,CACA,0BAAA,CAGA,yBAAA,CACA,8CAAA,CACA,iDAAA,CACA,oCAAA,CACA,0CAAA,CAGA,yEAAA,CAKA,yEAAA,CAKA,yEFKF,CG9HE,aAIE,iBAAA,CAHA,aAAA,CAEA,aAAA,CADA,YHmIJ,CG5HA,uBAME,2CAAA,CADA,UAAA,CAJA,mBAAA,CAEA,cAAA,CAKA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAHA,uBAAA,CAFA,aHqIF,CInJA,KACE,kCAAA,CACA,iCAAA,CAGA,uGAAA,CAKA,mFJoJF,CI9IA,iBAIE,mCAAA,CACA,6BAAA,CAFA,sCJmJF,CI7IA,aAIE,4BAAA,CADA,sCJiJF,CIxIA,MACE,wNAAA,CACA,gNAAA,CACA,iNJ2IF,CIpIA,YAIE,gCAAA,CAAA,kBAAA,CAHA,eAAA,CACA,eAAA,CACA,wBJwIF,CInIE,aARF,YASI,gBJsIF,CACF,CInIE,uGAME,iBAAA,CAAA,cJqIJ,CIjIE,eAKE,uCAAA,CAHA,aAAA,CAEA,eAAA,CAHA,iBJwIJ,CI/HE,8BAPE,eAAA,CAGA,qBJ0IJ,CItIE,eAEE,kBAAA,CAEA,eAAA,CAHA,oBJqIJ,CI7HE,eAEE,gBAAA,CACA,eAAA,CAEA,qBAAA,CADA,eAAA,CAHA,mBJmIJ,CI3HE,kBACE,eJ6HJ,CIzHE,eAEE,eAAA,CACA,qBAAA,CAFA,YJ6HJ,CIvHE,8BAKE,uCAAA,CAFA,cAAA,CACA,eAAA,CAEA,qBAAA,CAJA,eJ6HJ,CIrHE,eACE,wBJuHJ,CIpHI,oBACE,mBJsHN,CIjHE,eAGE,+DAAA,CAFA,iBAAA,CACA,cJoHJ,CI/GE,cACE,+BAAA,CACA,qBJiHJ,CI9GI,mCAEE,sBJ+GN,CI3GI,wCACE,+BJ6GN,CI1GM,kDACE,uDJ4GR,CIvGI,mBACE,kBAAA,CACA,iCJyGN,CIrGI,4BACE,uCAAA,CACA,oBJuGN,CIlGE,iDAIE,6BAAA,CACA,aAAA,CAFA,2BJsGJ,CIjGI,aARF,iDASI,oBJsGJ,CACF,CIlGE,iBAIE,wCAAA,CACA,mBAAA,CACA,kCAAA,CAAA,0BAAA,CAJA,eAAA,CADA,uBAAA,CAEA,qBJuGJ,CIjGI,qCAEE,uCAAA,CADA,YJoGN,CI9FE,gBAEE,iBAAA,CACA,eAAA,CAFA,iBJkGJ,CI7FI,qBAWE,kCAAA,CAAA,0BAAA,CADA,eAAA,CATA,aAAA,CAEA,QAAA,CAMA,uCAAA,CALA,aAAA,CAFA,oCAAA,CAKA,+DAAA,CACA,oBAAA,CAFA,iBAAA,CADA,iBJqGN,CI5FM,2BACE,qDJ8FR,CI1FM,wCAEE,YAAA,CADA,WJ6FR,CIxFM,8CACE,oDJ0FR,CIvFQ,oDACE,0CJyFV,CIjFI,8GAEE,YJmFN,CI9EE,gBAOE,4CAAA,CACA,mBAAA,CACA,mKACE,CANF,gCAAA,CAHA,oBAAA,CAEA,eAAA,CADA,uBAAA,CAIA,uBAAA,CADA,qBJoFJ,CIzEE,iBAGE,6CAAA,CACA,kCAAA,CAAA,0BAAA,CAHA,aAAA,CACA,qBJ6EJ,CIvEE,iBAGE,6DAAA,CADA,WAAA,CADA,oBJ2EJ,CIrEE,kBACE,WJuEJ,CInEE,oDAEE,qBJqEJ,CIvEE,oDAEE,sBJqEJ,CIjEE,iCACE,kBJsEJ,CIvEE,iCACE,mBJsEJ,CIvEE,iCAIE,2DJmEJ,CIvEE,iCAIE,4DJmEJ,CIvEE,uBAGE,uCAAA,CADA,aAAA,CAAA,cJqEJ,CI/DE,eACE,oBJiEJ,CI7DI,qBACE,4BJ+DN,CI1DE,kDAGE,kBJ4DJ,CI/DE,kDAGE,mBJ4DJ,CI/DE,8BAEE,SJ6DJ,CIzDI,0DACE,iBJ4DN,CIxDI,oCACE,2BJ2DN,CIxDM,0CACE,2BJ2DR,CIxDQ,gDACE,2BJ2DV,CIxDU,sDACE,2BJ2DZ,CInDI,0CACE,4BJsDN,CIlDI,wDACE,kBJsDN,CIvDI,wDACE,mBJsDN,CIvDI,oCAEE,kBJqDN,CIlDM,kGAEE,aJsDR,CIlDM,0DACE,eJqDR,CIjDM,4HAEE,kBJoDR,CItDM,4HAEE,mBJoDR,CItDM,oFACE,kBAAA,CAAA,eJqDR,CI9CE,yBAEE,mBJgDJ,CIlDE,yBAEE,oBJgDJ,CIlDE,eACE,mBAAA,CAAA,cJiDJ,CI5CE,kDAIE,WAAA,CADA,cJ+CJ,CIvCI,4BAEE,oBJyCN,CIrCI,6BAEE,oBJuCN,CInCI,kCACE,YJqCN,CIhCE,mBACE,iBAAA,CAGA,eAAA,CADA,cAAA,CAEA,iBAAA,CAHA,sBAAA,CAAA,iBJqCJ,CI/BI,uBACE,aAAA,CACA,aJiCN,CI5BE,uBAGE,iBAAA,CADA,eAAA,CADA,eJgCJ,CI1BE,mBACE,cJ4BJ,CIxBE,mCAaE,2CAAA,CACA,iDAAA,CACA,mBAAA,CAXA,aAAA,CAOA,gBAAA,CAFA,cAAA,CACA,aAAA,CAEA,iBAAA,CAJA,sBAAA,CAAA,iBJ2BJ,CIjBI,aAlBF,mCAmBI,aJoBJ,CACF,CIfI,qCACE,gBJiBN,CIVM,sGACE,YJYR,CIRM,oGACE,eJUR,CILI,oLACE,eJON,CIJM,wMACE,gBJMR,CIDI,sCAGE,eAAA,CAFA,cAAA,CACA,sBAAA,CAEA,kBJGN,CICI,sCAGE,qDAAA,CAFA,sBAAA,CACA,kBJEN,CIGI,4CACE,iCJDN,CIIM,kDACE,qDAAA,CACA,sDJFR,CIOI,qCACE,iBJLN,CIUE,wCACE,cJRJ,CIWI,wDAIE,gBJHN,CIDI,wDAIE,iBJHN,CIDI,8CAME,UAAA,CALA,oBAAA,CAEA,YAAA,CAIA,oDAAA,CAAA,4CAAA,CACA,6BAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,iBAAA,CACA,iCAAA,CALA,0BAAA,CAHA,WJDN,CIaI,oDACE,oDJXN,CIeI,mEACE,kDAAA,CACA,yDAAA,CAAA,iDJbN,CIiBI,oEACE,kDAAA,CACA,0DAAA,CAAA,kDJfN,CIoBE,wBACE,iBAAA,CACA,eAAA,CACA,iBJlBJ,CIsBE,mBACE,oBAAA,CAEA,kBAAA,CADA,eJnBJ,CIuBI,aANF,mBAOI,aJpBJ,CACF,CIuBI,8BACE,aAAA,CAEA,QAAA,CACA,eAAA,CAFA,UJnBN,CKtXI,0CDwZF,uBACE,iBJ9BF,CIiCE,4BACE,eJ/BJ,CACF,CMrjBE,uBAOE,kBAAA,CALA,aAAA,CACA,aAAA,CAEA,aAAA,CACA,eAAA,CALA,iBAAA,CAOA,sCACE,CALF,YN2jBJ,CMljBI,2BACE,aNojBN,CMhjBI,6BAME,+CAAA,CAFA,yCAAA,CAHA,eAAA,CACA,eAAA,CACA,kBAAA,CAEA,iBNmjBN,CM9iBI,6BAEE,aAAA,CADA,YNijBN,CM3iBE,wBACE,kBN6iBJ,CM1iBI,4BAIE,kBAAA,CAHA,mCAAA,CAIA,uBN0iBN,CMtiBI,4DAEE,oBAAA,CADA,SNyiBN,CMriBM,oEACE,mBNuiBR,COhmBA,WAGE,0CAAA,CADA,+BAAA,CADA,aPqmBF,COhmBE,aANF,WAOI,YPmmBF,CACF,COhmBE,oBAEE,2CAAA,CADA,gCPmmBJ,CO9lBE,kBAGE,eAAA,CADA,iBAAA,CADA,ePkmBJ,CO5lBE,6BACE,WPimBJ,COlmBE,6BACE,UPimBJ,COlmBE,mBAEE,aAAA,CACA,cAAA,CACA,uBP8lBJ,CO3lBI,0BACE,YP6lBN,COzlBI,yBACE,UP2lBN,CQhoBA,KASE,cAAA,CARA,WAAA,CACA,iBRooBF,CKheI,oCGtKJ,KAaI,gBR6nBF,CACF,CKreI,oCGtKJ,KAkBI,cR6nBF,CACF,CQxnBA,KASE,2CAAA,CAPA,YAAA,CACA,qBAAA,CAKA,eAAA,CAHA,eAAA,CAJA,iBAAA,CAGA,UR8nBF,CQtnBE,aAZF,KAaI,aRynBF,CACF,CKteI,0CGhJF,yBAII,cRsnBJ,CACF,CQ7mBA,SAEE,gBAAA,CAAA,iBAAA,CADA,eRinBF,CQ5mBA,cACE,YAAA,CAEA,qBAAA,CADA,WRgnBF,CQ5mBE,aANF,cAOI,aR+mBF,CACF,CQ3mBA,SACE,WR8mBF,CQ3mBE,gBACE,YAAA,CACA,WAAA,CACA,iBR6mBJ,CQxmBA,aACE,eAAA,CACA,sBR2mBF,CQlmBA,WACE,YRqmBF,CQhmBA,WAGE,QAAA,CACA,SAAA,CAHA,iBAAA,CACA,ORqmBF,CQhmBE,uCACE,aRkmBJ,CQ9lBE,+BAEE,uCAAA,CADA,kBRimBJ,CQ3lBA,SASE,2CAAA,CACA,mBAAA,CAFA,gCAAA,CADA,gBAAA,CADA,YAAA,CAMA,SAAA,CADA,uCAAA,CANA,mBAAA,CAJA,cAAA,CAYA,2BAAA,CATA,URqmBF,CQzlBE,eAEE,SAAA,CAIA,uBAAA,CAHA,oEACE,CAHF,UR8lBJ,CQhlBA,MACE,WRmlBF,CS5uBA,MACE,6PT8uBF,CSxuBA,cASE,mBAAA,CAFA,0CAAA,CACA,cAAA,CAFA,YAAA,CAIA,uCAAA,CACA,oBAAA,CAVA,iBAAA,CAEA,UAAA,CADA,QAAA,CAUA,qBAAA,CAPA,WAAA,CADA,STmvBF,CSxuBE,aAfF,cAgBI,YT2uBF,CACF,CSxuBE,kCAEE,uCAAA,CADA,YT2uBJ,CStuBE,qBACE,uCTwuBJ,CSpuBE,wCACE,+BTsuBJ,CSjuBE,oBAME,6BAAA,CADA,UAAA,CAJA,aAAA,CAEA,cAAA,CACA,aAAA,CAGA,2CAAA,CAAA,mCAAA,CAEA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CARA,aT2uBJ,CS/tBE,sBACE,cTiuBJ,CS9tBI,2BACE,2CTguBN,CS1tBI,kEAEE,uDAAA,CADA,+BT6tBN,CU/xBE,8BACE,YVkyBJ,CWvyBA,mBACE,GACE,SAAA,CACA,0BX0yBF,CWvyBA,GACE,SAAA,CACA,uBXyyBF,CACF,CWryBA,mBACE,GACE,SXuyBF,CWpyBA,GACE,SXsyBF,CACF,CW3xBE,qBASE,2BAAA,CAFA,mCAAA,CAAA,2BAAA,CADA,gCAAA,CADA,WAAA,CAGA,SAAA,CAPA,cAAA,CACA,KAAA,CAEA,UAAA,CADA,SXmyBJ,CWzxBE,mBAcE,mDAAA,CANA,2CAAA,CACA,QAAA,CACA,mBAAA,CARA,QAAA,CASA,gEACE,CAPF,eAAA,CAEA,aAAA,CADA,SAAA,CALA,cAAA,CAGA,UAAA,CADA,SXoyBJ,CWrxBE,kBACE,aXuxBJ,CWnxBE,sBACE,YAAA,CACA,YXqxBJ,CWlxBI,oCACE,aXoxBN,CW/wBE,sBACE,mBXixBJ,CW9wBI,6CACE,cXgxBN,CK1qBI,0CMvGA,6CAKI,aAAA,CAEA,gBAAA,CACA,iBAAA,CAFA,UXkxBN,CACF,CW3wBE,kBACE,cX6wBJ,CY92BA,YACE,WAAA,CAIA,WZ82BF,CY32BE,mBAEE,qBAAA,CADA,iBZ82BJ,CKjtBI,sCOtJE,4EACE,kBZ02BN,CYt2BI,0JACE,mBZw2BN,CYz2BI,8EACE,kBZw2BN,CACF,CYn2BI,0BAGE,UAAA,CAFA,aAAA,CACA,YZs2BN,CYj2BI,+BACE,eZm2BN,CY71BE,8BACE,WZk2BJ,CYn2BE,8BACE,UZk2BJ,CYn2BE,8BAIE,iBZ+1BJ,CYn2BE,8BAIE,kBZ+1BJ,CYn2BE,oBAGE,cAAA,CADA,SZi2BJ,CY51BI,aAPF,oBAQI,YZ+1BJ,CACF,CY51BI,gCACE,yCZ81BN,CY11BI,wBACE,cAAA,CACA,kBZ41BN,CYz1BM,kCACE,oBZ21BR,Ca55BA,qBAEE,Wb06BF,Ca56BA,qBAEE,Ub06BF,Ca56BA,WAQE,2CAAA,CACA,mBAAA,CANA,YAAA,CAOA,8BAAA,CALA,iBAAA,CAMA,SAAA,CALA,mBAAA,CACA,mBAAA,CANA,cAAA,CAcA,0BAAA,CAHA,wCACE,CATF,Sbw6BF,Ca15BE,aAlBF,WAmBI,Yb65BF,CACF,Ca15BE,mBAEE,SAAA,CADA,mBAAA,CAKA,uBAAA,CAHA,kEb65BJ,Cat5BE,kBAEE,gCAAA,CADA,eby5BJ,Cc37BA,aACE,gBAAA,CACA,iBd87BF,Cc37BE,sBAGE,WAAA,CADA,QAAA,CADA,Sd+7BJ,Ccz7BE,oBAEE,eAAA,CADA,ed47BJ,Ccv7BE,oBACE,iBdy7BJ,Ccr7BE,mBAEE,YAAA,CACA,cAAA,CACA,6BAAA,CAHA,iBd07BJ,Ccp7BI,iDACE,yCds7BN,Ccl7BI,6BACE,iBdo7BN,Cc/6BE,mBAGE,uCAAA,CACA,cAAA,CAHA,aAAA,CACA,cAAA,CAGA,sBdi7BJ,Cc96BI,gDACE,+Bdg7BN,Cc56BI,4BACE,0CAAA,CACA,mBd86BN,Ccz6BE,mBAEE,SAAA,CADA,iBAAA,CAKA,2BAAA,CAHA,8Dd46BJ,Cct6BI,qBAEE,aAAA,CADA,edy6BN,Ccp6BI,6BACE,SAAA,CACA,uBds6BN,Ccj6BE,aAnFF,aAoFI,Ydo6BF,CACF,Cez/BA,WAEE,0CAAA,CADA,+Bf6/BF,Cez/BE,aALF,WAMI,Yf4/BF,CACF,Cez/BE,kBACE,6BAAA,CAEA,aAAA,CADA,af4/BJ,Cex/BI,gCACE,Yf0/BN,Cer/BE,iBAOE,eAAA,CANA,YAAA,CAKA,cAAA,CAGA,mBAAA,CAAA,eAAA,CADA,cAAA,CAGA,uCAAA,CADA,eAAA,CAEA,uBfm/BJ,Ceh/BI,8CACE,Ufk/BN,Ce9+BI,+BACE,oBfg/BN,CKl2BI,0CUvIE,uBACE,af4+BN,Cez+BM,yCACE,Yf2+BR,CACF,Cet+BI,iCACE,gBfy+BN,Ce1+BI,iCACE,iBfy+BN,Ce1+BI,uBAEE,gBfw+BN,Cer+BM,iCACE,efu+BR,Cej+BE,kBACE,WAAA,CAIA,eAAA,CADA,mBAAA,CAFA,6BAAA,CACA,cAAA,CAGA,kBfm+BJ,Ce/9BE,mBAEE,YAAA,CADA,afk+BJ,Ce79BE,sBACE,gBAAA,CACA,Uf+9BJ,Ce19BA,gBACE,gDf69BF,Ce19BE,uBACE,YAAA,CACA,cAAA,CACA,6BAAA,CACA,af49BJ,Cex9BE,kCACE,sCf09BJ,Cev9BI,gFACE,+Bfy9BN,Cej9BA,cAKE,wCAAA,CADA,gBAAA,CADA,iBAAA,CADA,eAAA,CADA,Ufw9BF,CK56BI,mCU7CJ,cASI,Ufo9BF,CACF,Ceh9BE,yBACE,sCfk9BJ,Ce38BA,WACE,mBAAA,CACA,SAAA,CAEA,cAAA,CADA,qBf+8BF,CK37BI,mCUvBJ,WAQI,ef88BF,CACF,Ce38BE,iBACE,oBAAA,CAEA,aAAA,CACA,iBAAA,CAFA,Yf+8BJ,Ce18BI,wBACE,ef48BN,Cex8BI,qBAGE,iBAAA,CAFA,gBAAA,CACA,mBf28BN,CgBjnCE,uBAME,kBAAA,CACA,mBAAA,CAHA,gCAAA,CACA,cAAA,CAJA,oBAAA,CAEA,eAAA,CADA,kBAAA,CAMA,gEhBonCJ,CgB9mCI,gCAEE,2CAAA,CACA,uCAAA,CAFA,gChBknCN,CgB5mCI,0DAEE,0CAAA,CACA,sCAAA,CAFA,+BhBgnCN,CgBzmCE,gCAKE,4BhB8mCJ,CgBnnCE,gEAME,6BhB6mCJ,CgBnnCE,gCAME,4BhB6mCJ,CgBnnCE,sBAIE,6DAAA,CAGA,8BAAA,CAJA,eAAA,CAFA,aAAA,CACA,eAAA,CAMA,sChB2mCJ,CgBtmCI,wDACE,6CAAA,CACA,8BhBwmCN,CgBpmCI,+BACE,UhBsmCN,CiBzpCA,WAOE,2CAAA,CAGA,0DACE,CALF,gCAAA,CADA,aAAA,CAHA,MAAA,CADA,eAAA,CACA,OAAA,CACA,KAAA,CACA,SjBgqCF,CiBrpCE,aAfF,WAgBI,YjBwpCF,CACF,CiBrpCE,mBAIE,2BAAA,CAHA,iEjBwpCJ,CiBjpCE,mBACE,gEACE,CAEF,kEjBipCJ,CiB3oCE,kBAEE,kBAAA,CADA,YAAA,CAEA,ejB6oCJ,CiBzoCE,mBAKE,kBAAA,CAEA,cAAA,CAHA,YAAA,CAIA,uCAAA,CALA,aAAA,CAFA,iBAAA,CAQA,uBAAA,CAHA,qBAAA,CAJA,SjBkpCJ,CiBxoCI,yBACE,UjB0oCN,CiBtoCI,iCACE,oBjBwoCN,CiBpoCI,uCAEE,uCAAA,CADA,YjBuoCN,CiBloCI,2BAEE,YAAA,CADA,ajBqoCN,CKvhCI,0CY/GA,2BAMI,YjBooCN,CACF,CiBjoCM,8DAIE,iBAAA,CAHA,aAAA,CAEA,aAAA,CADA,UjBqoCR,CKrjCI,mCYzEA,iCAII,YjB8nCN,CACF,CiB3nCM,wCACE,YjB6nCR,CiBznCM,+CACE,oBjB2nCR,CKhkCI,sCYtDA,iCAII,YjBsnCN,CACF,CiBjnCE,kBAEE,YAAA,CACA,cAAA,CAFA,iBAAA,CAIA,8DACE,CAFF,kBjBonCJ,CiB9mCI,oCAGE,SAAA,CADA,mBAAA,CAKA,6BAAA,CAHA,8DACE,CAJF,UjBonCN,CiB3mCM,8CACE,8BjB6mCR,CiBxmCI,8BACE,ejB0mCN,CiBrmCE,4BAGE,gBAAA,CAAA,kBjBymCJ,CiB5mCE,4BAGE,iBAAA,CAAA,iBjBymCJ,CiB5mCE,kBACE,WAAA,CAGA,eAAA,CAFA,aAAA,CAGA,kBjBumCJ,CiBpmCI,4CAGE,SAAA,CADA,mBAAA,CAKA,8BAAA,CAHA,8DACE,CAJF,UjB0mCN,CiBjmCM,sDACE,6BjBmmCR,CiB/lCM,8DAGE,SAAA,CADA,mBAAA,CAKA,uBAAA,CAHA,8DACE,CAJF,SjBqmCR,CiB1lCI,uCAGE,WAAA,CAFA,iBAAA,CACA,UjB6lCN,CiBvlCE,mBACE,YAAA,CACA,aAAA,CACA,cAAA,CAEA,+CACE,CAFF,kBjB0lCJ,CiBplCI,8DACE,WAAA,CACA,SAAA,CACA,oCjBslCN,CiB7kCI,yBACE,QjB+kCN,CiB1kCE,mBACE,YjB4kCJ,CKxoCI,mCY2DF,6BAQI,gBjB4kCJ,CiBplCA,6BAQI,iBjB4kCJ,CiBplCA,mBAKI,aAAA,CAEA,iBAAA,CADA,ajB8kCJ,CACF,CKhpCI,sCY2DF,6BAaI,kBjB4kCJ,CiBzlCA,6BAaI,mBjB4kCJ,CACF,CkB3zCA,SAIE,2CAAA,CADA,gCAAA,CADA,cAAA,CADA,eAAA,CAIA,0BlB8zCF,CkB3zCE,gBAEE,eAAA,CADA,yBAAA,CAEA,8DACE,CAEF,oBlB2zCJ,CKhpCI,0CajLF,gBAWI,oBAAA,CADA,iBlB4zCJ,CACF,CkBxzCI,uCAEE,SAAA,CADA,mBAAA,CAKA,6BAAA,CAHA,4ClB2zCN,CkBpzCI,iCACE,oBlBszCN,CDx1CA,SAGE,uCAAA,CAFA,eAAA,CACA,eC41CF,CDx1CE,eACE,mBAAA,CACA,cAAA,CAGA,eAAA,CADA,QAAA,CADA,SC41CJ,CDt1CE,sCAEE,WAAA,CADA,iBAAA,CAAA,kBCy1CJ,CDp1CE,eACE,+BCs1CJ,CDn1CI,0CACE,+BCq1CN,CD/0CA,UAKE,wBoBaa,CpBZb,oBAAA,CAFA,UAAA,CAHA,oBAAA,CAEA,eAAA,CADA,0BAAA,CAAA,2BCs1CF,CoBx3CA,MACE,uMAAA,CACA,sLAAA,CACA,iNpB23CF,CoBr3CA,QACE,eAAA,CACA,epBw3CF,CoBr3CE,eAKE,kBAAA,CAIA,uCAAA,CANA,YAAA,CAKA,eAAA,CADA,eAAA,CADA,eAAA,CAIA,sBpBo3CJ,CoBj3CI,+BACE,YpBm3CN,CoBh3CM,mCAEE,WAAA,CADA,UpBm3CR,CoB32CQ,sFAME,iBAAA,CALA,aAAA,CAGA,aAAA,CADA,cAAA,CAEA,qBAAA,CAAA,kBAAA,CAHA,UpBi3CV,CoBt2CE,cAGE,eAAA,CADA,QAAA,CADA,SpB02CJ,CoBp2CE,cAGE,sBAAA,CAFA,YAAA,CACA,SAAA,CAKA,iBAAA,CACA,uBAAA,CACA,sBpBm2CJ,CoB/1CI,6BACE,mFpBi2CN,CoB31CI,sBACE,uCpB61CN,CoBt1CM,6EAEE,+BpBw1CR,CoBn1CI,2BACE,iBpBq1CN,CoBn1CM,kCAQE,2CAAA,CAJA,QAAA,CAGA,UAAA,CADA,WAAA,CALA,iBAAA,CAEA,wBAAA,CADA,KAAA,CAGA,YpBw1CR,CoBh1CI,2BAIE,iBpB+0CN,CoB30CI,4CACE,gBpB60CN,CoB90CI,4CACE,iBpB60CN,CoBz0CI,kBAME,iBAAA,CAFA,aAAA,CACA,YAAA,CAFA,iBpB40CN,CoBr0CI,sGACE,+BAAA,CACA,cpBu0CN,CoBn0CI,4BACE,uCAAA,CACA,oBpBq0CN,CoBj0CI,0CACE,YpBm0CN,CoBh0CM,yDAIE,6BAAA,CAHA,aAAA,CAEA,WAAA,CAEA,qCAAA,CAAA,6BAAA,CAHA,UpBq0CR,CoB9zCM,kDACE,YpBg0CR,CoB1zCE,iCACE,YpB4zCJ,CoBzzCI,6CACE,WAAA,CAGA,WpByzCN,CoBpzCE,gBAIE,2CAAA,CAHA,eAAA,CACA,kCAAA,CACA,oCpBuzCJ,CoBnzCE,qBAEE,aAAA,CAGA,WAAA,CAEA,kBpBizCJ,CoB7yCE,cACE,apB+yCJ,CoB3yCE,gBACE,YpB6yCJ,CK/zCI,0CeyBA,0CASE,2CAAA,CAHA,YAAA,CACA,qBAAA,CACA,WAAA,CALA,MAAA,CADA,iBAAA,CACA,OAAA,CACA,KAAA,CACA,SpB4yCJ,CoBjyCI,iCAGE,4BAAA,CACA,eAAA,CAHA,eAAA,CACA,YpBqyCN,CoB/xCI,+DACE,eAAA,CACA,epBiyCN,CoB7xCI,gCASE,qDAAA,CAHA,uCAAA,CAEA,cAAA,CAHA,kBAAA,CAFA,iBAAA,CACA,wBAAA,CAHA,iBAAA,CAMA,kBpBgyCN,CoB3xCM,wDAEE,UpBkyCR,CoBpyCM,wDAEE,WpBkyCR,CoBpyCM,8CAIE,aAAA,CAEA,aAAA,CACA,YAAA,CANA,iBAAA,CAEA,SAAA,CAEA,YpB+xCR,CoB1xCQ,oDAKE,6BAAA,CADA,UAAA,CAHA,aAAA,CAEA,WAAA,CAGA,2CAAA,CAAA,mCAAA,CAEA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CAPA,UpBmyCV,CoBvxCM,8CAIE,2CAAA,CACA,gEACE,CALF,eAAA,CAEA,4BAAA,CADA,kBpB4xCR,CoBrxCQ,2DACE,YpBuxCV,CoBlxCM,8CAGE,2CAAA,CADA,gCAAA,CADA,epBsxCR,CoBhxCM,yCAIE,aAAA,CAFA,UAAA,CAIA,YAAA,CADA,aAAA,CAJA,iBAAA,CACA,WAAA,CACA,SpBqxCR,CoB7wCI,+BACE,MpB+wCN,CoB3wCI,+BACE,4DpB6wCN,CoB1wCM,qDACE,+BpB4wCR,CoBzwCQ,sHACE,+BpB2wCV,CoBrwCI,+BAEE,YAAA,CADA,mBpBwwCN,CoBpwCM,mCACE,epBswCR,CoBlwCM,6CACE,SpBowCR,CoBhwCM,uDAGE,mBpBmwCR,CoBtwCM,uDAGE,kBpBmwCR,CoBtwCM,6CAIE,gBAAA,CAFA,aAAA,CADA,YpBqwCR,CoB/vCQ,mDAKE,6BAAA,CADA,UAAA,CAHA,aAAA,CAEA,WAAA,CAGA,2CAAA,CAAA,mCAAA,CAEA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CAPA,UpBwwCV,CoBxvCM,+CACE,mBpB0vCR,CoBlvCM,4CAEE,4BAAA,CADA,epBqvCR,CoBjvCQ,oEACE,mBpBmvCV,CoBpvCQ,oEACE,oBpBmvCV,CoB/uCQ,4EACE,iBpBivCV,CoBlvCQ,4EACE,kBpBivCV,CoB7uCQ,oFACE,mBpB+uCV,CoBhvCQ,oFACE,oBpB+uCV,CoB3uCQ,4FACE,mBpB6uCV,CoB9uCQ,4FACE,oBpB6uCV,CoBtuCE,mBACE,4BpBwuCJ,CoBpuCE,wBACE,YAAA,CACA,SAAA,CAIA,0BAAA,CAHA,oEpBuuCJ,CoBjuCI,kCACE,2BpBmuCN,CoB9tCE,gCACE,SAAA,CAIA,uBAAA,CAHA,qEpBiuCJ,CoB3tCI,8CAEE,0BpB4tCN,CoBttCE,oCACE,kBpBwtCJ,CoBptCI,wCACE,YpBstCN,CACF,CK99CI,0CeiRA,wBACE,sBpBgtCJ,CoB5sCE,0CACE,YpB8sCJ,CoB3sCI,yDACE,UpB6sCN,CoBzsCI,wDACE,YpB2sCN,CoBvsCI,kDACE,YpBysCN,CoBpsCE,gBAIE,iDAAA,CADA,gCAAA,CAFA,aAAA,CACA,epBwsCJ,CACF,CK9hDM,+De+VF,6CACE,YpBksCJ,CoB/rCI,4DACE,UpBisCN,CoB7rCI,2DACE,YpB+rCN,CoB3rCI,qDACE,YpB6rCN,CACF,CKthDI,mCe7JJ,QA4fI,oBpB2rCF,CoBxrCE,qBACE,sBpB0rCJ,CoBnrCI,kCAME,qCAAA,CACA,qDAAA,CANA,eAAA,CACA,KAAA,CAGA,SpBqrCN,CoBhrCM,6CACE,uBpBkrCR,CoB9qCM,gDACE,YpBgrCR,CoB3qCI,2CACE,kBpB8qCN,CoB/qCI,2CACE,mBpB8qCN,CoB/qCI,iCAEE,oBpB6qCN,CoBtqCI,yDACE,kBpBwqCN,CoBzqCI,yDACE,iBpBwqCN,CACF,CKljDI,sCe7JJ,QA6iBI,oBAAA,CACA,oDpBsqCF,CoBhqCI,gCAME,qCAAA,CACA,qDAAA,CANA,eAAA,CACA,KAAA,CAGA,SpBkqCN,CoB7pCM,8CACE,uBpB+pCR,CoB3pCM,8CACE,YpB6pCR,CoBxpCI,yCACE,kBpB2pCN,CoB5pCI,yCACE,mBpB2pCN,CoB5pCI,+BAEE,oBpB0pCN,CoBnpCI,uDACE,kBpBqpCN,CoBtpCI,uDACE,iBpBqpCN,CoBhpCE,wBACE,YAAA,CAGA,oCAAA,CAEA,SAAA,CACA,6FACE,CAHF,mBpBkpCJ,CoB1oCI,sCACE,epB4oCN,CoBvoCE,iFACE,oCAAA,CAEA,SAAA,CACA,4FACE,CAHF,kBpB2oCJ,CoB/nCI,6GACE,gBpBioCN,CoB5nCE,iDACE,epB8nCJ,CoB1nCE,6CACE,YpB4nCJ,CoBxnCE,uBACE,aAAA,CACA,epB0nCJ,CoBvnCI,kCACE,epBynCN,CoBrnCI,qCACE,epBunCN,CoBpnCM,0CACE,uCpBsnCR,CoBlnCM,6DACE,mBpBonCR,CoBhnCM,yFAEE,YpBknCR,CoB7mCI,yCAEE,kBpBinCN,CoBnnCI,yCAEE,mBpBinCN,CoBnnCI,+BACE,aAAA,CAGA,SAAA,CADA,kBpBgnCN,CoB5mCM,2DACE,SpB8mCR,CoBxmCE,cAGE,kBAAA,CADA,YAAA,CAEA,gCAAA,CAHA,WpB6mCJ,CoBvmCI,oBACE,uDpBymCN,CoBrmCI,oBAME,6BAAA,CACA,kBAAA,CAFA,UAAA,CAJA,oBAAA,CAEA,WAAA,CAKA,2CAAA,CAAA,mCAAA,CAEA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CACA,yBAAA,CARA,qBAAA,CAFA,UpBinCN,CoBpmCM,8BACE,wBpBsmCR,CoBlmCM,kKAEE,uBpBmmCR,CoBrlCI,2EACE,YpB0lCN,CoBvlCM,oDACE,apBylCR,CoBtlCQ,kEAKE,qCAAA,CACA,qDAAA,CAFA,YAAA,CAHA,eAAA,CACA,KAAA,CACA,sCpB2lCV,CoBrlCU,0FACE,mBpBulCZ,CoBllCQ,0EACE,QpBolCV,CoB/kCM,sFACE,kBpBilCR,CoBllCM,sFACE,mBpBilCR,CoB7kCM,kDACE,uCpB+kCR,CoBzkCI,2CACE,oCAAA,CAEA,SAAA,CADA,kBpB4kCN,CoBnkCI,qFAIE,mDpBskCN,CoB1kCI,qFAIE,oDpBskCN,CoB1kCI,2EACE,aAAA,CACA,oBAAA,CAGA,SAAA,CAFA,kBpBukCN,CoBlkCM,yFAEE,gBAAA,CADA,gBpBqkCR,CoBhkCM,0FACE,YpBkkCR,CACF,CqBr3DA,eAKE,eAAA,CACA,eAAA,CAJA,SrB43DF,CqBr3DE,gCANA,kBAAA,CAFA,YAAA,CAGA,sBrBm4DF,CqB93DE,iBAOE,mBAAA,CAFA,aAAA,CADA,gBAAA,CAEA,iBrBw3DJ,CqBn3DE,wBAEE,qDAAA,CADA,uCrBs3DJ,CqBj3DE,qBACE,6CrBm3DJ,CqB92DI,sDAEE,uDAAA,CADA,+BrBi3DN,CqB72DM,8DACE,+BrB+2DR,CqB12DI,mCACE,uCAAA,CACA,oBrB42DN,CqBx2DI,yBAKE,iBAAA,CADA,yCAAA,CAHA,aAAA,CAEA,eAAA,CADA,YrB62DN,CsB75DE,eAGE,+DAAA,CADA,oBAAA,CADA,qBtBk6DJ,CK7uDI,0CiBtLF,eAOI,YtBg6DJ,CACF,CsB15DM,6BACE,oBtB45DR,CsBt5DE,kBACE,YAAA,CACA,qBAAA,CACA,SAAA,CACA,qBtBw5DJ,CsBj5DI,0BACE,sBtBm5DN,CsBh5DM,gEACE,+BtBk5DR,CsB54DE,gBAEE,uCAAA,CADA,etB+4DJ,CsB14DE,kBACE,oBtB44DJ,CsBz4DI,mCAGE,kBAAA,CAFA,YAAA,CACA,SAAA,CAEA,iBtB24DN,CsBv4DI,oCAIE,kBAAA,CAHA,mBAAA,CACA,kBAAA,CACA,SAAA,CAGA,QAAA,CADA,iBtB04DN,CsBr4DI,0DACE,kBtBu4DN,CsBx4DI,0DACE,iBtBu4DN,CsBn4DI,iDACE,uBAAA,CAEA,YtBo4DN,CsB/3DE,4BACE,YtBi4DJ,CsB13DA,YAGE,kBAAA,CAFA,YAAA,CAIA,eAAA,CAHA,SAAA,CAIA,eAAA,CAFA,UtB+3DF,CsB13DE,yBACE,WtB43DJ,CsBr3DA,kBACE,YtBw3DF,CKhzDI,0CiBzEJ,kBAKI,wBtBw3DF,CACF,CsBr3DE,qCACE,WAAA,CACA,WtBu3DJ,CK50DI,sCiB7CF,+CAMI,kBtBu3DJ,CsB73DA,+CAMI,mBtBu3DJ,CACF,CK9zDI,0CiBpDJ,6BAMI,SAAA,CAFA,eAAA,CACA,UtBo3DF,CsBj3DE,qDACE,gBtBm3DJ,CsBh3DE,gDACE,StBk3DJ,CsB/2DE,4CACE,iBAAA,CAAA,kBtBi3DJ,CsB92DE,2CAEE,WAAA,CADA,ctBi3DJ,CsB72DE,2CACE,mBAAA,CACA,cAAA,CACA,SAAA,CACA,oBAAA,CAAA,iBtB+2DJ,CsB52DE,2CACE,StB82DJ,CsB32DE,qCAEE,WAAA,CACA,eAAA,CAFA,etB+2DJ,CACF,CuB1hEA,MACE,qBAAA,CACA,yBvB6hEF,CuBvhEA,aAME,qCAAA,CADA,cAAA,CAEA,0FACE,CAPF,cAAA,CACA,KAAA,CAaA,mDAAA,CACA,qBAAA,CAJA,wFACE,CATF,UAAA,CADA,SvBiiEF,CwB5iEA,MACE,mfxB+iEF,CwBziEA,WACE,iBxB4iEF,CK94DI,mCmB/JJ,WAKI,exB4iEF,CACF,CwBziEE,kBACE,YxB2iEJ,CwBviEE,oBAEE,SAAA,CADA,SxB0iEJ,CKv4DI,0CmBpKF,8BAOI,YxBkjEJ,CwBzjEA,8BAOI,axBkjEJ,CwBzjEA,oBAaI,2CAAA,CACA,kBAAA,CAJA,WAAA,CACA,eAAA,CACA,mBAAA,CANA,iBAAA,CAEA,SAAA,CAUA,uBAAA,CAHA,4CACE,CAPF,UxBgjEJ,CwBpiEI,+DACE,SAAA,CACA,oCxBsiEN,CACF,CK76DI,mCmBjJF,8BAgCI,MxByiEJ,CwBzkEA,8BAgCI,OxByiEJ,CwBzkEA,oBAqCI,gCAAA,CADA,cAAA,CADA,QAAA,CAJA,cAAA,CAEA,KAAA,CAKA,sDACE,CALF,OxBuiEJ,CwB7hEI,+DAME,YAAA,CACA,SAAA,CACA,4CACE,CARF,UxBkiEN,CACF,CK56DI,0CmBxGA,+DAII,mBxBohEN,CACF,CK19DM,+DmB/DF,+DASI,mBxBohEN,CACF,CK/9DM,+DmB/DF,+DAcI,mBxBohEN,CACF,CwB/gEE,kBAEE,0BxBghEJ,CK97DI,0CmBpFF,4BAOI,MxBwhEJ,CwB/hEA,4BAOI,OxBwhEJ,CwB/hEA,kBAWI,QAAA,CAEA,SAAA,CADA,eAAA,CANA,cAAA,CAEA,KAAA,CAWA,wBAAA,CALA,qGACE,CALF,OAAA,CADA,SxBshEJ,CwBzgEI,4BACE,yBxB2gEN,CwBvgEI,6DAEE,WAAA,CACA,SAAA,CAMA,uBAAA,CALA,sGACE,CAJF,UxB6gEN,CACF,CKz+DI,mCmBjEF,4BA2CI,WxBugEJ,CwBljEA,4BA2CI,UxBugEJ,CwBljEA,kBA6CI,eAAA,CAHA,iBAAA,CAIA,8CAAA,CAFA,axBsgEJ,CACF,CKxgEM,+DmBOF,6DAII,axBigEN,CACF,CKv/DI,sCmBfA,6DASI,axBigEN,CACF,CwB5/DE,iBAIE,2CAAA,CACA,gCAAA,CAFA,aAAA,CAFA,iBAAA,CAKA,2CACE,CALF,SxBkgEJ,CKpgEI,mCmBAF,iBAaI,gCAAA,CACA,mBAAA,CAFA,axB8/DJ,CwBz/DI,uBACE,oCxB2/DN,CACF,CwBv/DI,4DAEE,2CAAA,CACA,6BAAA,CACA,oCAAA,CAHA,gCxB4/DN,CwBp/DE,4BAKE,mBAAA,CAAA,oBxBy/DJ,CwB9/DE,4BAKE,mBAAA,CAAA,oBxBy/DJ,CwB9/DE,kBAQE,sBAAA,CAFA,eAAA,CAFA,WAAA,CAHA,iBAAA,CAMA,sBAAA,CAJA,UAAA,CADA,SxB4/DJ,CwBn/DI,oCACE,0BAAA,CAAA,qBxBq/DN,CwBt/DI,+BACE,qBxBq/DN,CwBj/DI,oCAEE,uCxBk/DN,CwBp/DI,kEAEE,uCxBk/DN,CwB9+DI,6BACE,YxBg/DN,CKphEI,0CmBaF,kBA8BI,eAAA,CADA,aAAA,CADA,UxBi/DJ,CACF,CK9iEI,mCmBgCF,4BAmCI,mBxBi/DJ,CwBphEA,4BAmCI,oBxBi/DJ,CwBphEA,kBAqCI,aAAA,CADA,exBg/DJ,CwB5+DI,oCACE,uCxB8+DN,CwB/+DI,+BACE,uCxB8+DN,CwB1+DI,mCACE,gCxB4+DN,CwBx+DI,6DACE,kBxB0+DN,CwBv+DM,8EACE,uCxBy+DR,CwBr+DM,+EACE,iBxBu+DR,CwBx+DM,0EACE,iBxBu+DR,CACF,CwBj+DE,iBAIE,cAAA,CAHA,oBAAA,CAEA,aAAA,CAEA,kCACE,CAJF,YxBs+DJ,CwB99DI,uBACE,UxBg+DN,CwB59DI,yCAEE,UxBg+DN,CwBl+DI,yCAEE,WxBg+DN,CwBl+DI,+BACE,iBAAA,CAEA,SAAA,CACA,SxB89DN,CwB39DM,6CACE,oBxB69DR,CKpkEI,0CmB+FA,yCAaI,UxB69DN,CwB1+DE,yCAaI,WxB69DN,CwB1+DE,+BAcI,SxB49DN,CwBz9DM,+CACE,YxB29DR,CACF,CKhmEI,mCmBkHA,+BAwBI,mBxB09DN,CwBv9DM,8CACE,YxBy9DR,CACF,CwBn9DE,8BAEE,WxBw9DJ,CwB19DE,8BAEE,UxBw9DJ,CwB19DE,oBAKE,mBAAA,CAJA,iBAAA,CAEA,SAAA,CACA,SxBs9DJ,CK5lEI,0CmBkIF,8BASI,WxBs9DJ,CwB/9DA,8BASI,UxBs9DJ,CwB/9DA,oBAUI,SxBq9DJ,CACF,CwBl9DI,uCACE,iBxBw9DN,CwBz9DI,uCACE,kBxBw9DN,CwBz9DI,6BAEE,uCAAA,CACA,SAAA,CAIA,oBAAA,CAHA,+DxBq9DN,CwB/8DM,iDAEE,uCAAA,CADA,YxBk9DR,CwB78DM,gGAGE,SAAA,CADA,mBAAA,CAEA,kBxB88DR,CwB38DQ,sGACE,UxB68DV,CwBt8DE,8BAOE,mBAAA,CAAA,oBxB68DJ,CwBp9DE,8BAOE,mBAAA,CAAA,oBxB68DJ,CwBp9DE,oBAIE,kBAAA,CAKA,yCAAA,CANA,YAAA,CAKA,eAAA,CAFA,WAAA,CAKA,SAAA,CAVA,iBAAA,CACA,KAAA,CAUA,uBAAA,CAFA,kBAAA,CALA,UxB+8DJ,CKtpEI,mCmBkMF,8BAgBI,mBxBy8DJ,CwBz9DA,8BAgBI,oBxBy8DJ,CwBz9DA,oBAiBI,exBw8DJ,CACF,CwBr8DI,+DACE,SAAA,CACA,0BxBu8DN,CwBl8DE,6BAKE,+BxBq8DJ,CwB18DE,0DAME,gCxBo8DJ,CwB18DE,6BAME,+BxBo8DJ,CwB18DE,mBAIE,eAAA,CAHA,iBAAA,CAEA,UAAA,CADA,SxBw8DJ,CKrpEI,0CmB2MF,mBAWI,QAAA,CADA,UxBq8DJ,CACF,CK9qEI,mCmB8NF,mBAiBI,SAAA,CADA,UAAA,CAEA,sBxBo8DJ,CwBj8DI,8DACE,8BAAA,CACA,SxBm8DN,CACF,CwB97DE,uBASE,0BAAA,CAFA,2CAAA,CANA,WAAA,CACA,eAAA,CAIA,kBxB+7DJ,CwBz7DI,iEAZF,uBAaI,uBxB47DJ,CACF,CK3tEM,+DmBiRJ,uBAkBI,axB47DJ,CACF,CK1sEI,sCmB2PF,uBAuBI,axB47DJ,CACF,CK/sEI,mCmB2PF,uBA4BI,YAAA,CACA,+DAAA,CACA,oBxB47DJ,CwBz7DI,kEACE,exB27DN,CwBv7DI,6BACE,qDxBy7DN,CwBr7DI,0CAEE,YAAA,CADA,WxBw7DN,CwBn7DI,gDACE,oDxBq7DN,CwBl7DM,sDACE,0CxBo7DR,CACF,CwB76DA,kBACE,gCAAA,CACA,qBxBg7DF,CwB76DE,wBAME,qDAAA,CAFA,uCAAA,CAFA,gBAAA,CACA,kBAAA,CAFA,eAAA,CAIA,uBxBg7DJ,CKnvEI,mCmB8TF,kCAUI,mBxB+6DJ,CwBz7DA,kCAUI,oBxB+6DJ,CACF,CwB36DE,wBAGE,eAAA,CADA,QAAA,CADA,SAAA,CAIA,wBAAA,CAAA,qBAAA,CAAA,gBxB46DJ,CwBx6DE,wBACE,yDxB06DJ,CwBv6DI,oCACE,exBy6DN,CwBp6DE,wBACE,aAAA,CAEA,YAAA,CADA,uBAAA,CAEA,gCxBs6DJ,CwBn6DI,4DACE,uDxBq6DN,CwBj6DI,gDACE,mBxBm6DN,CwB95DE,gCAKE,cAAA,CADA,aAAA,CAGA,YAAA,CANA,eAAA,CAKA,uBAAA,CAJA,KAAA,CACA,SxBo6DJ,CwB75DI,wCACE,YxB+5DN,CwB15DI,wDACE,YxB45DN,CwBx5DI,oCAGE,+BAAA,CADA,gBAAA,CADA,mBAAA,CAGA,2CxB05DN,CKryEI,mCmBuYA,8CAUI,mBxBw5DN,CwBl6DE,8CAUI,oBxBw5DN,CACF,CwBp5DI,oFAEE,uDAAA,CADA,+BxBu5DN,CwBj5DE,sCACE,2CxBm5DJ,CwB94DE,2BAGE,eAAA,CADA,eAAA,CADA,iBxBk5DJ,CKtzEI,mCmBmaF,qCAOI,mBxBg5DJ,CwBv5DA,qCAOI,oBxBg5DJ,CACF,CwB54DE,kCAEE,MxBk5DJ,CwBp5DE,kCAEE,OxBk5DJ,CwBp5DE,wBAME,uCAAA,CAFA,aAAA,CACA,YAAA,CAJA,iBAAA,CAEA,YxBi5DJ,CKhzEI,0CmB4ZF,wBAUI,YxB84DJ,CACF,CwB34DI,8BAKE,6BAAA,CADA,UAAA,CAHA,oBAAA,CAEA,WAAA,CAGA,+CAAA,CAAA,uCAAA,CAEA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CAPA,UxBo5DN,CwB14DM,wCACE,oBxB44DR,CwBt4DE,8BAGE,uCAAA,CAFA,gBAAA,CACA,exBy4DJ,CwBr4DI,iCAKE,gCAAA,CAHA,eAAA,CACA,eAAA,CACA,eAAA,CAHA,exB24DN,CwBp4DM,sCACE,oBxBs4DR,CwBj4DI,iCAKE,gCAAA,CAHA,gBAAA,CACA,eAAA,CACA,eAAA,CAHA,axBu4DN,CwBh4DM,sCACE,oBxBk4DR,CwB53DE,yBAKE,gCAAA,CAJA,aAAA,CAEA,gBAAA,CACA,iBAAA,CAFA,axBi4DJ,CwB13DE,uBAGE,4BAAA,CAFA,+BAAA,CACA,yBxB63DJ,CyBjiFA,WACE,iBAAA,CACA,SzBoiFF,CyBjiFE,kBAOE,2CAAA,CACA,mBAAA,CACA,8BAAA,CAHA,gCAAA,CAHA,QAAA,CAEA,gBAAA,CADA,YAAA,CAMA,SAAA,CATA,iBAAA,CACA,sBAAA,CAaA,mCAAA,CAJA,oEzBoiFJ,CyB7hFI,6EACE,gBAAA,CACA,SAAA,CAKA,+BAAA,CAJA,8EzBgiFN,CyBxhFI,wBAWE,qCAAA,CAAA,8CAAA,CAFA,mCAAA,CAAA,oCAAA,CACA,YAAA,CAFA,UAAA,CAHA,QAAA,CAFA,QAAA,CAIA,kBAAA,CADA,iBAAA,CALA,iBAAA,CACA,KAAA,CAEA,OzBiiFN,CyBrhFE,iBAOE,mBAAA,CAFA,eAAA,CACA,oBAAA,CAHA,QAAA,CAFA,kBAAA,CAGA,aAAA,CAFA,SzB4hFJ,CyBnhFE,iBACE,kBzBqhFJ,CyBjhFE,2BAGE,kBAAA,CAAA,oBzBuhFJ,CyB1hFE,2BAGE,mBAAA,CAAA,mBzBuhFJ,CyB1hFE,iBAIE,cAAA,CAHA,aAAA,CAKA,YAAA,CADA,uBAAA,CAEA,2CACE,CANF,UzBwhFJ,CyB9gFI,8CACE,+BzBghFN,CyB5gFI,uBACE,qDzB8gFN,C0BlmFA,YAIE,qBAAA,CADA,aAAA,CAGA,gBAAA,CALA,eAAA,CACA,UAAA,CAGA,a1BsmFF,C0BlmFE,aATF,YAUI,Y1BqmFF,CACF,CKv7EI,0CqB3KF,+BAKI,a1B0mFJ,C0B/mFA,+BAKI,c1B0mFJ,C0B/mFA,qBAWI,2CAAA,CAHA,aAAA,CAEA,WAAA,CANA,cAAA,CAEA,KAAA,CASA,uBAAA,CAHA,iEACE,CAJF,aAAA,CAFA,S1BwmFJ,C0B7lFI,mEACE,8BAAA,CACA,6B1B+lFN,C0B5lFM,6EACE,8B1B8lFR,C0BzlFI,6CAEE,QAAA,CAAA,MAAA,CACA,QAAA,CACA,eAAA,CAHA,iBAAA,CACA,OAAA,CAGA,qBAAA,CAHA,K1B8lFN,CACF,CKt+EI,sCqBtKJ,YAuDI,Q1BylFF,C0BtlFE,mBACE,W1BwlFJ,C0BplFE,6CACE,U1BslFJ,CACF,C0BllFE,uBACE,YAAA,CACA,O1BolFJ,CKr/EI,mCqBjGF,uBAMI,Q1BolFJ,C0BjlFI,8BACE,W1BmlFN,C0B/kFI,qCACE,a1BilFN,C0B7kFI,+CACE,kB1B+kFN,CACF,C0B1kFE,wBAIE,uBAAA,CAOA,0BAAA,CAVA,cAAA,CACA,eAAA,CACA,+DAAA,CAMA,oB1BykFJ,C0BpkFI,2CAEE,YAAA,CADA,W1BukFN,C0BlkFI,mEACE,qD1BokFN,C0BjkFM,qHACE,oD1BmkFR,C0BhkFQ,iIACE,0C1BkkFV,C0BnjFE,wCAGE,wBACE,qB1BmjFJ,C0B/iFE,6BACE,kC1BijFJ,C0BljFE,6BACE,iC1BijFJ,CACF,CK7gFI,0CqB5BF,YAME,gCAAA,CADA,QAAA,CAEA,SAAA,CANA,cAAA,CACA,KAAA,CAMA,sDACE,CALF,OAAA,CADA,S1BkjFF,C0BviFE,4CAEE,WAAA,CACA,SAAA,CACA,4CACE,CAJF,U1B4iFJ,CACF,C2BztFA,iBACE,GACE,Q3B2tFF,C2BxtFA,GACE,a3B0tFF,CACF,C2BttFA,gBACE,GACE,SAAA,CACA,0B3BwtFF,C2BrtFA,IACE,S3ButFF,C2BptFA,GACE,SAAA,CACA,uB3BstFF,CACF,C2B9sFA,MACE,2eAAA,CACA,+fAAA,CACA,0lBAAA,CACA,kf3BgtFF,C2B1sFA,WAOE,0BAAA,CANA,aAAA,CACA,gBAAA,CACA,eAAA,CAEA,uCAAA,CAGA,uBAAA,CAJA,kB3BgtFF,C2BzsFE,iBACE,U3B2sFJ,C2BvsFE,iBACE,oBAAA,CAEA,aAAA,CACA,qBAAA,CAFA,U3B2sFJ,C2BtsFI,+BACE,iB3BysFN,C2B1sFI,+BACE,kB3BysFN,C2B1sFI,qBAEE,gB3BwsFN,C2BpsFI,kDACE,iB3BusFN,C2BxsFI,kDACE,kB3BusFN,C2BxsFI,kDAEE,iB3BssFN,C2BxsFI,kDAEE,kB3BssFN,C2BjsFE,iCAGE,iB3BssFJ,C2BzsFE,iCAGE,kB3BssFJ,C2BzsFE,uBACE,oBAAA,CACA,6BAAA,CAEA,eAAA,CACA,sBAAA,CACA,qB3BmsFJ,C2B/rFE,kBACE,YAAA,CAMA,gBAAA,CALA,SAAA,CAMA,oBAAA,CAHA,gBAAA,CAIA,WAAA,CAHA,eAAA,CAFA,SAAA,CADA,U3BusFJ,C2B9rFI,iDACE,4B3BgsFN,C2B3rFE,iBACE,eAAA,CACA,sB3B6rFJ,C2B1rFI,gDACE,2B3B4rFN,C2BxrFI,kCAIE,kB3BgsFN,C2BpsFI,kCAIE,iB3BgsFN,C2BpsFI,wBAOE,6BAAA,CADA,UAAA,CALA,oBAAA,CAEA,YAAA,CAMA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CALA,uBAAA,CAHA,W3BksFN,C2BtrFI,iCACE,a3BwrFN,C2BprFI,iCACE,gDAAA,CAAA,wC3BsrFN,C2BlrFI,+BACE,8CAAA,CAAA,sC3BorFN,C2BhrFI,+BACE,8CAAA,CAAA,sC3BkrFN,C2B9qFI,sCACE,qDAAA,CAAA,6C3BgrFN,C2B1qFA,gBACE,Y3B6qFF,C2B1qFE,gCAIE,kB3B8qFJ,C2BlrFE,gCAIE,iB3B8qFJ,C2BlrFE,sBAGE,kBAAA,CAGA,uCAAA,CALA,mBAAA,CAIA,gBAAA,CAHA,S3BgrFJ,C2BzqFI,+BACE,aAAA,CACA,oB3B2qFN,C2BvqFI,2CACE,U3B0qFN,C2B3qFI,2CACE,W3B0qFN,C2B3qFI,iCAEE,kB3ByqFN,C2BrqFI,0BACE,W3BuqFN,C4B10FE,iBAME,kDAAA,CADA,UAAA,CAJA,oBAAA,CAEA,cAAA,CAUA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CAVA,0BAAA,CAFA,a5Bw3FJ,C4Bx2FE,uBACE,6B5B02FJ,C6Bj5FA,SASE,2CAAA,CADA,gCAAA,CAJA,aAAA,CAGA,eAAA,CADA,aAAA,CADA,UAAA,CAFA,S7Bw5FF,C6B/4FE,aAZF,SAaI,Y7Bk5FF,CACF,CKvuFI,0CwBzLJ,SAkBI,Y7Bk5FF,CACF,C6B/4FE,iBACE,mB7Bi5FJ,C6B74FE,yBAIE,iB7Bo5FJ,C6Bx5FE,yBAIE,kB7Bo5FJ,C6Bx5FE,eAQE,eAAA,CAPA,YAAA,CAMA,eAAA,CAJA,QAAA,CAEA,aAAA,CAHA,SAAA,CAWA,oBAAA,CAPA,kB7Bk5FJ,C6Bx4FI,kCACE,Y7B04FN,C6Br4FE,eACE,aAAA,CACA,kBAAA,CAAA,mB7Bu4FJ,C6Bp4FI,sCACE,aAAA,CACA,S7Bs4FN,C6Bh4FE,eAOE,0BAAA,CANA,YAAA,CAEA,eAAA,CADA,gBAAA,CAMA,UAAA,CAJA,uCAAA,CACA,oBAAA,CAIA,8D7Bi4FJ,C6B53FI,0CACE,aAAA,CACA,S7B83FN,C6B13FI,6BAEE,kB7B63FN,C6B/3FI,6BAEE,iB7B63FN,C6B/3FI,mBAGE,iBAAA,CAFA,Y7B83FN,C6Bv3FM,2CACE,qB7By3FR,C6B13FM,2CACE,qB7B43FR,C6B73FM,2CACE,qB7B+3FR,C6Bh4FM,2CACE,qB7Bk4FR,C6Bn4FM,2CACE,oB7Bq4FR,C6Bt4FM,2CACE,qB7Bw4FR,C6Bz4FM,2CACE,qB7B24FR,C6B54FM,2CACE,qB7B84FR,C6B/4FM,4CACE,qB7Bi5FR,C6Bl5FM,4CACE,oB7Bo5FR,C6Br5FM,4CACE,qB7Bu5FR,C6Bx5FM,4CACE,qB7B05FR,C6B35FM,4CACE,qB7B65FR,C6B95FM,4CACE,qB7Bg6FR,C6Bj6FM,4CACE,oB7Bm6FR,C6B75FI,gCACE,SAAA,CAIA,yBAAA,CAHA,wC7Bg6FN,C8BngGA,MACE,mS9BsgGF,C8B7/FE,mCACE,mBAAA,CACA,cAAA,CACA,QAAA,CAEA,mBAAA,CADA,kB9BigGJ,C8B5/FE,oBAGE,kBAAA,CAOA,+CAAA,CACA,oBAAA,CAVA,mBAAA,CAIA,gBAAA,CACA,0BAAA,CACA,eAAA,CALA,QAAA,CAOA,qBAAA,CADA,eAAA,CAJA,wB9BqgGJ,C8B3/FI,0BAGE,uCAAA,CAFA,aAAA,CACA,YAAA,CAEA,6C9B6/FN,C8Bx/FM,gEAEE,0CAAA,CADA,+B9B2/FR,C8Br/FI,yBACE,uB9Bu/FN,C8B/+FI,gCAME,oDAAA,CADA,UAAA,CAJA,oBAAA,CAEA,YAAA,CAIA,qCAAA,CAAA,6BAAA,CAEA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CACA,iCAAA,CAPA,0BAAA,CAFA,W9B0/FN,C8B7+FI,wFACE,0C9B++FN,C+BzjGA,iBACE,GACE,oB/B4jGF,C+BzjGA,IACE,kB/B2jGF,C+BxjGA,GACE,oB/B0jGF,CACF,C+BljGA,MACE,yNAAA,CACA,sP/BqjGF,C+B9iGA,YA6BE,0BAAA,CAVA,2CAAA,CACA,mBAAA,CACA,8BAAA,CAHA,gCAAA,CADA,sCAAA,CAdA,+IACE,CAYF,8BAAA,CAMA,SAAA,CArBA,iBAAA,CACA,uBAAA,CAyBA,4BAAA,CAJA,uDACE,CATF,6BAAA,CADA,S/BkjGF,C+BhiGE,oBAEE,SAAA,CAKA,uBAAA,CAJA,2EACE,CAHF,S/BqiGJ,C+B3hGE,oBAEE,eAAA,CACA,wBAAA,CAAA,qBAAA,CAAA,gBAAA,CAFA,U/B+hGJ,C+B1hGI,6CACE,qC/B4hGN,C+BxhGI,uCAEE,eAAA,CADA,mB/B2hGN,C+BrhGI,6BACE,Y/BuhGN,C+BlhGE,8CACE,sC/BohGJ,C+BhhGE,mBAEE,gBAAA,CADA,a/BmhGJ,C+B/gGI,2CACE,Y/BihGN,C+B7gGI,0CACE,e/B+gGN,C+BvgGA,eACE,iBAAA,CACA,eAAA,CAIA,YAAA,CAHA,kBAAA,CAEA,0BAAA,CADA,kB/B4gGF,C+BvgGE,yBACE,a/BygGJ,C+BrgGE,oBACE,sCAAA,CACA,iB/BugGJ,C+BngGE,6BACE,oBAAA,CAGA,gB/BmgGJ,C+B//FE,sBAYE,mBAAA,CANA,cAAA,CAHA,oBAAA,CACA,gBAAA,CAAA,iBAAA,CAIA,YAAA,CAGA,eAAA,CAVA,iBAAA,CAMA,wBAAA,CAAA,qBAAA,CAAA,gBAAA,CAFA,uBAAA,CAHA,S/BygGJ,C+B3/FI,qCACE,uB/B6/FN,C+Bz/FI,cArBF,sBAsBI,W/B4/FJ,C+Bz/FI,wCACE,2B/B2/FN,C+Bv/FI,6BAOE,qCAAA,CACA,+CAAA,CAAA,uC/B4/FN,C+Bl/FI,yDAZE,UAAA,CADA,YAAA,CAKA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CAVA,iBAAA,CACA,SAAA,CAEA,WAAA,CADA,U/BghGN,C+BjgGI,4BAOE,oDAAA,CACA,4CAAA,CAAA,oCAAA,CAQA,uBAAA,CAJA,+C/Bq/FN,C+B9+FM,gDACE,uB/Bg/FR,C+B5+FM,mFACE,0C/B8+FR,CACF,C+Bz+FI,0CAGE,2BAAA,CADA,uBAAA,CADA,S/B6+FN,C+Bv+FI,8CACE,oB/By+FN,C+Bt+FM,aAJF,8CASI,8CAAA,CACA,iBAAA,CAHA,gCAAA,CADA,eAAA,CADA,cAAA,CAGA,kB/B2+FN,C+Bt+FM,oDACE,mC/Bw+FR,CACF,C+B59FE,gCAEE,iBAAA,CADA,e/Bg+FJ,C+B59FI,mCACE,iB/B89FN,C+B39FM,oDAEE,a/B0+FR,C+B5+FM,oDAEE,c/B0+FR,C+B5+FM,0CAcE,8CAAA,CACA,iBAAA,CALA,gCAAA,CAEA,oBAAA,CACA,qBAAA,CANA,iBAAA,CACA,eAAA,CAHA,UAAA,CAIA,gBAAA,CALA,aAAA,CAEA,cAAA,CALA,iBAAA,CAUA,iBAAA,CARA,S/By+FR,CgCzvGA,MACE,wBAAA,CACA,wBhC4vGF,CgCtvGA,aA+BE,0BAAA,CAjBA,gCAAA,CADA,sCAAA,CAGA,SAAA,CADA,mBAAA,CAdA,iBAAA,CAGA,wDACE,CAgBF,4BAAA,CAGA,uEACE,CARF,uDACE,CANF,UAAA,CADA,ShC0vGF,CgCnuGE,oBAuBE,oDAAA,CAAA,qDAAA,CADA,UAAA,CADA,aAAA,CAfA,gJACE,CANF,iBAAA,CAmBA,ShCutGJ,CgChtGE,yBAGE,kEAAA,CAFA,gDAAA,CACA,6ChCmtGJ,CgC9sGE,4BAGE,qEAAA,CADA,8CAAA,CADA,6ChCktGJ,CgC5sGE,qBAEE,SAAA,CAKA,uBAAA,CAJA,wEACE,CAHF,ShCitGJ,CgCvsGE,oBAqBE,uBAAA,CAEA,2CAAA,CACA,mBAAA,CACA,8BAAA,CAnBA,0FACE,CAaF,eAAA,CADA,8BAAA,CAlBA,iBAAA,CAqBA,oBhC4rGJ,CgCtrGI,uCAEE,YAAA,CADA,WhCyrGN,CgCprGI,6CACE,oDhCsrGN,CgCnrGM,mDACE,0ChCqrGR,CgC7qGI,mCAwBE,eAAA,CACA,eAAA,CAxBA,oIACE,CAgBF,sCACE,CAIF,mBAAA,CAKA,wBAAA,CAAA,qBAAA,CAAA,gBAAA,CAbA,sBAAA,CAAA,iBhCuqGN,CgCtpGI,4CACE,YhCwpGN,CgCppGI,2CACE,ehCspGN,CiCz0GA,kBAME,ejCq1GF,CiC31GA,kBAME,gBjCq1GF,CiC31GA,QAUE,2CAAA,CACA,oBAAA,CAEA,8BAAA,CALA,uCAAA,CACA,cAAA,CALA,aAAA,CAGA,eAAA,CAKA,YAAA,CAPA,mBAAA,CAJA,cAAA,CACA,UAAA,CAiBA,yBAAA,CALA,mGACE,CAZF,SjCw1GF,CiCr0GE,aAtBF,QAuBI,YjCw0GF,CACF,CiCr0GE,kBACE,wBjCu0GJ,CiCn0GE,gBAEE,SAAA,CADA,mBAAA,CAGA,+BAAA,CADA,uBjCs0GJ,CiCl0GI,0BACE,8BjCo0GN,CiC/zGE,4BAEE,0CAAA,CADA,+BjCk0GJ,CiC7zGE,YACE,oBAAA,CACA,oBjC+zGJ,CkCp3GA,oBACE,GACE,mBlCu3GF,CACF,CkC/2GA,MACE,wflCi3GF,CkC32GA,YACE,aAAA,CAEA,eAAA,CADA,alC+2GF,CkC32GE,+BAOE,kBAAA,CAAA,kBlC42GJ,CkCn3GE,+BAOE,iBAAA,CAAA,mBlC42GJ,CkCn3GE,qBAQE,aAAA,CACA,cAAA,CACA,YAAA,CATA,iBAAA,CAKA,UlC62GJ,CkCt2GI,qCAIE,iBlC82GN,CkCl3GI,qCAIE,kBlC82GN,CkCl3GI,2BAME,6BAAA,CADA,UAAA,CAJA,oBAAA,CAEA,YAAA,CAIA,yCAAA,CAAA,iCAAA,CAEA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CARA,WlCg3GN,CkCn2GE,mBACE,iBAAA,CACA,UlCq2GJ,CkCj2GE,kBAWE,2CAAA,CACA,mBAAA,CACA,8BAAA,CALA,gCAAA,CACA,oBAAA,CAHA,kBAAA,CAFA,YAAA,CAUA,SAAA,CAPA,aAAA,CAFA,SAAA,CAJA,iBAAA,CASA,4BAAA,CARA,UAAA,CAaA,+CACE,CAbF,SlC+2GJ,CkC91GI,+EACE,gBAAA,CACA,SAAA,CACA,sClCg2GN,CkC11GI,qCAEE,oCACE,gClC21GN,CkCv1GI,2CACE,clCy1GN,CACF,CkCp1GE,kBACE,kBlCs1GJ,CkCl1GE,4BAGE,kBAAA,CAAA,oBlCy1GJ,CkC51GE,4BAGE,mBAAA,CAAA,mBlCy1GJ,CkC51GE,kBAKE,cAAA,CAJA,aAAA,CAMA,YAAA,CADA,uBAAA,CAEA,2CACE,CALF,kBAAA,CAFA,UlC01GJ,CkC/0GI,gDACE,+BlCi1GN,CkC70GI,wBACE,qDlC+0GN,CmCr7GA,MAEI,6VAAA,CAAA,uWAAA,CAAA,qPAAA,CAAA,2xBAAA,CAAA,qMAAA,CAAA,+aAAA,CAAA,2LAAA,CAAA,yPAAA,CAAA,2TAAA,CAAA,oaAAA,CAAA,2SAAA,CAAA,2LnC88GJ,CmCl8GE,4CAME,8CAAA,CACA,4BAAA,CACA,mBAAA,CACA,8BAAA,CAJA,mCAAA,CAJA,iBAAA,CAGA,gBAAA,CADA,iBAAA,CADA,eAAA,CASA,uBAAA,CADA,2BnCs8GJ,CmCl8GI,aAdF,4CAeI,enCq8GJ,CACF,CmCl8GI,sEACE,0CnCo8GN,CmC/7GI,gDACE,qBnCi8GN,CmC77GI,gIAEE,iBAAA,CADA,cnCg8GN,CmC37GI,4FACE,iBnC67GN,CmCz7GI,kFACE,enC27GN,CmCv7GI,0FACE,YnCy7GN,CmCr7GI,8EACE,mBnCu7GN,CmCl7GE,sEAGE,iBAAA,CAAA,mBnC47GJ,CmC/7GE,sEAGE,kBAAA,CAAA,kBnC47GJ,CmC/7GE,sEASE,uBnCs7GJ,CmC/7GE,sEASE,wBnCs7GJ,CmC/7GE,sEAUE,4BnCq7GJ,CmC/7GE,4IAWE,6BnCo7GJ,CmC/7GE,sEAWE,4BnCo7GJ,CmC/7GE,kDAOE,oCAAA,CACA,WAAA,CAFA,eAAA,CADA,eAAA,CAHA,oBAAA,CAAA,iBAAA,CADA,iBnC87GJ,CmCj7GI,kFACE,enCm7GN,CmC/6GI,oFAEE,UnC07GN,CmC57GI,oFAEE,WnC07GN,CmC57GI,gEAOE,wBhBiIU,CgBlIV,UAAA,CADA,WAAA,CAGA,kDAAA,CAAA,0CAAA,CAEA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CAVA,iBAAA,CAEA,UAAA,CACA,UnCw7GN,CmC76GI,4DACE,4DnC+6GN,CmCj6GE,sDACE,oBnCo6GJ,CmCj6GI,gFACE,0CnCm6GN,CmC95GE,8DACE,oCnCi6GJ,CmC95GI,4EACE,wBAlBG,CAmBH,kDAAA,CAAA,0CnCg6GN,CmC55GI,0EACE,anC85GN,CmCn7GE,8DACE,oBnCs7GJ,CmCn7GI,wFACE,yCnCq7GN,CmCh7GE,sEACE,mCnCm7GJ,CmCh7GI,oFACE,wBAlBG,CAmBH,sDAAA,CAAA,8CnCk7GN,CmC96GI,kFACE,anCg7GN,CmCr8GE,sDACE,oBnCw8GJ,CmCr8GI,gFACE,yCnCu8GN,CmCl8GE,8DACE,mCnCq8GJ,CmCl8GI,4EACE,wBAlBG,CAmBH,kDAAA,CAAA,0CnCo8GN,CmCh8GI,0EACE,anCk8GN,CmCv9GE,oDACE,oBnC09GJ,CmCv9GI,8EACE,yCnCy9GN,CmCp9GE,4DACE,mCnCu9GJ,CmCp9GI,0EACE,wBAlBG,CAmBH,iDAAA,CAAA,yCnCs9GN,CmCl9GI,wEACE,anCo9GN,CmCz+GE,4DACE,oBnC4+GJ,CmCz+GI,sFACE,wCnC2+GN,CmCt+GE,oEACE,kCnCy+GJ,CmCt+GI,kFACE,wBAlBG,CAmBH,qDAAA,CAAA,6CnCw+GN,CmCp+GI,gFACE,anCs+GN,CmC3/GE,8DACE,oBnC8/GJ,CmC3/GI,wFACE,0CnC6/GN,CmCx/GE,sEACE,oCnC2/GJ,CmCx/GI,oFACE,wBAlBG,CAmBH,sDAAA,CAAA,8CnC0/GN,CmCt/GI,kFACE,anCw/GN,CmC7gHE,4DACE,oBnCghHJ,CmC7gHI,sFACE,yCnC+gHN,CmC1gHE,oEACE,mCnC6gHJ,CmC1gHI,kFACE,wBAlBG,CAmBH,qDAAA,CAAA,6CnC4gHN,CmCxgHI,gFACE,anC0gHN,CmC/hHE,4DACE,oBnCkiHJ,CmC/hHI,sFACE,yCnCiiHN,CmC5hHE,oEACE,mCnC+hHJ,CmC5hHI,kFACE,wBAlBG,CAmBH,qDAAA,CAAA,6CnC8hHN,CmC1hHI,gFACE,anC4hHN,CmCjjHE,0DACE,oBnCojHJ,CmCjjHI,oFACE,yCnCmjHN,CmC9iHE,kEACE,mCnCijHJ,CmC9iHI,gFACE,wBAlBG,CAmBH,oDAAA,CAAA,4CnCgjHN,CmC5iHI,8EACE,anC8iHN,CmCnkHE,oDACE,oBnCskHJ,CmCnkHI,8EACE,wCnCqkHN,CmChkHE,4DACE,kCnCmkHJ,CmChkHI,0EACE,wBAlBG,CAmBH,iDAAA,CAAA,yCnCkkHN,CmC9jHI,wEACE,anCgkHN,CmCrlHE,4DACE,oBnCwlHJ,CmCrlHI,sFACE,0CnCulHN,CmCllHE,oEACE,oCnCqlHJ,CmCllHI,kFACE,wBAlBG,CAmBH,qDAAA,CAAA,6CnColHN,CmChlHI,gFACE,anCklHN,CmCvmHE,wDACE,oBnC0mHJ,CmCvmHI,kFACE,wCnCymHN,CmCpmHE,gEACE,kCnCumHJ,CmCpmHI,8EACE,wBAlBG,CAmBH,mDAAA,CAAA,2CnCsmHN,CmClmHI,4EACE,anComHN,CoCxwHA,MACE,qMpC2wHF,CoClwHE,sBAEE,uCAAA,CADA,gBpCswHJ,CoClwHI,mCACE,apCowHN,CoCrwHI,mCACE,cpCowHN,CoChwHM,4BACE,sBpCkwHR,CoC/vHQ,mCACE,gCpCiwHV,CoC7vHQ,2DACE,SAAA,CAEA,uBAAA,CADA,epCgwHV,CoC3vHQ,yGACE,SAAA,CACA,uBpC6vHV,CoCzvHQ,yCACE,YpC2vHV,CoCpvHE,0BACE,eAAA,CACA,epCsvHJ,CoCnvHI,+BACE,oBpCqvHN,CoChvHE,gDACE,YpCkvHJ,CoC9uHE,8BAIE,+BAAA,CAHA,oBAAA,CAEA,WAAA,CAGA,SAAA,CAKA,4BAAA,CAJA,4DACE,CAHF,0BpCkvHJ,CoCzuHI,aAdF,8BAeI,+BAAA,CACA,SAAA,CACA,uBpC4uHJ,CACF,CoCzuHI,wCACE,6BpC2uHN,CoCvuHI,oCACE,+BpCyuHN,CoCruHI,qCAKE,6BAAA,CADA,UAAA,CAHA,oBAAA,CAEA,YAAA,CAGA,2CAAA,CAAA,mCAAA,CAEA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CAPA,WpC8uHN,CoCjuHQ,mDACE,oBpCmuHV,CqCj1HE,kCAEE,iBrCu1HJ,CqCz1HE,kCAEE,kBrCu1HJ,CqCz1HE,wBAGE,yCAAA,CAFA,oBAAA,CAGA,SAAA,CACA,mCrCo1HJ,CqC/0HI,aAVF,wBAWI,YrCk1HJ,CACF,CqC90HE,6FAEE,SAAA,CACA,mCrCg1HJ,CqC10HE,4FAEE,+BrC40HJ,CqCx0HE,oBACE,yBAAA,CACA,uBAAA,CAGA,yErCw0HJ,CKzsHI,sCgCrHE,qDACE,uBrCi0HN,CACF,CqC5zHE,kEACE,yBrC8zHJ,CqC1zHE,sBACE,0BrC4zHJ,CsCv3HE,2BACE,atC03HJ,CKrsHI,0CiCtLF,2BAKI,etC03HJ,CsCv3HI,6BACE,sBAAA,CAAA,iBtCy3HN,CACF,CsCr3HI,6BAEE,0BAAA,CAAA,2BAAA,CADA,eAAA,CAEA,iBtCu3HN,CsCp3HM,2CACE,kBtCs3HR,CsCh3HI,6CACE,QtCk3HN,CuC94HE,uBACE,4CvCk5HJ,CuC74HE,8CAJE,kCAAA,CAAA,0BvCq5HJ,CuCj5HE,uBACE,4CvCg5HJ,CuC34HE,4BAEE,kCAAA,CAAA,0BAAA,CADA,qCvC84HJ,CuC14HI,mCACE,avC44HN,CuCx4HI,kCACE,avC04HN,CuCr4HE,0BAKE,eAAA,CAJA,aAAA,CAEA,YAAA,CACA,aAAA,CAFA,kBAAA,CAAA,mBvC04HJ,CuCp4HI,uCACE,evCs4HN,CuCl4HI,sCACE,kBvCo4HN,CwCj7HA,MACE,oLxCo7HF,CwC36HE,oBAGE,iBAAA,CAEA,gBAAA,CADA,axC66HJ,CwCz6HI,wCACE,uBxC26HN,CwCv6HI,gCAEE,eAAA,CADA,gBxC06HN,CwCn6HM,wCACE,mBxCq6HR,CwC/5HE,8BAKE,oBxCm6HJ,CwCx6HE,8BAKE,mBxCm6HJ,CwCx6HE,8BAUE,4BxC85HJ,CwCx6HE,4DAWE,6BxC65HJ,CwCx6HE,8BAWE,4BxC65HJ,CwCx6HE,oBASE,cAAA,CANA,aAAA,CACA,eAAA,CAIA,exCg6HJ,CwC15HI,kCACE,uCAAA,CACA,oBxC45HN,CwCx5HI,wCAEE,uCAAA,CADA,YxC25HN,CwCt5HI,oCAEE,WxCm6HN,CwCr6HI,oCAEE,UxCm6HN,CwCr6HI,0BAOE,6BAAA,CADA,UAAA,CADA,WAAA,CAGA,yCAAA,CAAA,iCAAA,CAEA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CAVA,iBAAA,CAEA,UAAA,CAUA,sBAAA,CADA,yBAAA,CARA,UxCi6HN,CwCr5HM,oCACE,wBxCu5HR,CwCl5HI,4BACE,YxCo5HN,CwC/4HI,4CACE,YxCi5HN,CyC3+HE,+DACE,sBAAA,CAEA,mBAAA,CACA,0BAAA,CACA,uBzC6+HJ,CyC1+HI,2EAGE,iBAAA,CADA,eAAA,CADA,yBzC8+HN,CyCv+HE,mEACE,0BzCy+HJ,CyCr+HE,oBACE,qBzCu+HJ,CyCn+HE,gBACE,oBzCq+HJ,CyCj+HE,gBACE,qBzCm+HJ,CyC/9HE,iBACE,kBzCi+HJ,CyC79HE,kBACE,kBzC+9HJ,C0CxgIE,6BACE,sC1C2gIJ,C0CxgIE,cACE,yC1C0gIJ,C0C9/HE,sIACE,oC1CggIJ,C0Cx/HE,2EACE,qC1C0/HJ,C0Ch/HE,wGACE,oC1Ck/HJ,C0Cz+HE,yFACE,qC1C2+HJ,C0Cv+HE,cACE,kC1Cy+HJ,C0Cl+HE,4DACE,sC1Co+HJ,C0C79HE,4DACE,sC1C+9HJ,C0Cx9HE,4DACE,qC1C09HJ,C0Cj9HE,yFACE,qC1Cm9HJ,C0C38HE,2EACE,sC1C68HJ,C0Cl8HE,wHACE,qC1Co8HJ,C0C/7HE,8BAGE,mBAAA,CADA,gBAAA,CADA,gB1Cm8HJ,C0C97HE,eACE,4C1Cg8HJ,C0C77HE,eACE,4C1C+7HJ,C0C37HE,gBAIE,+CAAA,CACA,kDAAA,CAJA,aAAA,CAEA,wBAAA,CADA,wB1Cg8HJ,C0Cz7HE,yBAOE,wCAAA,CACA,+DAAA,CACA,4BAAA,CACA,6BAAA,CARA,iBAAA,CAGA,eAAA,CACA,eAAA,CAFA,cAAA,CADA,oCAAA,CAFA,iB1Co8HJ,C0Cx7HI,6BACE,Y1C07HN,C0Cv7HM,kCACE,wBAAA,CACA,yB1Cy7HR,C0Cn7HE,iCAaE,wCAAA,CACA,+DAAA,CAJA,uCAAA,CACA,0BAAA,CALA,UAAA,CAJA,oBAAA,CAOA,2BAAA,CADA,2BAAA,CADA,2BAAA,CANA,eAAA,CAWA,wBAAA,CAAA,qBAAA,CAAA,gBAAA,CAPA,S1C47HJ,C0C16HE,sBACE,iBAAA,CACA,iB1C46HJ,C0Cv6HE,iCAKE,e1Cq6HJ,C0Cl6HI,sCACE,gB1Co6HN,C0Ch6HI,gDACE,Y1Ck6HN,C0Cx5HA,gBACE,iB1C25HF,C0Cv5HE,yCACE,aAAA,CACA,S1Cy5HJ,C0Cp5HE,mBACE,Y1Cs5HJ,C0Cj5HE,oBACE,Q1Cm5HJ,C0C/4HE,4BACE,WAAA,CACA,SAAA,CACA,e1Ci5HJ,C0C94HI,0CACE,Y1Cg5HN,C0C14HE,yBAKE,wCAAA,CAEA,+BAAA,CADA,4BAAA,CAHA,eAAA,CADA,oDAAA,CAEA,wBAAA,CAAA,qBAAA,CAAA,gB1C+4HJ,C0Cx4HE,2BAEE,+DAAA,CADA,2B1C24HJ,C0Cv4HI,+BACE,uCAAA,CACA,gB1Cy4HN,C0Cp4HE,sBACE,MAAA,CACA,W1Cs4HJ,C0Cj4HA,aACE,a1Co4HF,C0C13HE,4BAEE,aAAA,CADA,Y1C83HJ,C0C13HI,wDAEE,2BAAA,CADA,wB1C63HN,C0Cp3HE,6LAKE,2CAAA,CAEA,+BAAA,CADA,gCAAA,CADA,sBAAA,CAHA,mBAAA,CACA,gBAAA,CAFA,a1C43HJ,C0Cn3HI,2NAEE,UAAA,CACA,UAAA,CAFA,a1Cu3HN,C0Ch3HE,6CACE,Y1Ck3HJ,C0C92HE,qCAKE,wCAAA,CAFA,eAAA,CACA,eAAA,CAFA,eAAA,CADA,oC1Co3HJ,CK3gII,0CqCuKF,8BACE,iB1Cw2HF,C0C91HE,wSAGE,e1Co2HJ,C0Ch2HE,sCAEE,mBAAA,CACA,eAAA,CADA,oBAAA,CADA,kBAAA,CAAA,mB1Co2HJ,CACF,C2CltII,yDAIE,+BAAA,CACA,8BAAA,CAFA,aAAA,CADA,QAAA,CADA,iB3CwtIN,C2ChtII,uBAEE,uCAAA,CADA,c3CmtIN,C2C9pIM,iHAEE,WAlDkB,CAiDlB,kB3CyqIR,C2C1qIM,6HAEE,WAlDkB,CAiDlB,kB3CqrIR,C2CtrIM,6HAEE,WAlDkB,CAiDlB,kB3CisIR,C2ClsIM,oHAEE,WAlDkB,CAiDlB,kB3C6sIR,C2C9sIM,0HAEE,WAlDkB,CAiDlB,kB3CytIR,C2C1tIM,uHAEE,WAlDkB,CAiDlB,kB3CquIR,C2CtuIM,uHAEE,WAlDkB,CAiDlB,kB3CivIR,C2ClvIM,6HAEE,WAlDkB,CAiDlB,kB3C6vIR,C2C9vIM,yCAEE,WAlDkB,CAiDlB,kB3CiwIR,C2ClwIM,yCAEE,WAlDkB,CAiDlB,kB3CqwIR,C2CtwIM,0CAEE,WAlDkB,CAiDlB,kB3CywIR,C2C1wIM,uCAEE,WAlDkB,CAiDlB,kB3C6wIR,C2C9wIM,wCAEE,WAlDkB,CAiDlB,kB3CixIR,C2ClxIM,sCAEE,WAlDkB,CAiDlB,kB3CqxIR,C2CtxIM,wCAEE,WAlDkB,CAiDlB,kB3CyxIR,C2C1xIM,oCAEE,WAlDkB,CAiDlB,kB3C6xIR,C2C9xIM,2CAEE,WAlDkB,CAiDlB,kB3CiyIR,C2ClyIM,qCAEE,WAlDkB,CAiDlB,kB3CqyIR,C2CtyIM,oCAEE,WAlDkB,CAiDlB,kB3CyyIR,C2C1yIM,kCAEE,WAlDkB,CAiDlB,kB3C6yIR,C2C9yIM,qCAEE,WAlDkB,CAiDlB,kB3CizIR,C2ClzIM,mCAEE,WAlDkB,CAiDlB,kB3CqzIR,C2CtzIM,qCAEE,WAlDkB,CAiDlB,kB3CyzIR,C2C1zIM,wCAEE,WAlDkB,CAiDlB,kB3C6zIR,C2C9zIM,sCAEE,WAlDkB,CAiDlB,kB3Ci0IR,C2Cl0IM,2CAEE,WAlDkB,CAiDlB,kB3Cq0IR,C2C1zIM,iCAEE,WAPkB,CAMlB,iB3C6zIR,C2C9zIM,uCAEE,WAPkB,CAMlB,iB3Ci0IR,C2Cl0IM,mCAEE,WAPkB,CAMlB,iB3Cq0IR,C4Cv5IA,MACE,2LAAA,CACA,yL5C05IF,C4Cj5IE,wBAKE,mBAAA,CAHA,YAAA,CACA,qBAAA,CACA,YAAA,CAHA,iB5Cw5IJ,C4C94II,8BAGE,QAAA,CACA,SAAA,CAHA,iBAAA,CACA,O5Ck5IN,C4C74IM,qCACE,0B5C+4IR,C4Cl3IM,kEACE,0C5Co3IR,C4C92IE,2BAME,uBAAA,CADA,+DAAA,CAJA,YAAA,CACA,cAAA,CACA,aAAA,CACA,oB5Ck3IJ,C4C72II,aATF,2BAUI,gB5Cg3IJ,CACF,C4C72II,cAGE,+BACE,iB5C62IN,C4C12IM,sCAQE,qCAAA,CANA,QAAA,CAKA,UAAA,CAHA,aAAA,CAEA,UAAA,CAHA,MAAA,CAFA,iBAAA,CAaA,2CAAA,CALA,2DACE,CAGF,kDAAA,CARA,+B5Ck3IR,CACF,C4Cp2II,8CACE,Y5Cs2IN,C4Cl2II,iCAUE,qCAAA,CACA,6BAAA,CALA,uCAAA,CAEA,cAAA,CAPA,aAAA,CAGA,gBAAA,CACA,eAAA,CAFA,8BAAA,CAMA,+BAAA,CAGA,2CACE,CANF,kBAAA,CALA,U5C82IN,C4C/1IM,aAII,6CACE,O5C81IV,C4C/1IQ,8CACE,O5Ci2IV,C4Cl2IQ,8CACE,O5Co2IV,C4Cr2IQ,8CACE,O5Cu2IV,C4Cx2IQ,8CACE,O5C02IV,C4C32IQ,8CACE,O5C62IV,C4C92IQ,8CACE,O5Cg3IV,C4Cj3IQ,8CACE,O5Cm3IV,C4Cp3IQ,8CACE,O5Cs3IV,C4Cv3IQ,+CACE,Q5Cy3IV,C4C13IQ,+CACE,Q5C43IV,C4C73IQ,+CACE,Q5C+3IV,C4Ch4IQ,+CACE,Q5Ck4IV,C4Cn4IQ,+CACE,Q5Cq4IV,C4Ct4IQ,+CACE,Q5Cw4IV,C4Cz4IQ,+CACE,Q5C24IV,C4C54IQ,+CACE,Q5C84IV,C4C/4IQ,+CACE,Q5Ci5IV,C4Cl5IQ,+CACE,Q5Co5IV,C4Cr5IQ,+CACE,Q5Cu5IV,CACF,C4Cl5IM,uCACE,gC5Co5IR,C4Ch5IM,oDACE,a5Ck5IR,C4C74II,yCACE,S5C+4IN,C4C34IM,2CACE,aAAA,CACA,8B5C64IR,C4Cv4IE,4BACE,U5Cy4IJ,C4Ct4II,aAJF,4BAKI,gB5Cy4IJ,CACF,C4Cr4IE,0BACE,Y5Cu4IJ,C4Cp4II,aAJF,0BAKI,a5Cu4IJ,C4Cn4IM,sCACE,O5Cq4IR,C4Ct4IM,uCACE,O5Cw4IR,C4Cz4IM,uCACE,O5C24IR,C4C54IM,uCACE,O5C84IR,C4C/4IM,uCACE,O5Ci5IR,C4Cl5IM,uCACE,O5Co5IR,C4Cr5IM,uCACE,O5Cu5IR,C4Cx5IM,uCACE,O5C05IR,C4C35IM,uCACE,O5C65IR,C4C95IM,wCACE,Q5Cg6IR,C4Cj6IM,wCACE,Q5Cm6IR,C4Cp6IM,wCACE,Q5Cs6IR,C4Cv6IM,wCACE,Q5Cy6IR,C4C16IM,wCACE,Q5C46IR,C4C76IM,wCACE,Q5C+6IR,C4Ch7IM,wCACE,Q5Ck7IR,C4Cn7IM,wCACE,Q5Cq7IR,C4Ct7IM,wCACE,Q5Cw7IR,C4Cz7IM,wCACE,Q5C27IR,C4C57IM,wCACE,Q5C87IR,CACF,C4Cx7II,+FAEE,Q5C07IN,C4Cv7IM,yGACE,wBAAA,CACA,yB5C07IR,C4Cj7IM,2DAEE,wBAAA,CACA,yBAAA,CAFA,Q5Cq7IR,C4C96IM,iEACE,Q5Cg7IR,C4C76IQ,qLAGE,wBAAA,CACA,yBAAA,CAFA,Q5Ci7IV,C4C36IQ,6FACE,wBAAA,CACA,yB5C66IV,C4Cx6IM,yDACE,kB5C06IR,C4Cr6II,sCACE,Q5Cu6IN,C4Cl6IE,2BAEE,iBAAA,CAOA,kBAAA,CAHA,uCAAA,CAEA,cAAA,CAPA,aAAA,CAGA,YAAA,CACA,gBAAA,CAEA,mBAAA,CAGA,gCAAA,CAPA,W5C26IJ,C4Cj6II,iCAEE,uDAAA,CADA,+B5Co6IN,C4C/5II,iCAKE,6BAAA,CADA,UAAA,CAHA,aAAA,CAEA,WAAA,CAGA,8CAAA,CAAA,sCAAA,CAEA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CACA,+CACE,CATF,U5Cy6IN,C4C15IE,4BAOE,+EACE,CANF,YAAA,CAGA,aAAA,CAFA,qBAAA,CAGA,mBAAA,CALA,iBAAA,CAYA,wBAAA,CATA,Y5Cg6IJ,C4Cp5II,sCACE,wB5Cs5IN,C4Cl5II,oCACE,S5Co5IN,C4Ch5II,kCAGE,8EACE,CAFF,mBAAA,CADA,O5Co5IN,C4C14IM,uDACE,8CAAA,CAAA,sC5C44IR,CKnhJI,0CuCqJF,wDAEE,kB5Co4IF,C4Ct4IA,wDAEE,mB5Co4IF,C4Ct4IA,8CAGE,eAAA,CAFA,eAAA,CAGA,iC5Ck4IF,C4C93IE,8DACE,mB5Ci4IJ,C4Cl4IE,8DACE,kB5Ci4IJ,C4Cl4IE,oDAEE,U5Cg4IJ,C4C53IE,8EAEE,kB5C+3IJ,C4Cj4IE,8EAEE,mB5C+3IJ,C4Cj4IE,8EAGE,kB5C83IJ,C4Cj4IE,8EAGE,mB5C83IJ,C4Cj4IE,oEACE,U5Cg4IJ,C4C13IE,8EAEE,mB5C63IJ,C4C/3IE,8EAEE,kB5C63IJ,C4C/3IE,8EAGE,mB5C43IJ,C4C/3IE,8EAGE,kB5C43IJ,C4C/3IE,oEACE,U5C83IJ,CACF,C4Ch3IE,cAHF,olDAII,gC5Cm3IF,C4Ch3IE,g8GACE,uC5Ck3IJ,CACF,C4C72IA,4sDACE,+B5Cg3IF,C4C52IA,wmDACE,a5C+2IF,C6CnvJA,MACE,qWAAA,CACA,8W7CsvJF,C6C7uJE,4BAEE,oBAAA,CADA,iB7CivJJ,C6C5uJI,sDAEE,S7C+uJN,C6CjvJI,sDAEE,U7C+uJN,C6CjvJI,4CACE,iBAAA,CAEA,S7C8uJN,C6CzuJE,+CAEE,SAAA,CADA,U7C4uJJ,C6CvuJE,kDAEE,W7CkvJJ,C6CpvJE,kDAEE,Y7CkvJJ,C6CpvJE,wCAOE,qDAAA,CADA,UAAA,CADA,aAAA,CAGA,0CAAA,CAAA,kCAAA,CAEA,4BAAA,CAAA,oBAAA,CADA,6BAAA,CAAA,qBAAA,CAEA,yBAAA,CAAA,iBAAA,CAVA,iBAAA,CAEA,SAAA,CACA,Y7CgvJJ,C6CruJE,gEACE,wB1B2Wa,C0B1Wb,mDAAA,CAAA,2C7CuuJJ,C8C/yJA,cACE,sC9CkzJF,C8ChzJE,wBAGE,wBAAA,CAEA,gBAAA,CADA,WAAA,CAHA,Q9CqzJJ,C+CjyJA,aAQE,wBACE,Y/CgyJF,CACF,CgD1yJA,QACE,8DAAA,CAGA,+CAAA,CACA,iEAAA,CACA,oDAAA,CACA,sDAAA,CACA,mDAAA,CAGA,qEAAA,CACA,qEAAA,CACA,wEAAA,CACA,0EAAA,CACA,wEAAA,CACA,yEAAA,CACA,kEAAA,CACA,+DAAA,CACA,oEAAA,CACA,oEAAA,CACA,mEAAA,CACA,gEAAA,CACA,uEAAA,CACA,mEAAA,CACA,qEAAA,CACA,oEAAA,CACA,gEAAA,CACA,wEAAA,CACA,qEAAA,CACA,+DhDwyJF,CgDlyJA,SAEE,kBAAA,CADA,YhDsyJF,CiDr2JA,QAEE,mDAAA,CACA,kEAAA,CACA,qDAAA,CACA,uDAAA,CACA,oDAAA,CACA,sDjDu2JF,CiDn2JA,UACE,YjDs2JF,CiDn2JA,uBACE,6CjDs2JF,CkDz1JE,kBAUE,cAAA,CATA,YAAA,CACA,kEACE,CAQF,YlDq1JJ,CkDj1JI,sDACE,gBlDm1JN,CkD70JI,oFAKE,wDAAA,CACA,mBAAA,CAJA,aAAA,CAEA,QAAA,CADA,aAAA,CAIA,sClD+0JN,CkD10JM,iOACE,wBAAA,CACA,8BlD60JR,CkDz0JM,6FACE,iBAAA,CAAA,clD40JR,CkDx0JM,2HACE,YlD20JR,CkDv0JM,wHACE,elD00JR,CkD3zJI,yMAGE,eAAA,CAAA,YlDm0JN,CkDrzJI,ybAOE,WlD2zJN,CkDvzJI,8BACE,eAAA,CAAA,YlDyzJN,CKrvJI,mC8ChKA,8BACE,UnD65JJ,CmD95JE,8BACE,WnD65JJ,CmD95JE,8BAGE,kBnD25JJ,CmD95JE,8BAGE,iBnD25JJ,CmD95JE,oBAKE,mBAAA,CADA,YAAA,CAFA,anD45JJ,CmDt5JI,kCACE,WnDy5JN,CmD15JI,kCACE,UnDy5JN,CmD15JI,kCAEE,iBAAA,CAAA,cnDw5JN,CmD15JI,kCAEE,aAAA,CAAA,kBnDw5JN,CACF,CoD/7JE,wBACE,epDk8JJ,CoD/7JE,yBACE,gBpDi8JJ,CoD97JE,0BACE,UAAA,CACA,iBpDg8JJ,CoD77JE,uBACE,kBpD+7JJ,CoD57JE,0BACE,qBpD87JJ,CoD37JE,0BACE,qBpD67JJ,CoDz7JE,mJAKE,iBpD27JJ,CoDx7JE,6JAME,gBAAA,CADA,iBpD27JJ,CoDv7JE,wJAKE,gBpDy7JJ,CoDt7JE,wPAQE,apDw7JJ,CoDp7JE,sNAOE,kBpDs7JJ,CoDn7JE,oBACE,epDq7JJ,CoDl7JE,2CACE,WpDo7JJ,CoDj7JE,6BACE,iBpDm7JJ,CoD/6JE,2BACE,apDi7JJ,CoD96JE,0CACE,iBpDg7JJ,CoD56JE,oDAEE,YAAA,CACA,0CpD86JJ,CoD36JM,0EACE,apD86JR,CoD36JM,gFACE,apD86JR,CoD36JM,wFACE,kBpD86JR,CoD16JI,kHACE,apD66JN,CoD36JM,8IACE,YpD86JR,CoD36JM,wIACE,epD86JR,CoD56JQ,oJACE,UAAA,CACA,UpD+6JV,CqDviKE,sDAEE,kCAbkB,CAYlB,sCrD2iKJ,CqDviKI,gEAEE,eAAA,CADA,SrD0iKN,CqDtiKI,8HAGE,kCAtBW,CAqBX,eAAA,CADA,SrD0iKN,CqDriKI,4EACE,iBrDuiKN,CqDpiKI,gHACE,uCrDsiKN,CqDhiKI,wHAGE,eAAA,CADA,YrDmiKN,CqD/hKI,oJACE,+BrDiiKN,CqD3hKQ,sHAIE,iBAAA,CAHA,erD8hKV,CqDnhKQ,kIAEE,YAAA,CADA,erDshKV,CqD9gKE,kDACE,YrDghKJ,CqD1gKI,qcAGE,+BrD+gKN,CqD7gKM,2fACE,+BrDohKR,CqD/gKM,4PAEE,qCrDmhKR,CqDjhKQ,8QACE,arDshKV,CqDlhKM,iUAEE,+BrDshKR,CqDhhKI,0BAKE,kCAhHgB,CA+GhB,sCAAA,CADA,iBAAA,CADA,kBAAA,CADA,gBrDshKN,CqDhhKM,yDAEE,gBrDkhKR,CqD9gKM,oCAGE,qCA1HY,CAwHZ,iBAAA,CACA,erDihKR,CqD7gKM,uCAEE,kCA7Ha,CA4Hb,SrDghKR,CqD5gKM,kFAEE,WAAA,CACA,gBrD8gKR,CqD1gKQ,2FAEE,gBrD4gKV,CqDvgKI,sCACE,gBrDygKN,CqDtgKI,uCACE,mBrDwgKN,CqDlgKQ,2CAEE,aAAA,CACA,eAAA,CAFA,iBrDsgKV,CqD//JM,sCACE,arDigKR,CqD3/JI,4BAGE,arD2/JN,CqDx/JM,yCAEE,SAAA,CAEA,aAAA,CAHA,iBAAA,CAEA,OrD2/JR,CqDv/JM,6DAEE,gBrDy/JR,CqDt/JM,+CACE,WAAA,CACA,sCrDw/JR,CqDt/JQ,sDACE,WrDw/JV,CqDr/JQ,qDACE,WrDu/JV,CqD/+JI,kFAEE,YrDi/JN,CqD3+JE,kEAEE,sBAAA,CADA,iBrD8+JJ,CqDz+JA,cACE,kBrD4+JF,CqDx+JE,QACE,+BAAA,CACA,iCAAA,CACA,8BAAA,CACA,mCAAA,CACA,kCAAA,CACA,oDrD2+JJ,CqDt+JA,cAEE,6BACE,+BAAA,CACA,iCAAA,CACA,8BAAA,CACA,mCAAA,CACA,kCrDw+JF,CACF,CqDr+JA,cAYE,+CAAA,CACA,+CAAA,CACA,iBAAA,CALA,oCAAA,CARA,oBAAA,CACA,aAAA,CAIA,sCAAA,CACA,eAAA,CAHA,WArPkB,CAyPlB,gBAzPkB,CAsPlB,gBAAA,CAKA,iBAAA,CACA,qBAAA,CARA,UrDk/JF,CqDp+JI,qBAEE,6CAAA,CACA,6CAAA,CAFA,oCrDw+JN,CqDz+JI,yBAEE,iDAAA,CACA,iDAAA,CAFA,oCrD6+JN,CqD9+JI,oBAEE,4CAAA,CACA,4CAAA,CAFA,oCrDk/JN,CqDn/JI,wBAEE,gDAAA,CACA,gDAAA,CAFA,oCrDu/JN,CqDh/JA,wBAGE,kDAAA,CACA,iBAAA,CAHA,WAAA,CACA,WrDq/JF,CqD3+JM,yIACE,iBrD8+JR", "file": "main.css", "sourcesContent": ["////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Metadata\n.md-meta {\n  font-size: px2rem(14px);\n  line-height: 1.3;\n  color: var(--md-default-fg-color--light);\n\n  // Metadata list\n  &__list {\n    display: inline-flex;\n    flex-wrap: wrap;\n    padding: 0;\n    margin: 0;\n    list-style: none;\n  }\n\n  // Metadata item separator\n  &__item:not(:last-child)::after {\n    margin-inline: px2rem(4px);\n    content: \"·\";\n  }\n\n  // Metadata link\n  &__link {\n    color: var(--md-typeset-a-color);\n\n    // Metadata link on focus/hover\n    &:is(:focus, :hover) {\n      color: var(--md-accent-fg-color);\n    }\n  }\n}\n\n// Draft\n.md-draft {\n  display: inline-block;\n  padding-inline: px2em(8px, 14px);\n  font-weight: 700;\n  color: hsla(255, 100%, 100%);\n  background-color: $clr-red-a400;\n  border-radius: px2em(2px);\n}\n", "@charset \"UTF-8\";\nhtml {\n  box-sizing: border-box;\n  text-size-adjust: none;\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: inherit;\n}\n@media (prefers-reduced-motion) {\n  *,\n  *::before,\n  *::after {\n    transition: none !important;\n  }\n}\n\nbody {\n  margin: 0;\n}\n\na,\nbutton,\nlabel,\ninput {\n  -webkit-tap-highlight-color: transparent;\n}\n\na {\n  color: inherit;\n  text-decoration: none;\n}\n\nhr {\n  box-sizing: content-box;\n  display: block;\n  height: 0.05rem;\n  padding: 0;\n  overflow: visible;\n  border: 0;\n}\n\nsmall {\n  font-size: 80%;\n}\n\nsub,\nsup {\n  line-height: 1em;\n}\n\nimg {\n  border-style: none;\n}\n\ntable {\n  border-spacing: 0;\n  border-collapse: separate;\n}\n\ntd,\nth {\n  font-weight: 400;\n  vertical-align: top;\n}\n\nbutton {\n  padding: 0;\n  margin: 0;\n  font-family: inherit;\n  font-size: inherit;\n  background: transparent;\n  border: 0;\n}\n\ninput {\n  border: 0;\n  outline: none;\n}\n\n:root {\n  --md-primary-fg-color: hsla(231, 48%, 48%, 1);\n  --md-primary-fg-color--light: hsla(231, 44%, 56%, 1);\n  --md-primary-fg-color--dark: hsla(232, 54%, 41%, 1);\n  --md-primary-bg-color: hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light: hsla(0, 0%, 100%, 0.7);\n  --md-accent-fg-color: hsla(231, 99%, 66%, 1);\n  --md-accent-fg-color--transparent: hsla(231, 99%, 66%, 0.1);\n  --md-accent-bg-color: hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light: hsla(0, 0%, 100%, 0.7);\n}\n\n[data-md-color-scheme=default] {\n  color-scheme: light;\n}\n[data-md-color-scheme=default] img[src$=\"#only-dark\"],\n[data-md-color-scheme=default] img[src$=\"#gh-dark-mode-only\"] {\n  display: none;\n}\n\n:root, [data-md-color-scheme=default] {\n  --md-hue: 225deg;\n  --md-default-fg-color: hsla(0, 0%, 0%, 0.87);\n  --md-default-fg-color--light: hsla(0, 0%, 0%, 0.54);\n  --md-default-fg-color--lighter: hsla(0, 0%, 0%, 0.32);\n  --md-default-fg-color--lightest: hsla(0, 0%, 0%, 0.07);\n  --md-default-bg-color: hsla(0, 0%, 100%, 1);\n  --md-default-bg-color--light: hsla(0, 0%, 100%, 0.7);\n  --md-default-bg-color--lighter: hsla(0, 0%, 100%, 0.3);\n  --md-default-bg-color--lightest: hsla(0, 0%, 100%, 0.12);\n  --md-code-fg-color: hsla(200, 18%, 26%, 1);\n  --md-code-bg-color: hsla(200, 0%, 96%, 1);\n  --md-code-hl-color: hsla(218, 100%, 63%, 1);\n  --md-code-hl-color--light: hsla(218, 100%, 63%, 0.1);\n  --md-code-hl-number-color: hsla(0, 67%, 50%, 1);\n  --md-code-hl-special-color: hsla(340, 83%, 47%, 1);\n  --md-code-hl-function-color: hsla(291, 45%, 50%, 1);\n  --md-code-hl-constant-color: hsla(250, 63%, 60%, 1);\n  --md-code-hl-keyword-color: hsla(219, 54%, 51%, 1);\n  --md-code-hl-string-color: hsla(150, 63%, 30%, 1);\n  --md-code-hl-name-color: var(--md-code-fg-color);\n  --md-code-hl-operator-color: var(--md-default-fg-color--light);\n  --md-code-hl-punctuation-color: var(--md-default-fg-color--light);\n  --md-code-hl-comment-color: var(--md-default-fg-color--light);\n  --md-code-hl-generic-color: var(--md-default-fg-color--light);\n  --md-code-hl-variable-color: var(--md-default-fg-color--light);\n  --md-typeset-color: var(--md-default-fg-color);\n  --md-typeset-a-color: var(--md-primary-fg-color);\n  --md-typeset-del-color: hsla(6, 90%, 60%, 0.15);\n  --md-typeset-ins-color: hsla(150, 90%, 44%, 0.15);\n  --md-typeset-kbd-color: hsla(0, 0%, 98%, 1);\n  --md-typeset-kbd-accent-color: hsla(0, 100%, 100%, 1);\n  --md-typeset-kbd-border-color: hsla(0, 0%, 72%, 1);\n  --md-typeset-mark-color: hsla(60, 100%, 50%, 0.5);\n  --md-typeset-table-color: hsla(0, 0%, 0%, 0.12);\n  --md-typeset-table-color--light: hsla(0, 0%, 0%, 0.035);\n  --md-admonition-fg-color: var(--md-default-fg-color);\n  --md-admonition-bg-color: var(--md-default-bg-color);\n  --md-warning-fg-color: hsla(0, 0%, 0%, 0.87);\n  --md-warning-bg-color: hsla(60, 100%, 80%, 1);\n  --md-footer-fg-color: hsla(0, 0%, 100%, 1);\n  --md-footer-fg-color--light: hsla(0, 0%, 100%, 0.7);\n  --md-footer-fg-color--lighter: hsla(0, 0%, 100%, 0.45);\n  --md-footer-bg-color: hsla(0, 0%, 0%, 0.87);\n  --md-footer-bg-color--dark: hsla(0, 0%, 0%, 0.32);\n  --md-shadow-z1:\n    0 0.2rem 0.5rem hsla(0, 0%, 0%, 0.05),\n    0 0 0.05rem hsla(0, 0%, 0%, 0.1);\n  --md-shadow-z2:\n    0 0.2rem 0.5rem hsla(0, 0%, 0%, 0.1),\n    0 0 0.05rem hsla(0, 0%, 0%, 0.25);\n  --md-shadow-z3:\n    0 0.2rem 0.5rem hsla(0, 0%, 0%, 0.2),\n    0 0 0.05rem hsla(0, 0%, 0%, 0.35);\n}\n\n.md-icon svg {\n  display: block;\n  width: 1.2rem;\n  height: 1.2rem;\n  fill: currentcolor;\n}\n\n.si-icon-inline::before {\n  display: inline-flex;\n  width: 1.125em;\n  height: 1.125em;\n  vertical-align: text-top;\n  content: \"\";\n  background-color: var(--md-default-fg-color);\n  mask-repeat: no-repeat;\n  mask-position: center;\n}\n\nbody {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  --md-text-font-family:\n    var(--md-text-font, _),\n    -apple-system, BlinkMacSystemFont, Helvetica, Arial, sans-serif;\n  --md-code-font-family:\n    var(--md-code-font, _),\n    SFMono-Regular, Consolas, Menlo, monospace;\n}\n\nbody,\ninput,\naside {\n  font-family: var(--md-text-font-family);\n  font-feature-settings: \"kern\", \"liga\";\n  color: var(--md-typeset-color);\n}\n\ncode,\npre,\nkbd {\n  font-family: var(--md-code-font-family);\n  font-feature-settings: \"kern\";\n}\n\n:root {\n  --md-typeset-table-sort-icon: svg-load(\"material/sort.svg\");\n  --md-typeset-table-sort-icon--asc: svg-load(\"material/sort-ascending.svg\");\n  --md-typeset-table-sort-icon--desc: svg-load(\"material/sort-descending.svg\");\n}\n\n.md-typeset {\n  font-size: 0.8rem;\n  line-height: 1.6;\n  overflow-wrap: break-word;\n  color-adjust: exact;\n}\n@media print {\n  .md-typeset {\n    font-size: 0.68rem;\n  }\n}\n.md-typeset ul,\n.md-typeset ol,\n.md-typeset dl,\n.md-typeset figure,\n.md-typeset blockquote,\n.md-typeset pre {\n  margin-block: 1em;\n}\n.md-typeset h1 {\n  margin: 0 0 1.25em;\n  font-size: 2em;\n  font-weight: 300;\n  line-height: 1.3;\n  color: var(--md-default-fg-color--light);\n  letter-spacing: -0.01em;\n}\n.md-typeset h2 {\n  margin: 1.6em 0 0.64em;\n  font-size: 1.5625em;\n  font-weight: 300;\n  line-height: 1.4;\n  letter-spacing: -0.01em;\n}\n.md-typeset h3 {\n  margin: 1.6em 0 0.8em;\n  font-size: 1.25em;\n  font-weight: 400;\n  line-height: 1.5;\n  letter-spacing: -0.01em;\n}\n.md-typeset h2 + h3 {\n  margin-top: 0.8em;\n}\n.md-typeset h4 {\n  margin: 1em 0;\n  font-weight: 700;\n  letter-spacing: -0.01em;\n}\n.md-typeset h5,\n.md-typeset h6 {\n  margin: 1.25em 0;\n  font-size: 0.8em;\n  font-weight: 700;\n  color: var(--md-default-fg-color--light);\n  letter-spacing: -0.01em;\n}\n.md-typeset h5 {\n  text-transform: uppercase;\n}\n.md-typeset h5 code {\n  text-transform: none;\n}\n.md-typeset hr {\n  display: flow-root;\n  margin: 1.5em 0;\n  border-bottom: 0.05rem solid var(--md-default-fg-color--lightest);\n}\n.md-typeset a {\n  color: var(--md-typeset-a-color);\n  word-break: break-word;\n}\n.md-typeset a, .md-typeset a::before {\n  transition: color 125ms;\n}\n.md-typeset a:is(:focus, :hover) {\n  color: var(--md-accent-fg-color);\n}\n.md-typeset a:is(:focus, :hover) code {\n  background-color: var(--md-accent-fg-color--transparent);\n}\n.md-typeset a code {\n  color: currentcolor;\n  transition: background-color 125ms;\n}\n.md-typeset a.focus-visible {\n  outline-color: var(--md-accent-fg-color);\n  outline-offset: 0.2rem;\n}\n.md-typeset code,\n.md-typeset pre,\n.md-typeset kbd {\n  font-variant-ligatures: none;\n  color: var(--md-code-fg-color);\n  direction: ltr;\n}\n@media print {\n  .md-typeset code,\n  .md-typeset pre,\n  .md-typeset kbd {\n    white-space: pre-wrap;\n  }\n}\n.md-typeset code {\n  padding: 0 0.2941176471em;\n  font-size: 0.85em;\n  word-break: break-word;\n  background-color: var(--md-code-bg-color);\n  border-radius: 0.1rem;\n  box-decoration-break: clone;\n}\n.md-typeset code:not(.focus-visible) {\n  outline: none;\n  -webkit-tap-highlight-color: transparent;\n}\n.md-typeset pre {\n  position: relative;\n  display: flow-root;\n  line-height: 1.4;\n}\n.md-typeset pre > code {\n  display: block;\n  padding: 0.7720588235em 1.1764705882em;\n  margin: 0;\n  overflow: auto;\n  word-break: normal;\n  touch-action: auto;\n  scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n  scrollbar-width: thin;\n  outline-color: var(--md-accent-fg-color);\n  box-shadow: none;\n  box-decoration-break: slice;\n}\n.md-typeset pre > code:hover {\n  scrollbar-color: var(--md-accent-fg-color) transparent;\n}\n.md-typeset pre > code::-webkit-scrollbar {\n  width: 0.2rem;\n  height: 0.2rem;\n}\n.md-typeset pre > code::-webkit-scrollbar-thumb {\n  background-color: var(--md-default-fg-color--lighter);\n}\n.md-typeset pre > code::-webkit-scrollbar-thumb:hover {\n  background-color: var(--md-accent-fg-color);\n}\n.md-typeset .code-block-caption + .notranslate pre,\n.md-typeset .code-block-caption + .notranslate .highlighttable {\n  margin-top: 0;\n}\n.md-typeset kbd {\n  display: inline-block;\n  padding: 0 0.6666666667em;\n  font-size: 0.75em;\n  color: var(--md-default-fg-color);\n  word-break: break-word;\n  vertical-align: text-top;\n  background-color: var(--md-typeset-kbd-color);\n  border-radius: 0.1rem;\n  box-shadow: 0 0.1rem 0 0.05rem var(--md-typeset-kbd-border-color), 0 0.1rem 0 var(--md-typeset-kbd-border-color), 0 -0.1rem 0.2rem var(--md-typeset-kbd-accent-color) inset;\n}\n.md-typeset mark {\n  color: inherit;\n  word-break: break-word;\n  background-color: var(--md-typeset-mark-color);\n  box-decoration-break: clone;\n}\n.md-typeset abbr {\n  text-decoration: none;\n  cursor: help;\n  border-bottom: 0.05rem dotted var(--md-default-fg-color--light);\n}\n.md-typeset small {\n  opacity: 0.75;\n}\n.md-typeset sup,\n.md-typeset sub {\n  margin-inline-start: 0.078125em;\n}\n.md-typeset blockquote {\n  padding-inline-start: 0.6rem;\n  margin-inline: 0;\n  color: var(--md-default-fg-color--light);\n  border-inline-start: 0.2rem solid var(--md-default-fg-color--lighter);\n}\n.md-typeset ul {\n  list-style-type: disc;\n}\n.md-typeset ul[type] {\n  list-style-type: revert-layer;\n}\n.md-typeset ul,\n.md-typeset ol {\n  padding: 0;\n  margin-inline-start: 0.625em;\n}\n.md-typeset ul:not([hidden]),\n.md-typeset ol:not([hidden]) {\n  display: flow-root;\n}\n.md-typeset ul ol,\n.md-typeset ol ol {\n  list-style-type: lower-alpha;\n}\n.md-typeset ul ol ol,\n.md-typeset ol ol ol {\n  list-style-type: lower-roman;\n}\n.md-typeset ul ol ol ol,\n.md-typeset ol ol ol ol {\n  list-style-type: upper-alpha;\n}\n.md-typeset ul ol ol ol ol,\n.md-typeset ol ol ol ol ol {\n  list-style-type: upper-roman;\n}\n.md-typeset ul[type],\n.md-typeset ol[type] {\n  list-style-type: revert-layer;\n}\n.md-typeset ul li,\n.md-typeset ol li {\n  margin-inline-start: 1.25em;\n  margin-bottom: 0.5em;\n}\n.md-typeset ul li p,\n.md-typeset ul li blockquote,\n.md-typeset ol li p,\n.md-typeset ol li blockquote {\n  margin: 0.5em 0;\n}\n.md-typeset ul li:last-child,\n.md-typeset ol li:last-child {\n  margin-bottom: 0;\n}\n.md-typeset ul li :is(ul, ol),\n.md-typeset ol li :is(ul, ol) {\n  margin-block: 0.5em;\n  margin-inline-start: 0.625em;\n}\n.md-typeset dd {\n  margin-block: 1em 1.5em;\n  margin-inline-start: 1.875em;\n}\n.md-typeset img,\n.md-typeset svg,\n.md-typeset video {\n  max-width: 100%;\n  height: auto;\n}\n.md-typeset img[align=left] {\n  margin: 1em;\n  margin-left: 0;\n}\n.md-typeset img[align=right] {\n  margin: 1em;\n  margin-right: 0;\n}\n.md-typeset img[align]:only-child {\n  margin-top: 0;\n}\n.md-typeset figure {\n  display: flow-root;\n  width: fit-content;\n  max-width: 100%;\n  margin: 1em auto;\n  text-align: center;\n}\n.md-typeset figure img {\n  display: block;\n  margin: 0 auto;\n}\n.md-typeset figcaption {\n  max-width: 24rem;\n  margin: 1em auto;\n  font-style: italic;\n}\n.md-typeset iframe {\n  max-width: 100%;\n}\n.md-typeset table.data:not(.plain) {\n  display: block;\n  width: max-content;\n  max-width: 100%;\n  overflow: auto;\n  font-size: 0.64rem;\n  touch-action: auto;\n  background-color: var(--md-default-bg-color);\n  border: 0.05rem solid var(--md-typeset-table-color);\n  border-radius: 0.1rem;\n}\n@media print {\n  .md-typeset table.data:not(.plain) {\n    display: table;\n  }\n}\n.md-typeset table.data:not(.plain) + * {\n  margin-top: 1.5em;\n}\n.md-typeset table.data:not(.plain) :is(th, td) > *:first-child {\n  margin-top: 0;\n}\n.md-typeset table.data:not(.plain) :is(th, td) > *:last-child {\n  margin-bottom: 0;\n}\n.md-typeset table.data:not(.plain) :is(th, td):not([align], .align-center, .align-left, .align-right) {\n  text-align: left;\n}\n[dir=rtl] .md-typeset table.data:not(.plain) :is(th, td):not([align], .align-center, .align-left, .align-right) {\n  text-align: right;\n}\n.md-typeset table.data:not(.plain) th {\n  min-width: 5rem;\n  padding: 0.9375em 1.25em;\n  font-weight: 700;\n  vertical-align: top;\n}\n.md-typeset table.data:not(.plain) td {\n  padding: 0.9375em 1.25em;\n  vertical-align: top;\n  border-top: 0.05rem solid var(--md-typeset-table-color);\n}\n.md-typeset table.data:not(.plain) tbody tr {\n  transition: background-color 125ms;\n}\n.md-typeset table.data:not(.plain) tbody tr:hover {\n  background-color: var(--md-typeset-table-color--light);\n  box-shadow: 0 0.05rem 0 var(--md-default-bg-color) inset;\n}\n.md-typeset table.data:not(.plain) a {\n  word-break: normal;\n}\n.md-typeset table th[role=columnheader] {\n  cursor: pointer;\n}\n.md-typeset table th[role=columnheader]::after {\n  display: inline-block;\n  width: 1.2em;\n  height: 1.2em;\n  margin-inline-start: 0.5em;\n  vertical-align: text-bottom;\n  content: \"\";\n  mask-image: var(--md-typeset-table-sort-icon);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n  transition: background-color 125ms;\n}\n.md-typeset table th[role=columnheader]:hover::after {\n  background-color: var(--md-default-fg-color--lighter);\n}\n.md-typeset table th[role=columnheader][aria-sort=ascending]::after {\n  background-color: var(--md-default-fg-color--light);\n  mask-image: var(--md-typeset-table-sort-icon--asc);\n}\n.md-typeset table th[role=columnheader][aria-sort=descending]::after {\n  background-color: var(--md-default-fg-color--light);\n  mask-image: var(--md-typeset-table-sort-icon--desc);\n}\n.md-typeset__scrollwrap {\n  margin: 1em -0.8rem;\n  overflow-x: auto;\n  touch-action: auto;\n}\n.md-typeset__table {\n  display: inline-block;\n  padding: 0 0.8rem;\n  margin-bottom: 0.5em;\n}\n@media print {\n  .md-typeset__table {\n    display: block;\n  }\n}\nhtml .md-typeset__table table {\n  display: table;\n  width: 100%;\n  margin: 0;\n  overflow: hidden;\n}\n\n@media screen and (max-width: 44.984375em) {\n  .md-content__inner > pre {\n    margin: 1em -0.8rem;\n  }\n  .md-content__inner > pre code {\n    border-radius: 0;\n  }\n}\n.md-typeset .md-author {\n  position: relative;\n  display: block;\n  flex-shrink: 0;\n  width: 1.6rem;\n  height: 1.6rem;\n  overflow: hidden;\n  border-radius: 100%;\n  transition: color 125ms, transform 125ms;\n}\n.md-typeset .md-author img {\n  display: block;\n}\n.md-typeset .md-author--more {\n  font-size: 0.6rem;\n  font-weight: 700;\n  line-height: 1.6rem;\n  color: var(--md-default-fg-color--lighter);\n  text-align: center;\n  background: var(--md-default-fg-color--lightest);\n}\n.md-typeset .md-author--long {\n  width: 2.4rem;\n  height: 2.4rem;\n}\n.md-typeset a.md-author {\n  transform: scale(1);\n}\n.md-typeset a.md-author img {\n  filter: grayscale(100%) opacity(75%);\n  border-radius: 100%;\n  transition: filter 125ms;\n}\n.md-typeset a.md-author:is(:focus, :hover) {\n  z-index: 1;\n  transform: scale(1.1);\n}\n.md-typeset a.md-author:is(:focus, :hover) img {\n  filter: grayscale(0%);\n}\n\n.md-banner {\n  overflow: auto;\n  color: var(--md-footer-fg-color);\n  background-color: var(--md-footer-bg-color);\n}\n@media print {\n  .md-banner {\n    display: none;\n  }\n}\n.md-banner--warning {\n  color: var(--md-warning-fg-color);\n  background-color: var(--md-warning-bg-color);\n}\n.md-banner__inner {\n  padding: 0 0.8rem;\n  margin: 0.6rem auto;\n  font-size: 0.7rem;\n}\n.md-banner__button {\n  float: inline-end;\n  color: inherit;\n  cursor: pointer;\n  transition: opacity 250ms;\n}\n.no-js .md-banner__button {\n  display: none;\n}\n.md-banner__button:hover {\n  opacity: 0.7;\n}\n\nhtml {\n  height: 100%;\n  overflow-x: hidden;\n  font-size: 125%;\n}\n@media screen and (min-width: 100em) {\n  html {\n    font-size: 137.5%;\n  }\n}\n@media screen and (min-width: 125em) {\n  html {\n    font-size: 150%;\n  }\n}\n\nbody {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  min-height: 100%;\n  font-size: 0.5rem;\n  background-color: var(--md-default-bg-color);\n}\n@media print {\n  body {\n    display: block;\n  }\n}\n@media screen and (max-width: 59.984375em) {\n  body[data-md-scrolllock] {\n    position: fixed;\n  }\n}\n\n.md-grid {\n  max-width: 61rem;\n  margin-inline: auto;\n}\n\n.md-container {\n  display: flex;\n  flex-grow: 1;\n  flex-direction: column;\n}\n@media print {\n  .md-container {\n    display: block;\n  }\n}\n\n.md-main {\n  flex-grow: 1;\n}\n.md-main__inner {\n  display: flex;\n  height: 100%;\n  margin-top: 1.5rem;\n}\n\n.md-ellipsis {\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.md-toggle {\n  display: none;\n}\n\n.md-option {\n  position: absolute;\n  width: 0;\n  height: 0;\n  opacity: 0;\n}\n.md-option:checked + label:not([hidden]) {\n  display: block;\n}\n.md-option.focus-visible + label {\n  outline-style: auto;\n  outline-color: var(--md-accent-fg-color);\n}\n\n.md-skip {\n  position: fixed;\n  z-index: -1;\n  padding: 0.3rem 0.5rem;\n  margin: 0.5rem;\n  font-size: 0.64rem;\n  color: var(--md-default-bg-color);\n  background-color: var(--md-default-fg-color);\n  border-radius: 0.1rem;\n  outline-color: var(--md-accent-fg-color);\n  opacity: 0;\n  transform: translateY(0.4rem);\n}\n.md-skip:focus {\n  z-index: 10;\n  opacity: 1;\n  transition: transform 250ms cubic-bezier(0.4, 0, 0.2, 1), opacity 175ms 75ms;\n  transform: translateY(0);\n}\n\n@page {\n  margin: 25mm;\n}\n:root {\n  --md-clipboard-icon: svg-load(\"material/content-copy.svg\");\n}\n\n.md-clipboard {\n  position: absolute;\n  top: 0.5em;\n  right: 0.5em;\n  z-index: 1;\n  width: 1.5em;\n  height: 1.5em;\n  color: var(--md-default-fg-color--lightest);\n  cursor: pointer;\n  border-radius: 0.1rem;\n  outline-color: var(--md-accent-fg-color);\n  outline-offset: 0.1rem;\n  transition: color 250ms;\n}\n@media print {\n  .md-clipboard {\n    display: none;\n  }\n}\n.md-clipboard:not(.focus-visible) {\n  outline: none;\n  -webkit-tap-highlight-color: transparent;\n}\n:hover > .md-clipboard {\n  color: var(--md-default-fg-color--light);\n}\n.md-clipboard:is(:focus, :hover) {\n  color: var(--md-accent-fg-color);\n}\n.md-clipboard::after {\n  display: block;\n  width: 1.125em;\n  height: 1.125em;\n  margin: 0 auto;\n  content: \"\";\n  background-color: currentcolor;\n  mask-image: var(--md-clipboard-icon);\n  mask-repeat: no-repeat;\n  mask-position: center;\n  mask-size: contain;\n}\n.md-clipboard--inline {\n  cursor: pointer;\n}\n.md-clipboard--inline code {\n  transition: color 250ms, background-color 250ms;\n}\n.md-clipboard--inline:is(:focus, :hover) code {\n  color: var(--md-accent-fg-color);\n  background-color: var(--md-accent-fg-color--transparent);\n}\n\n.md-typeset .md-code__content {\n  display: grid;\n}\n\n@keyframes consent {\n  0% {\n    opacity: 0;\n    transform: translateY(100%);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n@keyframes overlay {\n  0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n.md-consent__overlay {\n  position: fixed;\n  top: 0;\n  z-index: 5;\n  width: 100%;\n  height: 100%;\n  background-color: hsla(0, 0%, 0%, 0.54);\n  backdrop-filter: blur(0.1rem);\n  opacity: 1;\n  animation: overlay 250ms both;\n}\n.md-consent__inner {\n  position: fixed;\n  bottom: 0;\n  z-index: 5;\n  width: 100%;\n  max-height: 100%;\n  padding: 0;\n  overflow: auto;\n  background-color: var(--md-default-bg-color);\n  border: 0;\n  border-radius: 0.1rem;\n  box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0.1), 0 0.2rem 0.4rem rgba(0, 0, 0, 0.2);\n  animation: consent 500ms cubic-bezier(0.1, 0.7, 0.1, 1) both;\n}\n.md-consent__form {\n  padding: 0.8rem;\n}\n.md-consent__settings {\n  display: none;\n  margin: 1em 0;\n}\ninput:checked + .md-consent__settings {\n  display: block;\n}\n.md-consent__controls {\n  margin-bottom: 0.8rem;\n}\n.md-typeset .md-consent__controls .md-button {\n  display: inline;\n}\n@media screen and (max-width: 44.984375em) {\n  .md-typeset .md-consent__controls .md-button {\n    display: block;\n    width: 100%;\n    margin-top: 0.4rem;\n    text-align: center;\n  }\n}\n.md-consent label {\n  cursor: pointer;\n}\n\n.md-content {\n  flex-grow: 1;\n  min-width: 0;\n}\n.md-content__inner {\n  padding-top: 0.6rem;\n  margin: 0 0.8rem 1.2rem;\n}\n@media screen and (min-width: 76.25em) {\n  .md-sidebar--primary:not([hidden]) ~ .md-content > .md-content__inner {\n    margin-inline-start: 1.2rem;\n  }\n  .md-sidebar--secondary:not([hidden]) ~ .md-content > .md-content__inner {\n    margin-inline-end: 1.2rem;\n  }\n}\n.md-content__inner::before {\n  display: block;\n  height: 0.4rem;\n  content: \"\";\n}\n.md-content__inner > :last-child {\n  margin-bottom: 0;\n}\n.md-content__button {\n  float: inline-end;\n  padding: 0;\n  margin: 0.4rem 0;\n  margin-inline-start: 0.4rem;\n}\n@media print {\n  .md-content__button {\n    display: none;\n  }\n}\n.md-typeset .md-content__button {\n  color: var(--md-default-fg-color--lighter);\n}\n.md-content__button svg {\n  display: inline;\n  vertical-align: top;\n}\n[dir=rtl] .md-content__button svg {\n  transform: scaleX(-1);\n}\n\n.md-dialog {\n  position: fixed;\n  inset-inline-end: 0.8rem;\n  bottom: 0.8rem;\n  z-index: 4;\n  min-width: 11.1rem;\n  padding: 0.4rem 0.6rem;\n  pointer-events: none;\n  background-color: var(--md-default-fg-color);\n  border-radius: 0.1rem;\n  box-shadow: var(--md-shadow-z3);\n  opacity: 0;\n  transition: transform 0ms 400ms, opacity 400ms;\n  transform: translateY(100%);\n}\n@media print {\n  .md-dialog {\n    display: none;\n  }\n}\n.md-dialog--active {\n  pointer-events: initial;\n  opacity: 1;\n  transition: transform 400ms cubic-bezier(0.075, 0.85, 0.175, 1), opacity 400ms;\n  transform: translateY(0);\n}\n.md-dialog__inner {\n  font-size: 0.7rem;\n  color: var(--md-default-bg-color);\n}\n\n.md-feedback {\n  margin: 2em 0 1em;\n  text-align: center;\n}\n.md-feedback fieldset {\n  padding: 0;\n  margin: 0;\n  border: none;\n}\n.md-feedback__title {\n  margin: 1em auto;\n  font-weight: 700;\n}\n.md-feedback__inner {\n  position: relative;\n}\n.md-feedback__list {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  place-content: baseline center;\n}\n.md-feedback__list:hover .md-icon:not(:disabled) {\n  color: var(--md-default-fg-color--lighter);\n}\n:disabled .md-feedback__list {\n  min-height: 1.8rem;\n}\n.md-feedback__icon {\n  flex-shrink: 0;\n  margin: 0 0.1rem;\n  color: var(--md-default-fg-color--light);\n  cursor: pointer;\n  transition: color 125ms;\n}\n.md-feedback__icon:not(:disabled).md-icon:hover {\n  color: var(--md-accent-fg-color);\n}\n.md-feedback__icon:disabled {\n  color: var(--md-default-fg-color--lightest);\n  pointer-events: none;\n}\n.md-feedback__note {\n  position: relative;\n  opacity: 0;\n  transition: transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 150ms;\n  transform: translateY(0.4rem);\n}\n.md-feedback__note > * {\n  max-width: 16rem;\n  margin: 0 auto;\n}\n:disabled .md-feedback__note {\n  opacity: 1;\n  transform: translateY(0);\n}\n@media print {\n  .md-feedback {\n    display: none;\n  }\n}\n\n.md-footer {\n  color: var(--md-footer-fg-color);\n  background-color: var(--md-footer-bg-color);\n}\n@media print {\n  .md-footer {\n    display: none;\n  }\n}\n.md-footer__inner {\n  justify-content: space-between;\n  padding: 0.2rem;\n  overflow: auto;\n}\n.md-footer__inner:not([hidden]) {\n  display: flex;\n}\n.md-footer__link {\n  display: flex;\n  flex-grow: 0.01;\n  align-items: end;\n  max-width: 100%;\n  margin-block: 1rem 0.4rem;\n  overflow: hidden;\n  outline-color: var(--md-accent-fg-color);\n  transition: opacity 250ms;\n}\n.md-footer__link:is(:focus, :hover) {\n  opacity: 0.7;\n}\n[dir=rtl] .md-footer__link svg {\n  transform: scaleX(-1);\n}\n@media screen and (max-width: 44.984375em) {\n  .md-footer__link--prev {\n    flex-shrink: 0;\n  }\n  .md-footer__link--prev .md-footer__title {\n    display: none;\n  }\n}\n.md-footer__link--next {\n  margin-inline-start: auto;\n  text-align: right;\n}\n[dir=rtl] .md-footer__link--next {\n  text-align: left;\n}\n.md-footer__title {\n  flex-grow: 1;\n  max-width: calc(100% - 2.4rem);\n  padding: 0 1rem;\n  margin-bottom: 0.7rem;\n  font-size: 0.9rem;\n  white-space: nowrap;\n}\n.md-footer__button {\n  padding: 0.4rem;\n  margin: 0.2rem;\n}\n.md-footer__direction {\n  font-size: 0.64rem;\n  opacity: 0.7;\n}\n\n.md-footer-meta {\n  background-color: var(--md-footer-bg-color--dark);\n}\n.md-footer-meta__inner {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  padding: 0.2rem;\n}\nhtml .md-footer-meta.md-typeset a {\n  color: var(--md-footer-fg-color--light);\n}\nhtml .md-footer-meta.md-typeset a:is(:focus, :hover) {\n  color: var(--md-footer-fg-color);\n}\n\n.md-copyright {\n  width: 100%;\n  padding: 0.4rem 0;\n  margin: auto 0.6rem;\n  font-size: 0.64rem;\n  color: var(--md-footer-fg-color--lighter);\n}\n@media screen and (min-width: 45em) {\n  .md-copyright {\n    width: auto;\n  }\n}\n.md-copyright__highlight {\n  color: var(--md-footer-fg-color--light);\n}\n\n.md-social {\n  display: inline-flex;\n  gap: 0.2rem;\n  padding: 0.2rem 0 0.6rem;\n  margin: 0 0.4rem;\n}\n@media screen and (min-width: 45em) {\n  .md-social {\n    padding: 0.6rem 0;\n  }\n}\n.md-social__link {\n  display: inline-block;\n  width: 1.6rem;\n  height: 1.6rem;\n  text-align: center;\n}\n.md-social__link::before {\n  line-height: 1.9;\n}\n.md-social__link svg {\n  max-height: 0.8rem;\n  vertical-align: -25%;\n  fill: currentcolor;\n}\n\n.md-typeset .md-button {\n  display: inline-block;\n  padding: 0.625em 2em;\n  font-weight: 700;\n  color: var(--md-primary-fg-color);\n  cursor: pointer;\n  border: 0.1rem solid currentcolor;\n  border-radius: 0.1rem;\n  transition: color 125ms, background-color 125ms, border-color 125ms;\n}\n.md-typeset .md-button--primary {\n  color: var(--md-primary-bg-color);\n  background-color: var(--md-primary-fg-color);\n  border-color: var(--md-primary-fg-color);\n}\n.md-typeset .md-button:is(:focus, :hover) {\n  color: var(--md-accent-bg-color);\n  background-color: var(--md-accent-fg-color);\n  border-color: var(--md-accent-fg-color);\n}\n.md-typeset .md-input {\n  height: 1.8rem;\n  padding: 0 0.6rem;\n  font-size: 0.8rem;\n  border-bottom: 0.1rem solid var(--md-default-fg-color--lighter);\n  border-start-start-radius: 0.1rem;\n  border-start-end-radius: 0.1rem;\n  box-shadow: var(--md-shadow-z1);\n  transition: border 250ms, box-shadow 250ms;\n}\n.md-typeset .md-input:is(:focus, :hover) {\n  border-bottom-color: var(--md-accent-fg-color);\n  box-shadow: var(--md-shadow-z2);\n}\n.md-typeset .md-input--stretch {\n  width: 100%;\n}\n\n.md-header {\n  position: sticky;\n  inset-inline: 0;\n  top: 0;\n  z-index: 4;\n  display: block;\n  color: var(--md-primary-bg-color);\n  background-color: var(--md-primary-fg-color);\n  box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0), 0 0.2rem 0.4rem rgba(0, 0, 0, 0);\n}\n@media print {\n  .md-header {\n    display: none;\n  }\n}\n.md-header[hidden] {\n  transition: transform 250ms cubic-bezier(0.8, 0, 0.6, 1), box-shadow 250ms;\n  transform: translateY(-100%);\n}\n.md-header--shadow {\n  box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0.1), 0 0.2rem 0.4rem rgba(0, 0, 0, 0.2);\n  transition: transform 250ms cubic-bezier(0.1, 0.7, 0.1, 1), box-shadow 250ms;\n}\n.md-header__inner {\n  display: flex;\n  align-items: center;\n  padding: 0 0.2rem;\n}\n.md-header__button {\n  position: relative;\n  z-index: 1;\n  padding: 0.4rem;\n  margin: 0.2rem;\n  color: currentcolor;\n  vertical-align: middle;\n  cursor: pointer;\n  outline-color: var(--md-accent-fg-color);\n  transition: opacity 250ms;\n}\n.md-header__button:hover {\n  opacity: 0.7;\n}\n.md-header__button:not([hidden]) {\n  display: inline-block;\n}\n.md-header__button:not(.focus-visible) {\n  outline: none;\n  -webkit-tap-highlight-color: transparent;\n}\n.md-header__button.md-logo {\n  padding: 0.4rem;\n  margin: 0.2rem;\n}\n@media screen and (max-width: 76.234375em) {\n  .md-header__button.md-logo {\n    display: none;\n  }\n}\n.md-header__button.md-logo :is(img, svg) {\n  display: block;\n  width: auto;\n  height: 1.2rem;\n  fill: currentcolor;\n}\n@media screen and (min-width: 60em) {\n  .md-header__button[for=__search] {\n    display: none;\n  }\n}\n.no-js .md-header__button[for=__search] {\n  display: none;\n}\n[dir=rtl] .md-header__button[for=__search] svg {\n  transform: scaleX(-1);\n}\n@media screen and (min-width: 76.25em) {\n  .md-header__button[for=__drawer] {\n    display: none;\n  }\n}\n.md-header__topic {\n  position: absolute;\n  display: flex;\n  max-width: 100%;\n  white-space: nowrap;\n  transition: transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 150ms;\n}\n.md-header__topic + .md-header__topic {\n  z-index: -1;\n  pointer-events: none;\n  opacity: 0;\n  transition: transform 400ms cubic-bezier(1, 0.7, 0.1, 0.1), opacity 150ms;\n  transform: translateX(1.25rem);\n}\n[dir=rtl] .md-header__topic + .md-header__topic {\n  transform: translateX(-1.25rem);\n}\n.md-header__topic:first-child {\n  font-weight: 700;\n}\n.md-header__title {\n  flex-grow: 1;\n  height: 2.4rem;\n  margin-inline: 1rem 0.4rem;\n  font-size: 0.9rem;\n  line-height: 2.4rem;\n}\n.md-header__title--active .md-header__topic {\n  z-index: -1;\n  pointer-events: none;\n  opacity: 0;\n  transition: transform 400ms cubic-bezier(1, 0.7, 0.1, 0.1), opacity 150ms;\n  transform: translateX(-1.25rem);\n}\n[dir=rtl] .md-header__title--active .md-header__topic {\n  transform: translateX(1.25rem);\n}\n.md-header__title--active .md-header__topic + .md-header__topic {\n  z-index: 0;\n  pointer-events: initial;\n  opacity: 1;\n  transition: transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 150ms;\n  transform: translateX(0);\n}\n.md-header__title > .md-header__ellipsis {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.md-header__option {\n  display: flex;\n  flex-shrink: 0;\n  max-width: 100%;\n  white-space: nowrap;\n  transition: max-width 0ms 250ms, opacity 250ms 250ms;\n}\n[data-md-toggle=search]:checked ~ .md-header .md-header__option {\n  max-width: 0;\n  opacity: 0;\n  transition: max-width 0ms, opacity 0ms;\n}\n.md-header__option > input {\n  bottom: 0;\n}\n.md-header__source {\n  display: none;\n}\n@media screen and (min-width: 60em) {\n  .md-header__source {\n    display: block;\n    width: 11.7rem;\n    max-width: 11.7rem;\n    margin-inline-start: 1rem;\n  }\n}\n@media screen and (min-width: 76.25em) {\n  .md-header__source {\n    margin-inline-start: 1.4rem;\n  }\n}\n\n.md-hero {\n  overflow: hidden;\n  font-size: 1rem;\n  color: var(--md-primary-bg-color);\n  background-color: var(--md-primary-fg-color);\n  transition: background 250ms;\n}\n.md-hero__inner {\n  padding: 0.8rem 0.8rem 0.4rem;\n  margin-top: 1rem;\n  transition: transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 250ms;\n  transition-delay: 100ms;\n}\n@media screen and (max-width: 76.234375em) {\n  .md-hero__inner {\n    margin-top: 2.4rem;\n    margin-bottom: 1.2rem;\n  }\n}\n[data-md-state=hidden] .md-hero__inner {\n  pointer-events: none;\n  opacity: 0;\n  transition: transform 0ms 400ms, opacity 100ms 0ms;\n  transform: translateY(0.625rem);\n}\n.md-hero--expand .md-hero__inner {\n  margin-bottom: 1.2rem;\n}\n\n.md-meta {\n  font-size: 0.7rem;\n  line-height: 1.3;\n  color: var(--md-default-fg-color--light);\n}\n.md-meta__list {\n  display: inline-flex;\n  flex-wrap: wrap;\n  padding: 0;\n  margin: 0;\n  list-style: none;\n}\n.md-meta__item:not(:last-child)::after {\n  margin-inline: 0.2rem;\n  content: \"·\";\n}\n.md-meta__link {\n  color: var(--md-typeset-a-color);\n}\n.md-meta__link:is(:focus, :hover) {\n  color: var(--md-accent-fg-color);\n}\n\n.md-draft {\n  display: inline-block;\n  padding-inline: 0.5714285714em;\n  font-weight: 700;\n  color: hsl(255, 100%, 100%);\n  background-color: #ff1744;\n  border-radius: 0.125em;\n}\n\n:root {\n  --md-nav-icon--prev: svg-load(\"material/arrow-left.svg\");\n  --md-nav-icon--next: svg-load(\"material/chevron-right.svg\");\n  --md-toc-icon: svg-load(\"material/table-of-contents.svg\");\n}\n\n.md-nav {\n  font-size: 0.7rem;\n  line-height: 1.3;\n}\n.md-nav__title {\n  display: flex;\n  align-items: center;\n  padding: 0 0.6rem;\n  overflow: hidden;\n  font-weight: 700;\n  color: var(--md-default-fg-color--light);\n  text-overflow: ellipsis;\n}\n.md-nav__title .md-nav__button {\n  display: none;\n}\n.md-nav__title .md-nav__button img {\n  width: auto;\n  height: 100%;\n}\n.md-nav__title .md-nav__button.md-logo :is(img, svg) {\n  display: block;\n  width: auto;\n  max-width: 100%;\n  height: 2.4rem;\n  object-fit: contain;\n  fill: currentcolor;\n}\n.md-nav__list {\n  padding: 0;\n  margin: 0;\n  list-style: none;\n}\n.md-nav__link {\n  display: flex;\n  gap: 0.4rem;\n  align-items: flex-start;\n  margin-top: 0.625em;\n  scroll-snap-align: start;\n  transition: color 125ms;\n}\n.md-nav__link.md-nav__sticky {\n  box-shadow: 0 -0.625em var(--md-default-bg-color), 0 0.625em var(--md-default-bg-color);\n}\n.md-nav__link--passed {\n  color: var(--md-default-fg-color--light);\n}\n.md-nav__item .md-nav__link--active,\n.md-nav__item .md-nav__link--active code {\n  color: var(--md-typeset-a-color);\n}\n.md-nav__link--in-viewport {\n  position: relative;\n}\n.md-nav__link--in-viewport::before {\n  position: absolute;\n  top: 0;\n  right: calc(100% + 0.3rem);\n  bottom: 0;\n  width: 0.05rem;\n  height: 100%;\n  content: \"\";\n  background-color: var(--md-primary-fg-color);\n}\n.md-nav__link .md-ellipsis {\n  position: relative;\n}\n.md-nav__link .md-icon:last-child {\n  margin-inline-start: auto;\n}\n.md-nav__link svg {\n  position: relative;\n  flex-shrink: 0;\n  height: 1.3em;\n  fill: currentcolor;\n}\n.md-nav__link:is([href], [for]):is(:focus, :hover) {\n  color: var(--md-accent-fg-color);\n  cursor: pointer;\n}\n.md-nav__link.focus-visible {\n  outline-color: var(--md-accent-fg-color);\n  outline-offset: 0.2rem;\n}\n.md-nav--primary .md-nav__link[for=__toc] {\n  display: none;\n}\n.md-nav--primary .md-nav__link[for=__toc] .md-icon::after {\n  display: block;\n  width: 100%;\n  height: 100%;\n  background-color: currentcolor;\n  mask-image: var(--md-toc-icon);\n}\n.md-nav--primary .md-nav__link[for=__toc] ~ .md-nav {\n  display: none;\n}\n.md-nav__container > .md-nav__link {\n  margin-top: 0;\n}\n.md-nav__container > .md-nav__link:first-child {\n  flex-grow: 1;\n  min-width: 0;\n}\n.md-nav__sticky {\n  position: sticky;\n  top: var(--md-nav__header-height, 0);\n  z-index: var(--md-nav__sticky-zindex);\n  background-color: var(--md-default-bg-color);\n}\n.md-nav .md-ellipsis {\n  display: block;\n  flex-grow: 1;\n  white-space: normal;\n}\n.md-nav__icon {\n  flex-shrink: 0;\n}\n.md-nav__source {\n  display: none;\n}\n@media screen and (max-width: 76.234375em) {\n  .md-nav--primary, .md-nav--primary .md-nav {\n    position: absolute;\n    inset-inline: 0;\n    top: 0;\n    z-index: 1;\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    background-color: var(--md-default-bg-color);\n  }\n  .md-nav--primary .md-nav__sticky {\n    position: static;\n    z-index: auto;\n    background-color: transparent;\n    box-shadow: none;\n  }\n  .md-nav--primary :is(.md-nav__title, .md-nav__item) {\n    font-size: 0.8rem;\n    line-height: 1.5;\n  }\n  .md-nav--primary .md-nav__title {\n    position: relative;\n    min-height: 5.6rem;\n    padding: 3rem 0.8rem 0.2rem;\n    line-height: 2.4rem;\n    color: var(--md-default-fg-color--light);\n    white-space: nowrap;\n    cursor: pointer;\n    background-color: var(--md-default-fg-color--lightest);\n  }\n  .md-nav--primary .md-nav__title .md-nav__icon {\n    position: absolute;\n    inset-inline-start: 0.4rem;\n    top: 0.4rem;\n    display: block;\n    width: 1.2rem;\n    height: 1.2rem;\n    margin: 0.2rem;\n  }\n  .md-nav--primary .md-nav__title .md-nav__icon::after {\n    display: block;\n    width: 100%;\n    height: 100%;\n    content: \"\";\n    background-color: currentcolor;\n    mask-image: var(--md-nav-icon--prev);\n    mask-repeat: no-repeat;\n    mask-position: center;\n    mask-size: contain;\n  }\n  .md-nav--primary .md-nav__title ~ .md-nav__list {\n    overflow-y: auto;\n    touch-action: pan-y;\n    scroll-snap-type: y mandatory;\n    background-color: var(--md-default-bg-color);\n    box-shadow: 0 0.05rem 0 var(--md-default-fg-color--lightest) inset;\n  }\n  .md-nav--primary .md-nav__title ~ .md-nav__list > :first-child {\n    border-top: 0;\n  }\n  .md-nav--primary .md-nav__title[for=__drawer] {\n    font-weight: 700;\n    color: var(--md-primary-bg-color);\n    background-color: var(--md-primary-fg-color);\n  }\n  .md-nav--primary .md-nav__title .md-logo {\n    position: absolute;\n    inset-inline: 0.2rem;\n    top: 0.2rem;\n    display: block;\n    padding: 0.4rem;\n    margin: 0.2rem;\n  }\n  .md-nav--primary .md-nav__list {\n    flex: 1;\n  }\n  .md-nav--primary .md-nav__item {\n    border-top: 0.05rem solid var(--md-default-fg-color--lightest);\n  }\n  .md-nav--primary .md-nav__item--active > .md-nav__link {\n    color: var(--md-typeset-a-color);\n  }\n  .md-nav--primary .md-nav__item--active > .md-nav__link:is(:focus, :hover) {\n    color: var(--md-accent-fg-color);\n  }\n  .md-nav--primary .md-nav__link {\n    padding: 0.6rem 0.8rem;\n    margin-top: 0;\n  }\n  .md-nav--primary .md-nav__link svg {\n    margin-top: 0.1em;\n  }\n  .md-nav--primary .md-nav__link > .md-nav__link {\n    padding: 0;\n  }\n  .md-nav--primary .md-nav__link .md-nav__icon {\n    width: 1.2rem;\n    height: 1.2rem;\n    margin-inline-end: -0.2rem;\n    font-size: 1.2rem;\n  }\n  .md-nav--primary .md-nav__link .md-nav__icon::after {\n    display: block;\n    width: 100%;\n    height: 100%;\n    content: \"\";\n    background-color: currentcolor;\n    mask-image: var(--md-nav-icon--next);\n    mask-repeat: no-repeat;\n    mask-position: center;\n    mask-size: contain;\n  }\n  [dir=rtl] .md-nav--primary .md-nav__icon::after {\n    transform: scale(-1);\n  }\n  .md-nav--primary .md-nav--secondary .md-nav {\n    position: static;\n    background-color: transparent;\n  }\n  .md-nav--primary .md-nav--secondary .md-nav .md-nav__link {\n    padding-inline-start: 1.4rem;\n  }\n  .md-nav--primary .md-nav--secondary .md-nav .md-nav .md-nav__link {\n    padding-inline-start: 2rem;\n  }\n  .md-nav--primary .md-nav--secondary .md-nav .md-nav .md-nav .md-nav__link {\n    padding-inline-start: 2.6rem;\n  }\n  .md-nav--primary .md-nav--secondary .md-nav .md-nav .md-nav .md-nav .md-nav__link {\n    padding-inline-start: 3.2rem;\n  }\n  .md-nav--secondary {\n    background-color: transparent;\n  }\n  .md-nav__toggle ~ .md-nav {\n    display: flex;\n    opacity: 0;\n    transition: transform 250ms cubic-bezier(0.8, 0, 0.6, 1), opacity 125ms 50ms;\n    transform: translateX(100%);\n  }\n  [dir=rtl] .md-nav__toggle ~ .md-nav {\n    transform: translateX(-100%);\n  }\n  .md-nav__toggle:checked ~ .md-nav {\n    opacity: 1;\n    transition: transform 250ms cubic-bezier(0.4, 0, 0.2, 1), opacity 125ms 125ms;\n    transform: translateX(0);\n  }\n  .md-nav__toggle:checked ~ .md-nav > .md-nav__list {\n    backface-visibility: hidden;\n  }\n  .md-nav .md-nav__title .md-ellipsis {\n    white-space: nowrap;\n  }\n  .md-nav .md-nav__title .md-ellipsis wbr {\n    display: none;\n  }\n}\n@media screen and (max-width: 59.984375em) {\n  .md-nav__current-nested {\n    display: none !important;\n  }\n  .md-nav--primary .md-nav__link[for=__toc] {\n    display: flex;\n  }\n  .md-nav--primary .md-nav__link[for=__toc] .md-icon::after {\n    content: \"\";\n  }\n  .md-nav--primary .md-nav__link[for=__toc] + .md-nav__link {\n    display: none;\n  }\n  .md-nav--primary .md-nav__link[for=__toc] ~ .md-nav {\n    display: flex;\n  }\n  .md-nav__source {\n    display: block;\n    padding: 0 0.2rem;\n    color: var(--md-primary-bg-color);\n    background-color: var(--md-primary-fg-color--dark);\n  }\n}\n@media screen and (min-width: 60em) and (max-width: 76.234375em) {\n  .md-nav--integrated .md-nav__link[for=__toc] {\n    display: flex;\n  }\n  .md-nav--integrated .md-nav__link[for=__toc] .md-icon::after {\n    content: \"\";\n  }\n  .md-nav--integrated .md-nav__link[for=__toc] + .md-nav__link {\n    display: none;\n  }\n  .md-nav--integrated .md-nav__link[for=__toc] ~ .md-nav {\n    display: flex;\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-nav {\n    margin-bottom: -0.4rem;\n  }\n  .md-nav__current-toc {\n    display: none !important;\n  }\n  .md-nav--secondary .md-nav__title {\n    position: sticky;\n    top: 0;\n    z-index: 1;\n    background: var(--md-default-bg-color);\n    box-shadow: 0 0 0.4rem 0.4rem var(--md-default-bg-color);\n  }\n  .md-nav--secondary .md-nav__title[for=__toc] {\n    scroll-snap-align: start;\n  }\n  .md-nav--secondary .md-nav__title .md-nav__icon {\n    display: none;\n  }\n  .md-nav--secondary .md-nav__list {\n    padding-inline-start: 0.6rem;\n    padding-bottom: 0.4rem;\n  }\n  .md-nav--secondary .md-nav__item > .md-nav__link {\n    margin-inline-end: 0.4rem;\n  }\n}\n@media screen and (min-width: 76.25em) {\n  .md-nav {\n    margin-bottom: -0.4rem;\n    transition: max-height 250ms cubic-bezier(0.86, 0, 0.07, 1);\n  }\n  .md-nav--primary .md-nav__title {\n    position: sticky;\n    top: 0;\n    z-index: 1;\n    background: var(--md-default-bg-color);\n    box-shadow: 0 0 0.4rem 0.4rem var(--md-default-bg-color);\n  }\n  .md-nav--primary .md-nav__title[for=__drawer] {\n    scroll-snap-align: start;\n  }\n  .md-nav--primary .md-nav__title .md-nav__icon {\n    display: none;\n  }\n  .md-nav--primary .md-nav__list {\n    padding-inline-start: 0.6rem;\n    padding-bottom: 0.4rem;\n  }\n  .md-nav--primary .md-nav__item > .md-nav__link {\n    margin-inline-end: 0.4rem;\n  }\n  .md-nav__toggle ~ .md-nav {\n    display: grid;\n    grid-template-rows: minmax(0.4rem, 0fr);\n    visibility: collapse;\n    opacity: 0;\n    transition: grid-template-rows 250ms cubic-bezier(0.86, 0, 0.07, 1), opacity 250ms, visibility 0ms 250ms;\n  }\n  .md-nav__toggle ~ .md-nav > .md-nav__list {\n    overflow: hidden;\n  }\n  .md-nav__toggle:is(:checked, .md-toggle--indeterminate) ~ .md-nav {\n    grid-template-rows: minmax(0.4rem, 1fr);\n    visibility: visible;\n    opacity: 1;\n    transition: grid-template-rows 250ms cubic-bezier(0.86, 0, 0.07, 1), opacity 150ms 100ms, visibility 0ms;\n  }\n  .md-nav__toggle:is(:checked, .md-toggle--indeterminate) ~ .md-nav > .md-nav__list {\n    overflow: visible;\n  }\n  .md-nav__toggle.md-toggle--indeterminate ~ .md-nav {\n    transition: none;\n  }\n  .md-nav__item--nested > .md-nav > .md-nav__title {\n    display: none;\n  }\n  .md-nav__item--section {\n    display: block;\n    margin: 1.25em 0;\n  }\n  .md-nav__item--section:last-child {\n    margin-bottom: 0;\n  }\n  .md-nav__item--section > .md-nav__link {\n    font-weight: 700;\n  }\n  .md-nav__item--section > .md-nav__link[for] {\n    color: var(--md-default-fg-color--light);\n  }\n  .md-nav__item--section > .md-nav__link:not(.md-nav__container) {\n    pointer-events: none;\n  }\n  .md-nav__item--section > .md-nav__link > [for],\n  .md-nav__item--section > .md-nav__link .md-icon {\n    display: none;\n  }\n  .md-nav__item--section > .md-nav {\n    display: block;\n    margin-inline-start: -0.6rem;\n    visibility: visible;\n    opacity: 1;\n  }\n  .md-nav__item--section > .md-nav > .md-nav__list > .md-nav__item {\n    padding: 0;\n  }\n  .md-nav__icon {\n    width: 0.9rem;\n    height: 0.9rem;\n    border-radius: 100%;\n    transition: background-color 250ms;\n  }\n  .md-nav__icon:hover {\n    background-color: var(--md-accent-fg-color--transparent);\n  }\n  .md-nav__icon::after {\n    display: inline-block;\n    width: 100%;\n    height: 100%;\n    vertical-align: -0.1rem;\n    content: \"\";\n    background-color: currentcolor;\n    border-radius: 100%;\n    mask-image: var(--md-nav-icon--next);\n    mask-repeat: no-repeat;\n    mask-position: center;\n    mask-size: contain;\n    transition: transform 250ms;\n  }\n  [dir=rtl] .md-nav__icon::after {\n    transform: rotate(180deg);\n  }\n  .md-nav__item--nested .md-nav__toggle:checked ~ .md-nav__link .md-nav__icon::after, .md-nav__item--nested .md-toggle--indeterminate ~ .md-nav__link .md-nav__icon::after {\n    transform: rotate(90deg);\n  }\n  .md-nav--lifted > .md-nav__title {\n    display: none;\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item {\n    display: none;\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item--active {\n    display: block;\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item--active > .md-nav__link {\n    position: sticky;\n    top: 0;\n    z-index: var(--md-nav__sticky-zindex, 1);\n    margin-top: 0;\n    background: var(--md-default-bg-color);\n    box-shadow: 0 0 0.4rem 0.4rem var(--md-default-bg-color);\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item--active > .md-nav__link:not(.md-nav__container) {\n    pointer-events: none;\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item--active.md-nav__item--section {\n    margin: 0;\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item > .md-nav:not(.md-nav--secondary) {\n    margin-inline-start: -0.6rem;\n  }\n  .md-nav--lifted > .md-nav__list > .md-nav__item > [for] {\n    color: var(--md-default-fg-color--light);\n  }\n  .md-nav--lifted .md-nav[data-md-level=\"1\"] {\n    grid-template-rows: minmax(0.4rem, 1fr);\n    visibility: visible;\n    opacity: 1;\n  }\n  .md-nav--integrated > .md-nav__list > .md-nav__item--active .md-nav--secondary {\n    display: block;\n    margin-bottom: 1.25em;\n    visibility: visible;\n    border-inline-start: 0.05rem solid var(--md-primary-fg-color);\n    opacity: 1;\n  }\n  .md-nav--integrated > .md-nav__list > .md-nav__item--active .md-nav--secondary > .md-nav__list {\n    padding-bottom: 0;\n    overflow: visible;\n  }\n  .md-nav--integrated > .md-nav__list > .md-nav__item--active .md-nav--secondary > .md-nav__title {\n    display: none;\n  }\n}\n\n.md-pagination {\n  display: flex;\n  gap: 0.4rem;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.8rem;\n  font-weight: 700;\n}\n.md-pagination > * {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 1.8rem;\n  height: 1.8rem;\n  text-align: center;\n  border-radius: 0.2rem;\n}\n.md-pagination__current {\n  color: var(--md-default-fg-color--light);\n  background-color: var(--md-default-fg-color--lightest);\n}\n.md-pagination__link {\n  transition: color 125ms, background-color 125ms;\n}\n.md-pagination__link:is(:focus, :hover) {\n  color: var(--md-accent-fg-color);\n  background-color: var(--md-accent-fg-color--transparent);\n}\n.md-pagination__link:is(:focus, :hover) svg {\n  color: var(--md-accent-fg-color);\n}\n.md-pagination__link.focus-visible {\n  outline-color: var(--md-accent-fg-color);\n  outline-offset: 0.2rem;\n}\n.md-pagination__link svg {\n  display: block;\n  width: 1.2rem;\n  max-height: 100%;\n  color: var(--md-default-fg-color--lighter);\n  fill: currentcolor;\n}\n\n.md-post__back {\n  padding-bottom: 1.2rem;\n  margin-bottom: 1.2rem;\n  border-bottom: 0.05rem solid var(--md-default-fg-color--lightest);\n}\n@media screen and (max-width: 76.234375em) {\n  .md-post__back {\n    display: none;\n  }\n}\n[dir=rtl] .md-post__back svg {\n  transform: scaleX(-1);\n}\n.md-post__authors {\n  display: flex;\n  flex-direction: column;\n  gap: 0.6rem;\n  margin: 0 0.6rem 1.2rem;\n}\n.md-post .md-post__meta a {\n  transition: color 125ms;\n}\n.md-post .md-post__meta a:is(:focus, :hover) {\n  color: var(--md-accent-fg-color);\n}\n.md-post__title {\n  font-weight: 700;\n  color: var(--md-default-fg-color--light);\n}\n.md-post--excerpt {\n  margin-bottom: 3.2rem;\n}\n.md-post--excerpt .md-post__header {\n  display: flex;\n  gap: 0.6rem;\n  align-items: center;\n  min-height: 1.6rem;\n}\n.md-post--excerpt .md-post__authors {\n  display: inline-flex;\n  flex-direction: row;\n  gap: 0.2rem;\n  align-items: center;\n  min-height: 2.4rem;\n  margin: 0;\n}\n.md-post--excerpt .md-post__meta .md-meta__list {\n  margin-inline-end: 0.4rem;\n}\n.md-post--excerpt .md-post__content > :first-child {\n  --md-scroll-margin: 6rem;\n  margin-top: 0;\n}\n.md-post > .md-nav--secondary {\n  margin: 1em 0;\n}\n\n.md-profile {\n  display: flex;\n  gap: 0.6rem;\n  align-items: center;\n  width: 100%;\n  font-size: 0.7rem;\n  line-height: 1.4;\n}\n.md-profile__description {\n  flex-grow: 1;\n}\n\n.md-content--post {\n  display: flex;\n}\n@media screen and (max-width: 76.234375em) {\n  .md-content--post {\n    flex-flow: column-reverse;\n  }\n}\n.md-content--post > .md-content__inner {\n  flex-grow: 1;\n  min-width: 0;\n}\n@media screen and (min-width: 76.25em) {\n  .md-content--post > .md-content__inner {\n    margin-inline-start: 1.2rem;\n  }\n}\n\n@media screen and (max-width: 76.234375em) {\n  .md-sidebar.md-sidebar--post {\n    position: initial;\n    width: 100%;\n    padding: 0;\n  }\n  .md-sidebar.md-sidebar--post .md-sidebar__scrollwrap {\n    overflow: visible;\n  }\n  .md-sidebar.md-sidebar--post .md-sidebar__inner {\n    padding: 0;\n  }\n  .md-sidebar.md-sidebar--post .md-post__meta {\n    margin-inline: 0.6rem;\n  }\n  .md-sidebar.md-sidebar--post .md-nav__item {\n    display: inline;\n    border: none;\n  }\n  .md-sidebar.md-sidebar--post .md-nav__list {\n    display: inline-flex;\n    flex-wrap: wrap;\n    gap: 0.6rem;\n    padding-block: 0.6rem;\n  }\n  .md-sidebar.md-sidebar--post .md-nav__link {\n    padding: 0;\n  }\n  .md-sidebar.md-sidebar--post .md-nav {\n    position: initial;\n    height: auto;\n    margin-bottom: 0;\n  }\n}\n\n:root {\n  --md-progress-value: 0;\n  --md-progress-delay: 400ms;\n}\n\n.md-progress {\n  position: fixed;\n  top: 0;\n  z-index: 4;\n  width: 100%;\n  height: 0.075rem;\n  background: var(--md-primary-bg-color);\n  opacity: min(clamp(0, var(--md-progress-value), 1), clamp(0, 100 - var(--md-progress-value), 1));\n  transition: transform 500ms cubic-bezier(0.19, 1, 0.22, 1), opacity 250ms var(--md-progress-delay);\n  transform: scaleX(calc(var(--md-progress-value) * 1%));\n  transform-origin: left;\n}\n\n:root {\n  --md-search-result-icon: svg-load(\"material/file-search-outline.svg\");\n}\n\n.md-search {\n  position: relative;\n}\n@media screen and (min-width: 60em) {\n  .md-search {\n    padding: 0.2rem 0;\n  }\n}\n.no-js .md-search {\n  display: none;\n}\n.md-search__overlay {\n  z-index: 1;\n  opacity: 0;\n}\n@media screen and (max-width: 59.984375em) {\n  .md-search__overlay {\n    position: absolute;\n    inset-inline-start: -2.2rem;\n    top: -1rem;\n    width: 2rem;\n    height: 2rem;\n    overflow: hidden;\n    pointer-events: none;\n    background-color: var(--md-default-bg-color);\n    border-radius: 1rem;\n    transition: transform 300ms 100ms, opacity 200ms 200ms;\n    transform-origin: center;\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__overlay {\n    opacity: 1;\n    transition: transform 400ms, opacity 100ms;\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-search__overlay {\n    position: fixed;\n    inset-inline-start: 0;\n    top: 0;\n    width: 0;\n    height: 0;\n    cursor: pointer;\n    background-color: hsla(0, 0%, 0%, 0.54);\n    transition: width 0ms 250ms, height 0ms 250ms, opacity 250ms;\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__overlay {\n    width: 100%;\n    height: 200vh;\n    opacity: 1;\n    transition: width 0ms, height 0ms, opacity 250ms;\n  }\n}\n@media screen and (max-width: 29.984375em) {\n  [data-md-toggle=search]:checked ~ .md-header .md-search__overlay {\n    transform: scale(45);\n  }\n}\n@media screen and (min-width: 30em) and (max-width: 44.984375em) {\n  [data-md-toggle=search]:checked ~ .md-header .md-search__overlay {\n    transform: scale(60);\n  }\n}\n@media screen and (min-width: 45em) and (max-width: 59.984375em) {\n  [data-md-toggle=search]:checked ~ .md-header .md-search__overlay {\n    transform: scale(75);\n  }\n}\n.md-search__inner {\n  backface-visibility: hidden;\n}\n@media screen and (max-width: 59.984375em) {\n  .md-search__inner {\n    position: fixed;\n    inset-inline-start: 0;\n    top: 0;\n    z-index: 2;\n    width: 0;\n    height: 0;\n    overflow: hidden;\n    opacity: 0;\n    transition: width 0ms 300ms, height 0ms 300ms, transform 150ms 150ms cubic-bezier(0.4, 0, 0.2, 1), opacity 150ms 150ms;\n    transform: translateX(5%);\n  }\n  [dir=rtl] .md-search__inner {\n    transform: translateX(-5%);\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__inner {\n    width: 100%;\n    height: 100%;\n    opacity: 1;\n    transition: width 0ms 0ms, height 0ms 0ms, transform 150ms 150ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 150ms 150ms;\n    transform: translateX(0);\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-search__inner {\n    position: relative;\n    float: inline-end;\n    width: 11.7rem;\n    padding: 0.1rem 0;\n    transition: width 250ms cubic-bezier(0.1, 0.7, 0.1, 1);\n  }\n}\n@media screen and (min-width: 60em) and (max-width: 76.234375em) {\n  [data-md-toggle=search]:checked ~ .md-header .md-search__inner {\n    width: 23.4rem;\n  }\n}\n@media screen and (min-width: 76.25em) {\n  [data-md-toggle=search]:checked ~ .md-header .md-search__inner {\n    width: 34.4rem;\n  }\n}\n.md-search__form {\n  position: relative;\n  z-index: 2;\n  height: 2.4rem;\n  background-color: var(--md-default-bg-color);\n  box-shadow: 0 0 0.6rem transparent;\n  transition: color 250ms, background-color 250ms;\n}\n@media screen and (min-width: 60em) {\n  .md-search__form {\n    height: 1.8rem;\n    background-color: hsla(0, 0%, 0%, 0.26);\n    border-radius: 0.1rem;\n  }\n  .md-search__form:hover {\n    background-color: hsla(0, 0%, 100%, 0.12);\n  }\n}\n[data-md-toggle=search]:checked ~ .md-header .md-search__form {\n  color: var(--md-default-fg-color);\n  background-color: var(--md-default-bg-color);\n  border-radius: 0.1rem 0.1rem 0 0;\n  box-shadow: 0 0 0.6rem hsla(0, 0%, 0%, 0.07);\n}\n.md-search__input {\n  position: relative;\n  z-index: 2;\n  width: 100%;\n  height: 100%;\n  padding-inline: 3.6rem 2.2rem;\n  font-size: 0.9rem;\n  text-overflow: ellipsis;\n  background: transparent;\n}\n.md-search__input::placeholder {\n  transition: color 250ms;\n}\n.md-search__input ~ .md-search__icon, .md-search__input::placeholder {\n  color: var(--md-default-fg-color--light);\n}\n.md-search__input::-ms-clear {\n  display: none;\n}\n@media screen and (max-width: 59.984375em) {\n  .md-search__input {\n    width: 100%;\n    height: 2.4rem;\n    font-size: 0.9rem;\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-search__input {\n    padding-inline-start: 2.2rem;\n    font-size: 0.8rem;\n    color: inherit;\n  }\n  .md-search__input::placeholder {\n    color: var(--md-primary-bg-color--light);\n  }\n  .md-search__input + .md-search__icon {\n    color: var(--md-primary-bg-color);\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__input {\n    text-overflow: clip;\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__input + .md-search__icon {\n    color: var(--md-default-fg-color--light);\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__input::placeholder {\n    color: transparent;\n  }\n}\n.md-search__icon {\n  display: inline-block;\n  width: 1.2rem;\n  height: 1.2rem;\n  cursor: pointer;\n  transition: color 250ms, opacity 250ms;\n}\n.md-search__icon:hover {\n  opacity: 0.7;\n}\n.md-search__icon[for=__search] {\n  position: absolute;\n  inset-inline-start: 0.5rem;\n  top: 0.3rem;\n  z-index: 2;\n}\n[dir=rtl] .md-search__icon[for=__search] svg {\n  transform: scaleX(-1);\n}\n@media screen and (max-width: 59.984375em) {\n  .md-search__icon[for=__search] {\n    inset-inline-start: 0.8rem;\n    top: 0.6rem;\n  }\n  .md-search__icon[for=__search] svg:first-child {\n    display: none;\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-search__icon[for=__search] {\n    pointer-events: none;\n  }\n  .md-search__icon[for=__search] svg:last-child {\n    display: none;\n  }\n}\n.md-search__options {\n  position: absolute;\n  inset-inline-end: 0.5rem;\n  top: 0.3rem;\n  z-index: 2;\n  pointer-events: none;\n}\n@media screen and (max-width: 59.984375em) {\n  .md-search__options {\n    inset-inline-end: 0.8rem;\n    top: 0.6rem;\n  }\n}\n.md-search__options > .md-icon {\n  margin-inline-start: 0.2rem;\n  color: var(--md-default-fg-color--light);\n  opacity: 0;\n  transition: transform 150ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 150ms;\n  transform: scale(0.75);\n}\n.md-search__options > .md-icon:not(.focus-visible) {\n  outline: none;\n  -webkit-tap-highlight-color: transparent;\n}\n[data-md-toggle=search]:checked ~ .md-header .md-search__input:valid ~ .md-search__options > .md-icon {\n  pointer-events: initial;\n  opacity: 1;\n  transform: scale(1);\n}\n[data-md-toggle=search]:checked ~ .md-header .md-search__input:valid ~ .md-search__options > .md-icon:hover {\n  opacity: 0.7;\n}\n.md-search__suggest {\n  position: absolute;\n  top: 0;\n  display: flex;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  padding-inline: 3.6rem 2.2rem;\n  font-size: 0.9rem;\n  color: var(--md-default-fg-color--lighter);\n  white-space: nowrap;\n  opacity: 0;\n  transition: opacity 50ms;\n}\n@media screen and (min-width: 60em) {\n  .md-search__suggest {\n    padding-inline-start: 2.2rem;\n    font-size: 0.8rem;\n  }\n}\n[data-md-toggle=search]:checked ~ .md-header .md-search__suggest {\n  opacity: 1;\n  transition: opacity 300ms 100ms;\n}\n.md-search__output {\n  position: absolute;\n  z-index: 1;\n  width: 100%;\n  overflow: hidden;\n  border-end-start-radius: 0.1rem;\n  border-end-end-radius: 0.1rem;\n}\n@media screen and (max-width: 59.984375em) {\n  .md-search__output {\n    top: 2.4rem;\n    bottom: 0;\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-search__output {\n    top: 1.9rem;\n    opacity: 0;\n    transition: opacity 400ms;\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__output {\n    box-shadow: var(--md-shadow-z3);\n    opacity: 1;\n  }\n}\n.md-search__scrollwrap {\n  height: 100%;\n  overflow-y: auto;\n  touch-action: pan-y;\n  background-color: var(--md-default-bg-color);\n  backface-visibility: hidden;\n}\n@media (max-resolution: 1dppx) {\n  .md-search__scrollwrap {\n    transform: translateZ(0);\n  }\n}\n@media screen and (min-width: 60em) and (max-width: 76.234375em) {\n  .md-search__scrollwrap {\n    width: 23.4rem;\n  }\n}\n@media screen and (min-width: 76.25em) {\n  .md-search__scrollwrap {\n    width: 34.4rem;\n  }\n}\n@media screen and (min-width: 60em) {\n  .md-search__scrollwrap {\n    max-height: 0;\n    scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n    scrollbar-width: thin;\n  }\n  [data-md-toggle=search]:checked ~ .md-header .md-search__scrollwrap {\n    max-height: 75vh;\n  }\n  .md-search__scrollwrap:hover {\n    scrollbar-color: var(--md-accent-fg-color) transparent;\n  }\n  .md-search__scrollwrap::-webkit-scrollbar {\n    width: 0.2rem;\n    height: 0.2rem;\n  }\n  .md-search__scrollwrap::-webkit-scrollbar-thumb {\n    background-color: var(--md-default-fg-color--lighter);\n  }\n  .md-search__scrollwrap::-webkit-scrollbar-thumb:hover {\n    background-color: var(--md-accent-fg-color);\n  }\n}\n\n.md-search-result {\n  color: var(--md-default-fg-color);\n  word-break: break-word;\n}\n.md-search-result__meta {\n  padding: 0 0.8rem;\n  font-size: 0.64rem;\n  line-height: 1.8rem;\n  color: var(--md-default-fg-color--light);\n  scroll-snap-align: start;\n  background-color: var(--md-default-fg-color--lightest);\n}\n@media screen and (min-width: 60em) {\n  .md-search-result__meta {\n    padding-inline-start: 2.2rem;\n  }\n}\n.md-search-result__list {\n  padding: 0;\n  margin: 0;\n  list-style: none;\n  user-select: none;\n}\n.md-search-result__item {\n  box-shadow: 0 -0.05rem var(--md-default-fg-color--lightest);\n}\n.md-search-result__item:first-child {\n  box-shadow: none;\n}\n.md-search-result__link {\n  display: block;\n  scroll-snap-align: start;\n  outline: none;\n  transition: background-color 250ms;\n}\n.md-search-result__link:is(:focus, :hover) {\n  background-color: var(--md-accent-fg-color--transparent);\n}\n.md-search-result__link:last-child p:last-child {\n  margin-bottom: 0.6rem;\n}\n.md-search-result__more > summary {\n  position: sticky;\n  top: 0;\n  z-index: 1;\n  display: block;\n  cursor: pointer;\n  scroll-snap-align: start;\n  outline: none;\n}\n.md-search-result__more > summary::marker {\n  display: none;\n}\n.md-search-result__more > summary::-webkit-details-marker {\n  display: none;\n}\n.md-search-result__more > summary > div {\n  padding: 0.75em 0.8rem;\n  font-size: 0.64rem;\n  color: var(--md-typeset-a-color);\n  transition: color 250ms, background-color 250ms;\n}\n@media screen and (min-width: 60em) {\n  .md-search-result__more > summary > div {\n    padding-inline-start: 2.2rem;\n  }\n}\n.md-search-result__more > summary:is(:focus, :hover) > div {\n  color: var(--md-accent-fg-color);\n  background-color: var(--md-accent-fg-color--transparent);\n}\n.md-search-result__more[open] > summary {\n  background-color: var(--md-default-bg-color);\n}\n.md-search-result__article {\n  position: relative;\n  padding: 0 0.8rem;\n  overflow: hidden;\n}\n@media screen and (min-width: 60em) {\n  .md-search-result__article {\n    padding-inline-start: 2.2rem;\n  }\n}\n.md-search-result__icon {\n  position: absolute;\n  inset-inline-start: 0;\n  width: 1.2rem;\n  height: 1.2rem;\n  margin: 0.5rem;\n  color: var(--md-default-fg-color--light);\n}\n@media screen and (max-width: 59.984375em) {\n  .md-search-result__icon {\n    display: none;\n  }\n}\n.md-search-result__icon::after {\n  display: inline-block;\n  width: 100%;\n  height: 100%;\n  content: \"\";\n  background-color: currentcolor;\n  mask-image: var(--md-search-result-icon);\n  mask-repeat: no-repeat;\n  mask-position: center;\n  mask-size: contain;\n}\n[dir=rtl] .md-search-result__icon::after {\n  transform: scaleX(-1);\n}\n.md-search-result .md-typeset {\n  font-size: 0.64rem;\n  line-height: 1.6;\n  color: var(--md-default-fg-color--light);\n}\n.md-search-result .md-typeset h1 {\n  margin: 0.55rem 0;\n  font-size: 0.8rem;\n  font-weight: 400;\n  line-height: 1.4;\n  color: var(--md-default-fg-color);\n}\n.md-search-result .md-typeset h1 mark {\n  text-decoration: none;\n}\n.md-search-result .md-typeset h2 {\n  margin: 0.5em 0;\n  font-size: 0.64rem;\n  font-weight: 700;\n  line-height: 1.6;\n  color: var(--md-default-fg-color);\n}\n.md-search-result .md-typeset h2 mark {\n  text-decoration: none;\n}\n.md-search-result__terms {\n  display: block;\n  margin: 0.5em 0;\n  font-size: 0.64rem;\n  font-style: italic;\n  color: var(--md-default-fg-color);\n}\n.md-search-result mark {\n  color: var(--md-accent-fg-color);\n  text-decoration: underline;\n  background-color: transparent;\n}\n\n.md-select {\n  position: relative;\n  z-index: 1;\n}\n.md-select__inner {\n  position: absolute;\n  top: calc(100% - 0.2rem);\n  left: 50%;\n  max-height: 0;\n  margin-top: 0.2rem;\n  color: var(--md-default-fg-color);\n  background-color: var(--md-default-bg-color);\n  border-radius: 0.1rem;\n  box-shadow: var(--md-shadow-z2);\n  opacity: 0;\n  transition: transform 250ms 375ms, opacity 250ms 250ms, max-height 0ms 500ms;\n  transform: translate3d(-50%, 0.3rem, 0);\n}\n.md-select:is(:focus-within, :hover) .md-select__inner {\n  max-height: 10rem;\n  opacity: 1;\n  transition: transform 250ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 250ms, max-height 0ms;\n  transform: translate3d(-50%, 0, 0);\n}\n.md-select__inner::after {\n  position: absolute;\n  top: 0;\n  left: 50%;\n  width: 0;\n  height: 0;\n  margin-top: -0.2rem;\n  margin-left: -0.2rem;\n  content: \"\";\n  border: 0.2rem solid transparent;\n  border-top: 0;\n  border-bottom-color: var(--md-default-bg-color);\n}\n.md-select__list {\n  max-height: inherit;\n  padding: 0;\n  margin: 0;\n  overflow: auto;\n  font-size: 0.8rem;\n  list-style-type: none;\n  border-radius: 0.1rem;\n}\n.md-select__item {\n  line-height: 1.8rem;\n}\n.md-select__link {\n  display: block;\n  width: 100%;\n  padding-inline: 0.6rem 1.2rem;\n  cursor: pointer;\n  scroll-snap-align: start;\n  outline: none;\n  transition: background-color 250ms, color 250ms;\n}\n.md-select__link:is(:focus, :hover) {\n  color: var(--md-accent-fg-color);\n}\n.md-select__link:focus {\n  background-color: var(--md-default-fg-color--lightest);\n}\n\n.md-sidebar {\n  position: sticky;\n  top: 2.4rem;\n  flex-shrink: 0;\n  align-self: flex-start;\n  width: 12.1rem;\n  padding: 1.2rem 0;\n}\n@media print {\n  .md-sidebar {\n    display: none;\n  }\n}\n@media screen and (max-width: 76.234375em) {\n  .md-sidebar--primary {\n    position: fixed;\n    inset-inline-start: -12.1rem;\n    top: 0;\n    z-index: 5;\n    display: block;\n    width: 12.1rem;\n    height: 100%;\n    background-color: var(--md-default-bg-color);\n    transition: transform 250ms cubic-bezier(0.4, 0, 0.2, 1), box-shadow 250ms;\n    transform: translateX(0);\n  }\n  [data-md-toggle=drawer]:checked ~ .md-container .md-sidebar--primary {\n    box-shadow: var(--md-shadow-z3);\n    transform: translateX(12.1rem);\n  }\n  [dir=rtl] [data-md-toggle=drawer]:checked ~ .md-container .md-sidebar--primary {\n    transform: translateX(-12.1rem);\n  }\n  .md-sidebar--primary .md-sidebar__scrollwrap {\n    position: absolute;\n    inset: 0;\n    margin: 0;\n    overflow: hidden;\n    scroll-snap-type: none;\n  }\n}\n@media screen and (min-width: 76.25em) {\n  .md-sidebar {\n    height: 0;\n  }\n  .no-js .md-sidebar {\n    height: auto;\n  }\n  .md-header--lifted ~ .md-container .md-sidebar {\n    top: 4.8rem;\n  }\n}\n.md-sidebar--secondary {\n  display: none;\n  order: 2;\n}\n@media screen and (min-width: 60em) {\n  .md-sidebar--secondary {\n    height: 0;\n  }\n  .no-js .md-sidebar--secondary {\n    height: auto;\n  }\n  .md-sidebar--secondary:not([hidden]) {\n    display: block;\n  }\n  .md-sidebar--secondary .md-sidebar__scrollwrap {\n    touch-action: pan-y;\n  }\n}\n.md-sidebar__scrollwrap {\n  margin: 0 0.2rem;\n  overflow-y: auto;\n  scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n  scrollbar-gutter: stable;\n  scrollbar-width: thin;\n  backface-visibility: hidden;\n}\n.md-sidebar__scrollwrap::-webkit-scrollbar {\n  width: 0.2rem;\n  height: 0.2rem;\n}\n.md-sidebar__scrollwrap:is(:focus-within, :hover) {\n  scrollbar-color: var(--md-accent-fg-color) transparent;\n}\n.md-sidebar__scrollwrap:is(:focus-within, :hover)::-webkit-scrollbar-thumb {\n  background-color: var(--md-default-fg-color--lighter);\n}\n.md-sidebar__scrollwrap:is(:focus-within, :hover)::-webkit-scrollbar-thumb:hover {\n  background-color: var(--md-accent-fg-color);\n}\n@supports selector(::-webkit-scrollbar) {\n  .md-sidebar__scrollwrap {\n    scrollbar-gutter: auto;\n  }\n  .md-sidebar__inner {\n    padding-inline-end: calc(100% - 11.5rem);\n  }\n}\n\n@media screen and (max-width: 76.234375em) {\n  .md-overlay {\n    position: fixed;\n    top: 0;\n    z-index: 5;\n    width: 0;\n    height: 0;\n    background-color: hsla(0, 0%, 0%, 0.54);\n    opacity: 0;\n    transition: width 0ms 250ms, height 0ms 250ms, opacity 250ms;\n  }\n  [data-md-toggle=drawer]:checked ~ .md-overlay {\n    width: 100%;\n    height: 100%;\n    opacity: 1;\n    transition: width 0ms, height 0ms, opacity 250ms;\n  }\n}\n@keyframes facts {\n  0% {\n    height: 0;\n  }\n  100% {\n    height: 0.65rem;\n  }\n}\n@keyframes fact {\n  0% {\n    opacity: 0;\n    transform: translateY(100%);\n  }\n  50% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0%);\n  }\n}\n:root {\n  --md-source-forks-icon: svg-load(\"octicons/repo-forked-16.svg\");\n  --md-source-repositories-icon: svg-load(\"octicons/repo-16.svg\");\n  --md-source-stars-icon: svg-load(\"octicons/star-16.svg\");\n  --md-source-version-icon: svg-load(\"octicons/tag-16.svg\");\n}\n\n.md-source {\n  display: block;\n  font-size: 0.65rem;\n  line-height: 1.2;\n  white-space: nowrap;\n  outline-color: var(--md-accent-fg-color);\n  backface-visibility: hidden;\n  transition: opacity 250ms;\n}\n.md-source:hover {\n  opacity: 0.7;\n}\n.md-source__icon {\n  display: inline-block;\n  width: 2rem;\n  height: 2.4rem;\n  vertical-align: middle;\n}\n.md-source__icon svg {\n  margin-inline-start: 0.6rem;\n  margin-top: 0.6rem;\n}\n.md-source__icon + .md-source__repository {\n  padding-inline-start: 2rem;\n  margin-inline-start: -2rem;\n}\n.md-source__repository {\n  display: inline-block;\n  max-width: calc(100% - 1.2rem);\n  margin-inline-start: 0.6rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n.md-source__facts {\n  display: flex;\n  gap: 0.4rem;\n  width: 100%;\n  padding: 0;\n  margin: 0.1rem 0 0;\n  overflow: hidden;\n  font-size: 0.55rem;\n  list-style-type: none;\n  opacity: 0.75;\n}\n.md-source__repository--active .md-source__facts {\n  animation: facts 250ms ease-in;\n}\n.md-source__fact {\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.md-source__repository--active .md-source__fact {\n  animation: fact 400ms ease-out;\n}\n.md-source__fact::before {\n  display: inline-block;\n  width: 0.6rem;\n  height: 0.6rem;\n  margin-inline-end: 0.1rem;\n  vertical-align: text-top;\n  content: \"\";\n  background-color: currentcolor;\n  mask-repeat: no-repeat;\n  mask-position: center;\n  mask-size: contain;\n}\n.md-source__fact:nth-child(1n+2) {\n  flex-shrink: 0;\n}\n.md-source__fact--version::before {\n  mask-image: var(--md-source-version-icon);\n}\n.md-source__fact--stars::before {\n  mask-image: var(--md-source-stars-icon);\n}\n.md-source__fact--forks::before {\n  mask-image: var(--md-source-forks-icon);\n}\n.md-source__fact--repositories::before {\n  mask-image: var(--md-source-repositories-icon);\n}\n\n.md-source-file {\n  margin: 1em 0;\n}\n.md-source-file__fact {\n  display: inline-flex;\n  gap: 0.3rem;\n  align-items: center;\n  margin-inline-end: 0.6rem;\n  font-size: 0.68rem;\n  color: var(--md-default-fg-color--light);\n}\n.md-source-file__fact .md-icon {\n  flex-shrink: 0;\n  margin-bottom: 0.05rem;\n}\n.md-source-file__fact .md-author {\n  float: inline-start;\n  margin-right: 0.2rem;\n}\n.md-source-file__fact svg {\n  width: 0.9rem;\n}\n\n/* stylelint-disable scss/comment-no-loud */\n/* sphinx-immaterial: exclude default status icons,\n   they are defined by custom_admonitions.css instead\n\n:root {\n  --md-status: svg-load(\"material/information-outline.svg\");\n  --md-status--new: svg-load(\"material/alert-decagram.svg\");\n  --md-status--deprecated: svg-load(\"material/trash-can.svg\");\n  --md-status--encrypted: svg-load(\"material/shield-lock.svg\");\n}\n\nsphinx-immaterial: end exclude default status icons */\n.md-status {\n  /* sphinx-immaterial: exclude default status icons\n\n  // Status: new\n  &--new::after {\n    mask-image: var(--md-status--new);\n  }\n\n  // Status: deprecated\n  &--deprecated::after {\n    mask-image: var(--md-status--deprecated);\n  }\n\n  // Status: encrypted\n  &--encrypted::after {\n    mask-image: var(--md-status--encrypted);\n  }\n\n  sphinx-immaterial: end exclude default status icons */\n}\n.md-status::after {\n  display: inline-block;\n  width: 1.125em;\n  height: 1.125em;\n  vertical-align: text-bottom;\n  content: \"\";\n  background-color: var(--md-default-fg-color--light);\n  /* sphinx-immaterial: exclude default status icon,\n     all status icons should be defined\n  mask-image: var(--md-status);\n     sphinx_immaterial: end exclude default status icon */\n  mask-repeat: no-repeat;\n  mask-position: center;\n  mask-size: contain;\n}\n.md-status:hover::after {\n  background-color: currentcolor;\n}\n\n.md-tabs {\n  z-index: 3;\n  display: block;\n  width: 100%;\n  overflow: auto;\n  line-height: 1.3;\n  color: var(--md-primary-bg-color);\n  background-color: var(--md-primary-fg-color);\n}\n@media print {\n  .md-tabs {\n    display: none;\n  }\n}\n@media screen and (max-width: 76.234375em) {\n  .md-tabs {\n    display: none;\n  }\n}\n.md-tabs[hidden] {\n  pointer-events: none;\n}\n.md-tabs__list {\n  display: flex;\n  padding: 0;\n  margin: 0;\n  margin-inline-start: 0.2rem;\n  overflow: auto;\n  white-space: nowrap;\n  list-style: none;\n  contain: content;\n  scrollbar-width: none;\n}\n.md-tabs__list::-webkit-scrollbar {\n  display: none;\n}\n.md-tabs__item {\n  height: 2.4rem;\n  padding-inline: 0.6rem;\n}\n.md-tabs__item--active .md-tabs__link {\n  color: inherit;\n  opacity: 1;\n}\n.md-tabs__link {\n  display: flex;\n  margin-top: 0.8rem;\n  font-size: 0.7rem;\n  outline-color: var(--md-accent-fg-color);\n  outline-offset: 0.2rem;\n  backface-visibility: hidden;\n  opacity: 0.7;\n  transition: transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 250ms;\n}\n.md-tabs__link:is(:focus, :hover) {\n  color: inherit;\n  opacity: 1;\n}\n.md-tabs__link svg {\n  height: 1.3em;\n  margin-inline-end: 0.4rem;\n  fill: currentcolor;\n}\n.md-tabs__item:nth-child(2) .md-tabs__link {\n  transition-delay: 20ms;\n}\n.md-tabs__item:nth-child(3) .md-tabs__link {\n  transition-delay: 40ms;\n}\n.md-tabs__item:nth-child(4) .md-tabs__link {\n  transition-delay: 60ms;\n}\n.md-tabs__item:nth-child(5) .md-tabs__link {\n  transition-delay: 80ms;\n}\n.md-tabs__item:nth-child(6) .md-tabs__link {\n  transition-delay: 100ms;\n}\n.md-tabs__item:nth-child(7) .md-tabs__link {\n  transition-delay: 120ms;\n}\n.md-tabs__item:nth-child(8) .md-tabs__link {\n  transition-delay: 140ms;\n}\n.md-tabs__item:nth-child(9) .md-tabs__link {\n  transition-delay: 160ms;\n}\n.md-tabs__item:nth-child(10) .md-tabs__link {\n  transition-delay: 180ms;\n}\n.md-tabs__item:nth-child(11) .md-tabs__link {\n  transition-delay: 200ms;\n}\n.md-tabs__item:nth-child(12) .md-tabs__link {\n  transition-delay: 220ms;\n}\n.md-tabs__item:nth-child(13) .md-tabs__link {\n  transition-delay: 240ms;\n}\n.md-tabs__item:nth-child(14) .md-tabs__link {\n  transition-delay: 260ms;\n}\n.md-tabs__item:nth-child(15) .md-tabs__link {\n  transition-delay: 280ms;\n}\n.md-tabs__item:nth-child(16) .md-tabs__link {\n  transition-delay: 300ms;\n}\n.md-tabs[hidden] .md-tabs__link {\n  opacity: 0;\n  transition: transform 0ms 100ms, opacity 100ms;\n  transform: translateY(50%);\n}\n\n:root {\n  --md-tag-icon: svg-load(\"material/pound.svg\");\n}\n\n.md-typeset .md-tags:not([hidden]) {\n  display: inline-flex;\n  flex-wrap: wrap;\n  gap: 0.5em;\n  margin-top: -0.125em;\n  margin-bottom: 0.75em;\n}\n.md-typeset .md-tag {\n  display: inline-flex;\n  gap: 0.5em;\n  align-items: center;\n  padding: 0.3125em 0.78125em;\n  font-size: 0.64rem;\n  font-size: min(0.8em, 0.64rem);\n  font-weight: 700;\n  line-height: 1.6;\n  letter-spacing: initial;\n  background: var(--md-default-fg-color--lightest);\n  border-radius: 2.4rem;\n}\n.md-typeset .md-tag[href] {\n  color: inherit;\n  outline: none;\n  -webkit-tap-highlight-color: transparent;\n  transition: color 125ms, background-color 125ms;\n}\n.md-typeset .md-tag[href]:is(:focus, :hover) {\n  color: var(--md-accent-bg-color);\n  background-color: var(--md-accent-fg-color);\n}\n[id] > .md-typeset .md-tag {\n  vertical-align: text-top;\n}\n.md-typeset .md-tag-icon::before {\n  display: inline-block;\n  width: 1.2em;\n  height: 1.2em;\n  vertical-align: text-bottom;\n  content: \"\";\n  background-color: var(--md-default-fg-color--lighter);\n  mask-image: var(--md-tag-icon);\n  mask-repeat: no-repeat;\n  mask-position: center;\n  mask-size: contain;\n  transition: background-color 125ms;\n}\n.md-typeset .md-tag-icon[href]:is(:focus, :hover)::before {\n  background-color: var(--md-accent-bg-color);\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(0.95);\n  }\n  75% {\n    transform: scale(1);\n  }\n  100% {\n    transform: scale(0.95);\n  }\n}\n:root {\n  --md-annotation-bg-icon: svg-load(\"material/circle.svg\");\n  --md-annotation-icon: svg-load(\"material/plus-circle.svg\");\n  --md-tooltip-width: 20rem;\n}\n\n.md-tooltip {\n  position: absolute;\n  top: var(--md-tooltip-y);\n  left: clamp(var(--md-tooltip-0, 0rem) + 0.8rem, var(--md-tooltip-x), 100vw + var(--md-tooltip-0, 0rem) + 0.8rem - var(--md-tooltip-width) - 2 * 0.8rem);\n  z-index: 0;\n  width: var(--md-tooltip-width);\n  max-width: calc(100vw - 2 * 0.8rem);\n  font-family: var(--md-text-font-family);\n  color: var(--md-default-fg-color);\n  background-color: var(--md-default-bg-color);\n  border-radius: 0.1rem;\n  box-shadow: var(--md-shadow-z2);\n  opacity: 0;\n  transition: transform 0ms 250ms, opacity 250ms, z-index 250ms;\n  transform: translateY(-0.4rem);\n  backface-visibility: hidden;\n}\n.md-tooltip--active {\n  z-index: 2;\n  opacity: 1;\n  transition: transform 250ms cubic-bezier(0.1, 0.7, 0.1, 1), opacity 250ms, z-index 0ms;\n  transform: translateY(0);\n}\n.md-tooltip--inline {\n  width: auto;\n  font-weight: 700;\n  user-select: none;\n}\n.md-tooltip--inline:not(.md-tooltip--active) {\n  transform: translateY(0.2rem) scale(0.9);\n}\n.md-tooltip--inline .md-tooltip__inner {\n  padding: 0.2rem 0.4rem;\n  font-size: 0.5rem;\n}\n[hidden] + .md-tooltip--inline {\n  display: none;\n}\n:is(.focus-visible > .md-tooltip, .md-tooltip:target) {\n  outline: var(--md-accent-fg-color) auto;\n}\n.md-tooltip__inner {\n  padding: 0.8rem;\n  font-size: 0.64rem;\n}\n.md-tooltip__inner.md-typeset > :first-child {\n  margin-top: 0;\n}\n.md-tooltip__inner.md-typeset > :last-child {\n  margin-bottom: 0;\n}\n\n.md-annotation {\n  font-style: initial;\n  font-weight: 400;\n  text-align: initial;\n  white-space: normal;\n  vertical-align: text-bottom;\n  outline: none;\n}\n[dir=rtl] .md-annotation {\n  direction: rtl;\n}\ncode .md-annotation {\n  font-family: var(--md-code-font-family);\n  font-size: inherit;\n}\n.md-annotation:not([hidden]) {\n  display: inline-block;\n  line-height: 1.25;\n}\n.md-annotation__index {\n  position: relative;\n  z-index: 0;\n  display: inline-block;\n  margin-inline: 0.4ch;\n  vertical-align: text-top;\n  cursor: pointer;\n  user-select: none;\n  outline: none;\n  overflow: hidden;\n  border-radius: 0.01px;\n}\n.md-annotation .md-annotation__index {\n  transition: z-index 250ms;\n}\n@media screen {\n  .md-annotation__index {\n    width: 2.2ch;\n  }\n  [data-md-visible] > .md-annotation__index {\n    animation: pulse 2000ms infinite;\n  }\n  .md-annotation__index::before {\n    position: absolute;\n    top: -0.1ch;\n    z-index: -1;\n    width: 2.2ch;\n    height: 2.2ch;\n    content: \"\";\n    background: var(--md-default-bg-color);\n    mask-image: var(--md-annotation-bg-icon);\n    mask-repeat: no-repeat;\n    mask-position: center;\n    mask-size: contain;\n  }\n  .md-annotation__index::after {\n    position: absolute;\n    top: -0.1ch;\n    z-index: -1;\n    width: 2.2ch;\n    height: 2.2ch;\n    content: \"\";\n    background-color: var(--md-default-fg-color--lighter);\n    mask-image: var(--md-annotation-icon);\n    mask-repeat: no-repeat;\n    mask-position: center;\n    mask-size: contain;\n    transition: background-color 250ms, transform 250ms;\n    transform: scale(1.0001);\n  }\n  .md-tooltip--active + .md-annotation__index::after {\n    transform: rotate(45deg);\n  }\n  :is(.md-tooltip--active + .md-annotation__index::after, :hover > .md-annotation__index::after) {\n    background-color: var(--md-accent-fg-color);\n  }\n}\n.md-tooltip--active + .md-annotation__index {\n  z-index: 2;\n  transition-duration: 0ms;\n  animation-play-state: paused;\n}\n.md-annotation__index [data-md-annotation-id] {\n  display: inline-block;\n}\n@media print {\n  .md-annotation__index [data-md-annotation-id] {\n    padding: 0 0.6ch;\n    font-weight: 700;\n    color: var(--md-default-bg-color);\n    white-space: nowrap;\n    background: var(--md-default-fg-color--lighter);\n    border-radius: 2ch;\n  }\n  .md-annotation__index [data-md-annotation-id]::after {\n    content: attr(data-md-annotation-id);\n  }\n}\n\n.md-typeset .md-annotation-list {\n  list-style: none;\n  counter-reset: xxx;\n}\n.md-typeset .md-annotation-list li {\n  position: relative;\n}\n.md-typeset .md-annotation-list li::before {\n  position: absolute;\n  inset-inline-start: -2.125em;\n  top: 0.25em;\n  min-width: 2ch;\n  height: 2ch;\n  padding: 0 0.6ch;\n  font-size: 0.8875em;\n  font-weight: 700;\n  line-height: 1.25;\n  color: var(--md-default-bg-color);\n  text-align: center;\n  content: counter(xxx);\n  counter-increment: xxx;\n  background: var(--md-default-fg-color--lighter);\n  border-radius: 2ch;\n}\n\n:root {\n  --md-tooltip-width: 20rem;\n  --md-tooltip-tail: 0.3rem;\n}\n\n.md-tooltip2 {\n  position: absolute;\n  top: calc(var(--md-tooltip-host-y) + var(--md-tooltip-y));\n  z-index: 0;\n  inline-size: 100%;\n  font-family: var(--md-text-font-family);\n  color: var(--md-default-fg-color);\n  pointer-events: none;\n  opacity: 0;\n  transition: transform 0ms 250ms, opacity 250ms, z-index 250ms;\n  transform: translateY(-0.4rem);\n  transform-origin: calc(var(--md-tooltip-host-x) + var(--md-tooltip-x)) 0;\n  backface-visibility: hidden;\n}\n.md-tooltip2::before {\n  position: absolute;\n  left: clamp(1.5 * 0.8rem, var(--md-tooltip-host-x) + var(--md-tooltip-x) - var(--md-tooltip-tail), 100vw - 2 * var(--md-tooltip-tail) - 1.5 * 0.8rem);\n  z-index: 1;\n  display: block;\n  content: \"\";\n  border-inline: var(--md-tooltip-tail) solid transparent;\n}\n.md-tooltip2--top::before {\n  bottom: calc(-1 * var(--md-tooltip-tail) + 0.025rem);\n  filter: drop-shadow(0 1px 0 hsla(0, 0%, 0%, 0.05));\n  border-top: var(--md-tooltip-tail) solid var(--md-default-bg-color);\n}\n.md-tooltip2--bottom::before {\n  top: calc(-1 * var(--md-tooltip-tail) + 0.025rem);\n  filter: drop-shadow(0 -1px 0 hsla(0, 0%, 0%, 0.05));\n  border-bottom: var(--md-tooltip-tail) solid var(--md-default-bg-color);\n}\n.md-tooltip2--active {\n  z-index: 2;\n  opacity: 1;\n  transition: transform 400ms cubic-bezier(0, 1, 0.5, 1), opacity 250ms, z-index 0ms;\n  transform: translateY(0);\n}\n.md-tooltip2__inner {\n  position: relative;\n  left: clamp(0.8rem, var(--md-tooltip-host-x) - 0.8rem, 100vw - var(--md-tooltip-width) - 0.8rem);\n  max-width: calc(100vw - 2 * 0.8rem);\n  max-height: 40vh;\n  scrollbar-gutter: stable;\n  scrollbar-width: thin;\n  background-color: var(--md-default-bg-color);\n  border-radius: 0.1rem;\n  box-shadow: var(--md-shadow-z2);\n}\n.md-tooltip2__inner::-webkit-scrollbar {\n  width: 0.2rem;\n  height: 0.2rem;\n}\n.md-tooltip2__inner::-webkit-scrollbar-thumb {\n  background-color: var(--md-default-fg-color--lighter);\n}\n.md-tooltip2__inner::-webkit-scrollbar-thumb:hover {\n  background-color: var(--md-accent-fg-color);\n}\n[role=tooltip] > .md-tooltip2__inner {\n  left: clamp(0.8rem, var(--md-tooltip-host-x) + var(--md-tooltip-x) - var(--md-tooltip-width) / 2, 100vw - var(--md-tooltip-width) - 0.8rem);\n  width: fit-content;\n  max-width: min(100vw - 2 * 0.8rem, 400px);\n  padding: 0.2rem 0.4rem;\n  font-size: 0.5rem;\n  font-weight: 700;\n  user-select: none;\n}\n.md-tooltip2__inner.md-typeset > :first-child {\n  margin-top: 0;\n}\n.md-tooltip2__inner.md-typeset > :last-child {\n  margin-bottom: 0;\n}\n\n.md-top {\n  position: fixed;\n  top: 3.2rem;\n  z-index: 2;\n  display: block;\n  padding: 0.4rem 0.8rem;\n  margin-inline-start: 50%;\n  font-size: 0.7rem;\n  color: var(--md-default-fg-color--light);\n  cursor: pointer;\n  background-color: var(--md-default-bg-color);\n  border-radius: 1.6rem;\n  outline: none;\n  box-shadow: var(--md-shadow-z2);\n  transition: color 125ms, background-color 125ms, transform 125ms cubic-bezier(0.4, 0, 0.2, 1), opacity 125ms;\n  transform: translate(-50%, 0);\n}\n@media print {\n  .md-top {\n    display: none;\n  }\n}\n[dir=rtl] .md-top {\n  transform: translate(50%, 0);\n}\n.md-top[hidden] {\n  pointer-events: none;\n  opacity: 0;\n  transition-duration: 0ms;\n  transform: translate(-50%, 0.2rem);\n}\n[dir=rtl] .md-top[hidden] {\n  transform: translate(50%, 0.2rem);\n}\n.md-top:is(:focus, :hover) {\n  color: var(--md-accent-bg-color);\n  background-color: var(--md-accent-fg-color);\n}\n.md-top svg {\n  display: inline-block;\n  vertical-align: -0.5em;\n}\n\n@keyframes hoverfix {\n  0% {\n    pointer-events: none;\n  }\n}\n:root {\n  --md-version-icon: svg-load(\"fontawesome/solid/caret-down.svg\");\n}\n\n.md-version {\n  flex-shrink: 0;\n  height: 2.4rem;\n  font-size: 0.8rem;\n}\n.md-version__current {\n  position: relative;\n  top: 0.05rem;\n  margin-inline: 1.4rem 0.4rem;\n  color: inherit;\n  cursor: pointer;\n  outline: none;\n}\n.md-version__current::after {\n  display: inline-block;\n  width: 0.4rem;\n  height: 0.6rem;\n  margin-inline-start: 0.4rem;\n  content: \"\";\n  background-color: currentcolor;\n  mask-image: var(--md-version-icon);\n  mask-repeat: no-repeat;\n  mask-position: center;\n  mask-size: contain;\n}\n.md-version__alias {\n  margin-left: 0.3rem;\n  opacity: 0.7;\n}\n.md-version__list {\n  position: absolute;\n  top: 0.15rem;\n  z-index: 3;\n  max-height: 0;\n  padding: 0;\n  margin: 0.2rem 0.8rem;\n  overflow: auto;\n  color: var(--md-default-fg-color);\n  list-style-type: none;\n  scroll-snap-type: y mandatory;\n  background-color: var(--md-default-bg-color);\n  border-radius: 0.1rem;\n  box-shadow: var(--md-shadow-z2);\n  opacity: 0;\n  transition: max-height 0ms 500ms, opacity 250ms 250ms;\n}\n.md-version:is(:focus-within, :hover) .md-version__list {\n  max-height: 10rem;\n  opacity: 1;\n  transition: max-height 0ms, opacity 250ms;\n}\n@media (pointer: coarse), (hover: none) {\n  .md-version:hover .md-version__list {\n    animation: hoverfix 250ms forwards;\n  }\n  .md-version:focus-within .md-version__list {\n    animation: none;\n  }\n}\n.md-version__item {\n  line-height: 1.8rem;\n}\n.md-version__link {\n  display: block;\n  width: 100%;\n  padding-inline: 0.6rem 1.2rem;\n  white-space: nowrap;\n  cursor: pointer;\n  scroll-snap-align: start;\n  outline: none;\n  transition: color 250ms, background-color 250ms;\n}\n.md-version__link:is(:focus, :hover) {\n  color: var(--md-accent-fg-color);\n}\n.md-version__link:focus {\n  background-color: var(--md-default-fg-color--lightest);\n}\n\n:root {\n  --md-admonition-icon--note:\n    svg-load(\"material/pencil-circle.svg\");\n  --md-admonition-icon--abstract:\n    svg-load(\"material/clipboard-text.svg\");\n  --md-admonition-icon--info:\n    svg-load(\"material/information.svg\");\n  --md-admonition-icon--tip:\n    svg-load(\"material/fire.svg\");\n  --md-admonition-icon--success:\n    svg-load(\"material/check.svg\");\n  --md-admonition-icon--question:\n    svg-load(\"material/help-circle.svg\");\n  --md-admonition-icon--warning:\n    svg-load(\"material/alert.svg\");\n  --md-admonition-icon--failure:\n    svg-load(\"material/close.svg\");\n  --md-admonition-icon--danger:\n    svg-load(\"material/lightning-bolt-circle.svg\");\n  --md-admonition-icon--bug:\n    svg-load(\"material/shield-bug.svg\");\n  --md-admonition-icon--example:\n    svg-load(\"material/test-tube.svg\");\n  --md-admonition-icon--quote:\n    svg-load(\"material/format-quote-close.svg\");\n}\n\n.md-typeset .admonition, .md-typeset details {\n  display: flow-root;\n  padding: 0 0.6rem;\n  margin: 1.5625em 0;\n  font-size: 0.64rem;\n  color: var(--md-admonition-fg-color);\n  background-color: var(--md-admonition-bg-color);\n  border: 0.075rem solid #448aff;\n  border-radius: 0.2rem;\n  box-shadow: var(--md-shadow-z1);\n  transition: box-shadow 125ms;\n  page-break-inside: avoid;\n}\n@media print {\n  .md-typeset .admonition, .md-typeset details {\n    box-shadow: none;\n  }\n}\n.md-typeset .admonition:focus-within, .md-typeset details:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(68, 138, 255, 0.1);\n}\n.md-typeset .admonition > *, .md-typeset details > * {\n  box-sizing: border-box;\n}\n.md-typeset .admonition .admonition, .md-typeset details .admonition, .md-typeset .admonition details, .md-typeset details details {\n  margin-top: 1em;\n  margin-bottom: 1em;\n}\n.md-typeset .admonition .md-typeset__scrollwrap, .md-typeset details .md-typeset__scrollwrap {\n  margin: 1em -0.6rem;\n}\n.md-typeset .admonition .md-typeset__table, .md-typeset details .md-typeset__table {\n  padding: 0 0.6rem;\n}\n.md-typeset .admonition > .tabbed-set:only-child, .md-typeset details > .tabbed-set:only-child {\n  margin-top: 0;\n}\nhtml .md-typeset .admonition > :last-child, html .md-typeset details > :last-child {\n  margin-bottom: 0.6rem;\n}\n.md-typeset .admonition-title, .md-typeset summary {\n  position: relative;\n  padding-block: 0.4rem;\n  padding-inline: 2rem 0.6rem;\n  margin-block: 0;\n  margin-inline: -0.6rem;\n  font-weight: 700;\n  background-color: rgba(68, 138, 255, 0.1);\n  border: none;\n  border-inline-start-width: 0.2rem;\n  border-start-start-radius: 0.1rem;\n  border-start-end-radius: 0.1rem;\n}\nhtml .md-typeset .admonition-title:last-child, html .md-typeset summary:last-child {\n  margin-bottom: 0;\n}\n.md-typeset .admonition-title::before, .md-typeset summary::before {\n  position: absolute;\n  inset-inline-start: 0.6rem;\n  top: 0.625em;\n  width: 1rem;\n  height: 1rem;\n  content: \"\";\n  background-color: #448aff;\n  mask-image: var(--md-admonition-icon--note);\n  mask-repeat: no-repeat;\n  mask-position: center;\n  mask-size: contain;\n}\n.md-typeset .admonition-title code, .md-typeset summary code {\n  box-shadow: 0 0 0 0.05rem var(--md-default-fg-color--lightest);\n}\n\n.md-typeset .admonition.note, .md-typeset details.note {\n  border-color: #448aff;\n}\n.md-typeset .admonition.note:focus-within, .md-typeset details.note:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(68, 138, 255, 0.1);\n}\n\n.md-typeset .note > .admonition-title, .md-typeset .note > summary {\n  background-color: rgba(68, 138, 255, 0.1);\n}\n.md-typeset .note > .admonition-title::before, .md-typeset .note > summary::before {\n  background-color: #448aff;\n  mask-image: var(--md-admonition-icon--note);\n}\n.md-typeset .note > .admonition-title::after, .md-typeset .note > summary::after {\n  color: #448aff;\n}\n\n.md-typeset .admonition.abstract, .md-typeset details.abstract {\n  border-color: #00b0ff;\n}\n.md-typeset .admonition.abstract:focus-within, .md-typeset details.abstract:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(0, 176, 255, 0.1);\n}\n\n.md-typeset .abstract > .admonition-title, .md-typeset .abstract > summary {\n  background-color: rgba(0, 176, 255, 0.1);\n}\n.md-typeset .abstract > .admonition-title::before, .md-typeset .abstract > summary::before {\n  background-color: #00b0ff;\n  mask-image: var(--md-admonition-icon--abstract);\n}\n.md-typeset .abstract > .admonition-title::after, .md-typeset .abstract > summary::after {\n  color: #00b0ff;\n}\n\n.md-typeset .admonition.info, .md-typeset details.info {\n  border-color: #00b8d4;\n}\n.md-typeset .admonition.info:focus-within, .md-typeset details.info:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(0, 184, 212, 0.1);\n}\n\n.md-typeset .info > .admonition-title, .md-typeset .info > summary {\n  background-color: rgba(0, 184, 212, 0.1);\n}\n.md-typeset .info > .admonition-title::before, .md-typeset .info > summary::before {\n  background-color: #00b8d4;\n  mask-image: var(--md-admonition-icon--info);\n}\n.md-typeset .info > .admonition-title::after, .md-typeset .info > summary::after {\n  color: #00b8d4;\n}\n\n.md-typeset .admonition.tip, .md-typeset details.tip {\n  border-color: #00bfa5;\n}\n.md-typeset .admonition.tip:focus-within, .md-typeset details.tip:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(0, 191, 165, 0.1);\n}\n\n.md-typeset .tip > .admonition-title, .md-typeset .tip > summary {\n  background-color: rgba(0, 191, 165, 0.1);\n}\n.md-typeset .tip > .admonition-title::before, .md-typeset .tip > summary::before {\n  background-color: #00bfa5;\n  mask-image: var(--md-admonition-icon--tip);\n}\n.md-typeset .tip > .admonition-title::after, .md-typeset .tip > summary::after {\n  color: #00bfa5;\n}\n\n.md-typeset .admonition.success, .md-typeset details.success {\n  border-color: #00c853;\n}\n.md-typeset .admonition.success:focus-within, .md-typeset details.success:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(0, 200, 83, 0.1);\n}\n\n.md-typeset .success > .admonition-title, .md-typeset .success > summary {\n  background-color: rgba(0, 200, 83, 0.1);\n}\n.md-typeset .success > .admonition-title::before, .md-typeset .success > summary::before {\n  background-color: #00c853;\n  mask-image: var(--md-admonition-icon--success);\n}\n.md-typeset .success > .admonition-title::after, .md-typeset .success > summary::after {\n  color: #00c853;\n}\n\n.md-typeset .admonition.question, .md-typeset details.question {\n  border-color: #64dd17;\n}\n.md-typeset .admonition.question:focus-within, .md-typeset details.question:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(100, 221, 23, 0.1);\n}\n\n.md-typeset .question > .admonition-title, .md-typeset .question > summary {\n  background-color: rgba(100, 221, 23, 0.1);\n}\n.md-typeset .question > .admonition-title::before, .md-typeset .question > summary::before {\n  background-color: #64dd17;\n  mask-image: var(--md-admonition-icon--question);\n}\n.md-typeset .question > .admonition-title::after, .md-typeset .question > summary::after {\n  color: #64dd17;\n}\n\n.md-typeset .admonition.warning, .md-typeset details.warning {\n  border-color: #ff9100;\n}\n.md-typeset .admonition.warning:focus-within, .md-typeset details.warning:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(255, 145, 0, 0.1);\n}\n\n.md-typeset .warning > .admonition-title, .md-typeset .warning > summary {\n  background-color: rgba(255, 145, 0, 0.1);\n}\n.md-typeset .warning > .admonition-title::before, .md-typeset .warning > summary::before {\n  background-color: #ff9100;\n  mask-image: var(--md-admonition-icon--warning);\n}\n.md-typeset .warning > .admonition-title::after, .md-typeset .warning > summary::after {\n  color: #ff9100;\n}\n\n.md-typeset .admonition.failure, .md-typeset details.failure {\n  border-color: #ff5252;\n}\n.md-typeset .admonition.failure:focus-within, .md-typeset details.failure:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(255, 82, 82, 0.1);\n}\n\n.md-typeset .failure > .admonition-title, .md-typeset .failure > summary {\n  background-color: rgba(255, 82, 82, 0.1);\n}\n.md-typeset .failure > .admonition-title::before, .md-typeset .failure > summary::before {\n  background-color: #ff5252;\n  mask-image: var(--md-admonition-icon--failure);\n}\n.md-typeset .failure > .admonition-title::after, .md-typeset .failure > summary::after {\n  color: #ff5252;\n}\n\n.md-typeset .admonition.danger, .md-typeset details.danger {\n  border-color: #ff1744;\n}\n.md-typeset .admonition.danger:focus-within, .md-typeset details.danger:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(255, 23, 68, 0.1);\n}\n\n.md-typeset .danger > .admonition-title, .md-typeset .danger > summary {\n  background-color: rgba(255, 23, 68, 0.1);\n}\n.md-typeset .danger > .admonition-title::before, .md-typeset .danger > summary::before {\n  background-color: #ff1744;\n  mask-image: var(--md-admonition-icon--danger);\n}\n.md-typeset .danger > .admonition-title::after, .md-typeset .danger > summary::after {\n  color: #ff1744;\n}\n\n.md-typeset .admonition.bug, .md-typeset details.bug {\n  border-color: #f50057;\n}\n.md-typeset .admonition.bug:focus-within, .md-typeset details.bug:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(245, 0, 87, 0.1);\n}\n\n.md-typeset .bug > .admonition-title, .md-typeset .bug > summary {\n  background-color: rgba(245, 0, 87, 0.1);\n}\n.md-typeset .bug > .admonition-title::before, .md-typeset .bug > summary::before {\n  background-color: #f50057;\n  mask-image: var(--md-admonition-icon--bug);\n}\n.md-typeset .bug > .admonition-title::after, .md-typeset .bug > summary::after {\n  color: #f50057;\n}\n\n.md-typeset .admonition.example, .md-typeset details.example {\n  border-color: #7c4dff;\n}\n.md-typeset .admonition.example:focus-within, .md-typeset details.example:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(124, 77, 255, 0.1);\n}\n\n.md-typeset .example > .admonition-title, .md-typeset .example > summary {\n  background-color: rgba(124, 77, 255, 0.1);\n}\n.md-typeset .example > .admonition-title::before, .md-typeset .example > summary::before {\n  background-color: #7c4dff;\n  mask-image: var(--md-admonition-icon--example);\n}\n.md-typeset .example > .admonition-title::after, .md-typeset .example > summary::after {\n  color: #7c4dff;\n}\n\n.md-typeset .admonition.quote, .md-typeset details.quote {\n  border-color: #9e9e9e;\n}\n.md-typeset .admonition.quote:focus-within, .md-typeset details.quote:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.1);\n}\n\n.md-typeset .quote > .admonition-title, .md-typeset .quote > summary {\n  background-color: rgba(158, 158, 158, 0.1);\n}\n.md-typeset .quote > .admonition-title::before, .md-typeset .quote > summary::before {\n  background-color: #9e9e9e;\n  mask-image: var(--md-admonition-icon--quote);\n}\n.md-typeset .quote > .admonition-title::after, .md-typeset .quote > summary::after {\n  color: #9e9e9e;\n}\n\n:root {\n  --md-footnotes-icon: svg-load(\"material/keyboard-return.svg\");\n}\n\n.md-typeset .footnote {\n  font-size: 0.64rem;\n  color: var(--md-default-fg-color--light);\n}\n.md-typeset .footnote > ol {\n  margin-inline-start: 0;\n}\n.md-typeset .footnote > ol > li {\n  transition: color 125ms;\n}\n.md-typeset .footnote > ol > li:target {\n  color: var(--md-default-fg-color);\n}\n.md-typeset .footnote > ol > li:focus-within .footnote-backref {\n  opacity: 1;\n  transition: none;\n  transform: translateX(0);\n}\n.md-typeset .footnote > ol > li:is(:hover, :target) .footnote-backref {\n  opacity: 1;\n  transform: translateX(0);\n}\n.md-typeset .footnote > ol > li > :first-child {\n  margin-top: 0;\n}\n.md-typeset .footnote-ref {\n  font-size: 0.75em;\n  font-weight: 700;\n}\nhtml .md-typeset .footnote-ref {\n  outline-offset: 0.1rem;\n}\n.md-typeset [id^=\"fnref:\"]:target > .footnote-ref {\n  outline: auto;\n}\n.md-typeset .footnote-backref {\n  display: inline-block;\n  font-size: 0;\n  color: var(--md-typeset-a-color);\n  vertical-align: text-bottom;\n  opacity: 0;\n  transition: color 250ms, transform 250ms 250ms, opacity 125ms 250ms;\n  transform: translateX(0.25rem);\n}\n@media print {\n  .md-typeset .footnote-backref {\n    color: var(--md-typeset-a-color);\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n[dir=rtl] .md-typeset .footnote-backref {\n  transform: translateX(-0.25rem);\n}\n.md-typeset .footnote-backref:hover {\n  color: var(--md-accent-fg-color);\n}\n.md-typeset .footnote-backref::before {\n  display: inline-block;\n  width: 0.8rem;\n  height: 0.8rem;\n  content: \"\";\n  background-color: currentcolor;\n  mask-image: var(--md-footnotes-icon);\n  mask-repeat: no-repeat;\n  mask-position: center;\n  mask-size: contain;\n}\n[dir=rtl] .md-typeset .footnote-backref::before svg {\n  transform: scaleX(-1);\n}\n\n.md-typeset .headerlink {\n  display: inline-block;\n  margin-inline-start: 0.5rem;\n  color: var(--md-default-fg-color--lighter);\n  opacity: 0;\n  transition: color 250ms, opacity 125ms;\n}\n@media print {\n  .md-typeset .headerlink {\n    display: none;\n  }\n}\n.md-typeset :is(:hover, :target) > .headerlink,\n.md-typeset .headerlink:focus {\n  opacity: 1;\n  transition: color 250ms, opacity 125ms;\n}\n.md-typeset :target > .headerlink,\n.md-typeset .headerlink:is(:focus, :hover) {\n  color: var(--md-accent-fg-color);\n}\n.md-typeset :target {\n  --md-scroll-margin: 3.6rem;\n  --md-scroll-offset: 0rem;\n  scroll-margin-top: calc(var(--md-scroll-margin) - var(--md-scroll-offset));\n}\n@media screen and (min-width: 76.25em) {\n  .md-header--lifted ~ .md-container .md-typeset :target {\n    --md-scroll-margin: 6rem;\n  }\n}\n.md-typeset :is(h1, h2, h3):target {\n  --md-scroll-offset: 0.2rem;\n}\n.md-typeset h4:target {\n  --md-scroll-offset: 0.15rem;\n}\n\n.md-typeset div.arithmatex {\n  overflow: auto;\n}\n@media screen and (max-width: 44.984375em) {\n  .md-typeset div.arithmatex {\n    margin: 0 -0.8rem;\n  }\n  .md-typeset div.arithmatex > * {\n    width: min-content;\n  }\n}\n.md-typeset div.arithmatex > * {\n  padding: 0 0.8rem;\n  margin-inline: auto !important;\n  touch-action: auto;\n}\n.md-typeset div.arithmatex > * mjx-container {\n  margin: 0 !important;\n}\n.md-typeset div.arithmatex mjx-assistive-mml {\n  height: 0;\n}\n\n.md-typeset del.critic {\n  background-color: var(--md-typeset-del-color);\n  box-decoration-break: clone;\n}\n.md-typeset ins.critic {\n  background-color: var(--md-typeset-ins-color);\n  box-decoration-break: clone;\n}\n.md-typeset .critic.comment {\n  color: var(--md-code-hl-comment-color);\n  box-decoration-break: clone;\n}\n.md-typeset .critic.comment::before {\n  content: \"/* \";\n}\n.md-typeset .critic.comment::after {\n  content: \" */\";\n}\n.md-typeset .critic.block {\n  display: block;\n  padding-inline: 0.8rem;\n  margin: 1em 0;\n  overflow: auto;\n  box-shadow: none;\n}\n.md-typeset .critic.block > :first-child {\n  margin-top: 0.5em;\n}\n.md-typeset .critic.block > :last-child {\n  margin-bottom: 0.5em;\n}\n\n:root {\n  --md-details-icon: svg-load(\"material/chevron-right.svg\");\n}\n\n.md-typeset details {\n  display: flow-root;\n  padding-top: 0;\n  overflow: visible;\n}\n.md-typeset details[open] > summary::after {\n  transform: rotate(90deg);\n}\n.md-typeset details:not([open]) {\n  padding-bottom: 0;\n  box-shadow: none;\n}\n.md-typeset details:not([open]) > summary {\n  border-radius: 0.1rem;\n}\n.md-typeset summary {\n  display: block;\n  min-height: 1rem;\n  padding-inline-end: 1.8rem;\n  overflow: hidden;\n  cursor: pointer;\n  border-start-start-radius: 0.1rem;\n  border-start-end-radius: 0.1rem;\n}\n.md-typeset summary.focus-visible {\n  outline-color: var(--md-accent-fg-color);\n  outline-offset: 0.2rem;\n}\n.md-typeset summary:not(.focus-visible) {\n  outline: none;\n  -webkit-tap-highlight-color: transparent;\n}\n.md-typeset summary::after {\n  position: absolute;\n  inset-inline-end: 0.4rem;\n  top: 0.625em;\n  width: 1rem;\n  height: 1rem;\n  content: \"\";\n  background-color: currentcolor;\n  mask-image: var(--md-details-icon);\n  mask-repeat: no-repeat;\n  mask-position: center;\n  mask-size: contain;\n  transition: transform 250ms;\n  transform: rotate(0deg);\n}\n[dir=rtl] .md-typeset summary::after {\n  transform: rotate(180deg);\n}\n.md-typeset summary::marker {\n  display: none;\n}\n.md-typeset summary::-webkit-details-marker {\n  display: none;\n}\n\n.md-typeset :is(.emojione, .twemoji, .gemoji) {\n  --md-icon-size: 1.125em;\n  display: inline-flex;\n  height: var(--md-icon-size);\n  vertical-align: text-top;\n}\n.md-typeset :is(.emojione, .twemoji, .gemoji) svg {\n  width: var(--md-icon-size);\n  max-height: 100%;\n  fill: currentcolor;\n}\n.md-typeset :is(.lg, .xl, .xxl, .xxxl) {\n  vertical-align: text-bottom;\n}\n.md-typeset .middle {\n  vertical-align: middle;\n}\n.md-typeset .lg {\n  --md-icon-size: 1.5em;\n}\n.md-typeset .xl {\n  --md-icon-size: 2.25em;\n}\n.md-typeset .xxl {\n  --md-icon-size: 3em;\n}\n.md-typeset .xxxl {\n  --md-icon-size: 4em;\n}\n\n.highlight :is(.o, .ow) {\n  color: var(--md-code-hl-operator-color);\n}\n.highlight .p {\n  color: var(--md-code-hl-punctuation-color);\n}\n.highlight :is(.cpf, .l, .s, .sb, .sc, .s2, .si, .s1, .ss) {\n  color: var(--md-code-hl-string-color);\n}\n.highlight :is(.cp, .se, .sh, .sr, .sx) {\n  color: var(--md-code-hl-special-color);\n}\n.highlight :is(.m, .mb, .mf, .mh, .mi, .il, .mo) {\n  color: var(--md-code-hl-number-color);\n}\n.highlight :is(.k, .kd, .kn, .kp, .kr, .kt) {\n  color: var(--md-code-hl-keyword-color);\n}\n.highlight :is(.n) {\n  color: var(--md-code-hl-name-color);\n}\n.highlight :is(.kc, .no, .nb, .bp) {\n  color: var(--md-code-hl-constant-color);\n}\n.highlight :is(.nc, .ne, .nf, .nn) {\n  color: var(--md-code-hl-function-color);\n}\n.highlight :is(.nd, .ni, .nl, .nt) {\n  color: var(--md-code-hl-keyword-color);\n}\n.highlight :is(.c, .cm, .c1, .ch, .cs, .sd) {\n  color: var(--md-code-hl-comment-color);\n}\n.highlight :is(.na, .nv, .vc, .vg, .vi) {\n  color: var(--md-code-hl-variable-color);\n}\n.highlight :is(.ge, .gr, .gh, .go, .gp, .gs, .gu, .gt) {\n  color: var(--md-code-hl-generic-color);\n}\n.highlight :is(.gd, .gi) {\n  padding: 0 0.125em;\n  margin: 0 -0.125em;\n  border-radius: 0.1rem;\n}\n.highlight .gd {\n  background-color: var(--md-typeset-del-color);\n}\n.highlight .gi {\n  background-color: var(--md-typeset-ins-color);\n}\n.highlight .hll {\n  display: block;\n  padding: 0 1.1764705882em;\n  margin: 0 -1.1764705882em;\n  background-color: var(--md-code-hl-color--light);\n  box-shadow: 2px 0 0 0 var(--md-code-hl-color) inset;\n}\n.highlight span.filename {\n  position: relative;\n  display: flow-root;\n  padding: 0.6617647059em 1.1764705882em;\n  margin-top: 1em;\n  font-size: 0.85em;\n  font-weight: 700;\n  background-color: var(--md-code-bg-color);\n  border-bottom: 0.05rem solid var(--md-default-fg-color--lightest);\n  border-top-left-radius: 0.1rem;\n  border-top-right-radius: 0.1rem;\n}\n.highlight span.filename + pre {\n  margin-top: 0;\n}\n.highlight span.filename + pre > code {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n.highlight [data-linenos]::before {\n  position: sticky;\n  left: -1.1764705882em;\n  z-index: 3;\n  float: left;\n  padding-left: 1.1764705882em;\n  margin-right: 1.1764705882em;\n  margin-left: -1.1764705882em;\n  color: var(--md-default-fg-color--light);\n  content: attr(data-linenos);\n  user-select: none;\n  background-color: var(--md-code-bg-color);\n  box-shadow: -0.05rem 0 var(--md-default-fg-color--lightest) inset;\n}\n.highlight code a[id] {\n  position: absolute;\n  visibility: hidden;\n}\n.highlight code[data-md-copying] {\n  display: initial;\n}\n.highlight code[data-md-copying] .hll {\n  display: contents;\n}\n.highlight code[data-md-copying] .md-annotation {\n  display: none;\n}\n\n.highlighttable {\n  display: flow-root;\n}\n.highlighttable :is(tbody, td) {\n  display: block;\n  padding: 0;\n}\n.highlighttable tr {\n  display: flex;\n}\n.highlighttable pre {\n  margin: 0;\n}\n.highlighttable th.filename {\n  flex-grow: 1;\n  padding: 0;\n  text-align: left;\n}\n.highlighttable th.filename span.filename {\n  margin-top: 0;\n}\n.highlighttable .linenos {\n  padding: 0.7720588235em 1.1764705882em;\n  padding-right: 0;\n  font-size: 0.85em;\n  user-select: none;\n  background-color: var(--md-code-bg-color);\n  border-top-left-radius: 0.1rem;\n  border-bottom-left-radius: 0.1rem;\n}\n.highlighttable .linenodiv {\n  padding-right: 0.5882352941em;\n  box-shadow: -0.05rem 0 var(--md-default-fg-color--lightest) inset;\n}\n.highlighttable .linenodiv pre {\n  color: var(--md-default-fg-color--light);\n  text-align: right;\n}\n.highlighttable .code {\n  flex: 1;\n  min-width: 0;\n}\n\n.linenodiv a {\n  color: inherit;\n}\n\n.md-typeset .highlighttable {\n  margin: 1em 0;\n  direction: ltr;\n}\n.md-typeset .highlighttable > tbody > tr > .code > div > pre > code {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.md-typeset :is(.highlight, .highlighttable, .literal-block-wrapper, div[class^=highlight-], .results-prefix) + .result {\n  padding: 0 1em;\n  margin-top: calc(-1em + -0.125em);\n  overflow: visible;\n  border: 0.05rem solid var(--md-code-bg-color);\n  border-top-width: 0.1rem;\n  border-bottom-right-radius: 0.1rem;\n  border-bottom-left-radius: 0.1rem;\n}\n.md-typeset :is(.highlight, .highlighttable, .literal-block-wrapper, div[class^=highlight-], .results-prefix) + .result::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n.md-typeset .results .results-prefix + .result {\n  margin-top: 0;\n}\n.md-typeset .results .results-prefix {\n  padding: 0.6617647059em 1.1764705882em;\n  margin-top: -1em;\n  font-size: 0.85em;\n  font-weight: 700;\n  background-color: var(--md-code-bg-color);\n}\n\n@media screen and (max-width: 44.984375em) {\n  .md-content__inner > .highlight {\n    margin: 1em -0.8rem;\n  }\n  .md-content__inner > .highlight > .filename,\n  .md-content__inner > .highlight > pre > code {\n    border-radius: 0;\n  }\n  .md-content__inner > .highlight > .highlighttable > tbody > tr > .filename span.filename,\n  .md-content__inner > .highlight > .highlighttable > tbody > tr > .linenos,\n  .md-content__inner > .highlight > .highlighttable > tbody > tr > .code > div > pre > code {\n    border-radius: 0;\n  }\n  .md-content__inner > .highlight + .result {\n    margin-inline: -0.8rem;\n    border-inline-width: 0;\n    border-radius: 0;\n  }\n}\n.md-typeset .keys kbd:is(::before, ::after) {\n  position: relative;\n  margin: 0;\n  color: inherit;\n  -moz-osx-font-smoothing: initial;\n  -webkit-font-smoothing: initial;\n}\n.md-typeset .keys span {\n  padding: 0 0.2em;\n  color: var(--md-default-fg-color--light);\n}\n.md-typeset .keys .key-alt::before {\n  padding-right: 0.4em;\n  content: \"⎇\";\n}\n.md-typeset .keys .key-left-alt::before {\n  padding-right: 0.4em;\n  content: \"⎇\";\n}\n.md-typeset .keys .key-right-alt::before {\n  padding-right: 0.4em;\n  content: \"⎇\";\n}\n.md-typeset .keys .key-command::before {\n  padding-right: 0.4em;\n  content: \"⌘\";\n}\n.md-typeset .keys .key-left-command::before {\n  padding-right: 0.4em;\n  content: \"⌘\";\n}\n.md-typeset .keys .key-right-command::before {\n  padding-right: 0.4em;\n  content: \"⌘\";\n}\n.md-typeset .keys .key-control::before {\n  padding-right: 0.4em;\n  content: \"⌃\";\n}\n.md-typeset .keys .key-left-control::before {\n  padding-right: 0.4em;\n  content: \"⌃\";\n}\n.md-typeset .keys .key-right-control::before {\n  padding-right: 0.4em;\n  content: \"⌃\";\n}\n.md-typeset .keys .key-meta::before {\n  padding-right: 0.4em;\n  content: \"◆\";\n}\n.md-typeset .keys .key-left-meta::before {\n  padding-right: 0.4em;\n  content: \"◆\";\n}\n.md-typeset .keys .key-right-meta::before {\n  padding-right: 0.4em;\n  content: \"◆\";\n}\n.md-typeset .keys .key-option::before {\n  padding-right: 0.4em;\n  content: \"⌥\";\n}\n.md-typeset .keys .key-left-option::before {\n  padding-right: 0.4em;\n  content: \"⌥\";\n}\n.md-typeset .keys .key-right-option::before {\n  padding-right: 0.4em;\n  content: \"⌥\";\n}\n.md-typeset .keys .key-shift::before {\n  padding-right: 0.4em;\n  content: \"⇧\";\n}\n.md-typeset .keys .key-left-shift::before {\n  padding-right: 0.4em;\n  content: \"⇧\";\n}\n.md-typeset .keys .key-right-shift::before {\n  padding-right: 0.4em;\n  content: \"⇧\";\n}\n.md-typeset .keys .key-super::before {\n  padding-right: 0.4em;\n  content: \"❖\";\n}\n.md-typeset .keys .key-left-super::before {\n  padding-right: 0.4em;\n  content: \"❖\";\n}\n.md-typeset .keys .key-right-super::before {\n  padding-right: 0.4em;\n  content: \"❖\";\n}\n.md-typeset .keys .key-windows::before {\n  padding-right: 0.4em;\n  content: \"⊞\";\n}\n.md-typeset .keys .key-left-windows::before {\n  padding-right: 0.4em;\n  content: \"⊞\";\n}\n.md-typeset .keys .key-right-windows::before {\n  padding-right: 0.4em;\n  content: \"⊞\";\n}\n.md-typeset .keys .key-arrow-down::before {\n  padding-right: 0.4em;\n  content: \"↓\";\n}\n.md-typeset .keys .key-arrow-left::before {\n  padding-right: 0.4em;\n  content: \"←\";\n}\n.md-typeset .keys .key-arrow-right::before {\n  padding-right: 0.4em;\n  content: \"→\";\n}\n.md-typeset .keys .key-arrow-up::before {\n  padding-right: 0.4em;\n  content: \"↑\";\n}\n.md-typeset .keys .key-backspace::before {\n  padding-right: 0.4em;\n  content: \"⌫\";\n}\n.md-typeset .keys .key-backtab::before {\n  padding-right: 0.4em;\n  content: \"⇤\";\n}\n.md-typeset .keys .key-caps-lock::before {\n  padding-right: 0.4em;\n  content: \"⇪\";\n}\n.md-typeset .keys .key-clear::before {\n  padding-right: 0.4em;\n  content: \"⌧\";\n}\n.md-typeset .keys .key-context-menu::before {\n  padding-right: 0.4em;\n  content: \"☰\";\n}\n.md-typeset .keys .key-delete::before {\n  padding-right: 0.4em;\n  content: \"⌦\";\n}\n.md-typeset .keys .key-eject::before {\n  padding-right: 0.4em;\n  content: \"⏏\";\n}\n.md-typeset .keys .key-end::before {\n  padding-right: 0.4em;\n  content: \"⤓\";\n}\n.md-typeset .keys .key-escape::before {\n  padding-right: 0.4em;\n  content: \"⎋\";\n}\n.md-typeset .keys .key-home::before {\n  padding-right: 0.4em;\n  content: \"⤒\";\n}\n.md-typeset .keys .key-insert::before {\n  padding-right: 0.4em;\n  content: \"⎀\";\n}\n.md-typeset .keys .key-page-down::before {\n  padding-right: 0.4em;\n  content: \"⇟\";\n}\n.md-typeset .keys .key-page-up::before {\n  padding-right: 0.4em;\n  content: \"⇞\";\n}\n.md-typeset .keys .key-print-screen::before {\n  padding-right: 0.4em;\n  content: \"⎙\";\n}\n.md-typeset .keys .key-tab::after {\n  padding-left: 0.4em;\n  content: \"⇥\";\n}\n.md-typeset .keys .key-num-enter::after {\n  padding-left: 0.4em;\n  content: \"⌤\";\n}\n.md-typeset .keys .key-enter::after {\n  padding-left: 0.4em;\n  content: \"⏎\";\n}\n\n:root {\n  --md-tabbed-icon--prev: svg-load(\"material/chevron-left.svg\");\n  --md-tabbed-icon--next: svg-load(\"material/chevron-right.svg\");\n}\n\n.md-typeset .tabbed-set {\n  position: relative;\n  display: flex;\n  flex-flow: column wrap;\n  margin: 1em 0;\n  border-radius: 0.1rem;\n}\n.md-typeset .tabbed-set > input {\n  position: absolute;\n  width: 0;\n  height: 0;\n  opacity: 0;\n}\n.md-typeset .tabbed-set > input:target {\n  --md-scroll-offset: 0.625em;\n}\n.md-typeset .tabbed-set > input.focus-visible ~ .tabbed-labels::before {\n  background-color: var(--md-accent-fg-color);\n}\n.md-typeset .tabbed-labels {\n  display: flex;\n  max-width: 100%;\n  overflow: auto;\n  scrollbar-width: none;\n  box-shadow: 0 -0.05rem var(--md-default-fg-color--lightest) inset;\n  -ms-overflow-style: none;\n}\n@media print {\n  .md-typeset .tabbed-labels {\n    display: contents;\n  }\n}\n@media screen {\n  .js .md-typeset .tabbed-labels {\n    position: relative;\n  }\n  .js .md-typeset .tabbed-labels::before {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    display: block;\n    width: var(--md-indicator-width);\n    height: 2px;\n    content: \"\";\n    background: var(--md-default-fg-color);\n    transition: width 225ms, background-color 250ms, transform 250ms;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transform: translateX(var(--md-indicator-x));\n  }\n}\n.md-typeset .tabbed-labels::-webkit-scrollbar {\n  display: none;\n}\n.md-typeset .tabbed-labels > label {\n  flex-shrink: 0;\n  width: auto;\n  padding: 0.78125em 1.25em 0.625em;\n  font-size: 0.64rem;\n  font-weight: 700;\n  color: var(--md-default-fg-color--light);\n  white-space: nowrap;\n  cursor: pointer;\n  scroll-margin-inline-start: 1rem;\n  border-bottom: 0.1rem solid transparent;\n  border-radius: 0.1rem 0.1rem 0 0;\n  transition: background-color 250ms, color 250ms;\n}\n@media print {\n  .md-typeset .tabbed-labels > label:nth-child(1) {\n    order: 1;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(2) {\n    order: 2;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(3) {\n    order: 3;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(4) {\n    order: 4;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(5) {\n    order: 5;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(6) {\n    order: 6;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(7) {\n    order: 7;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(8) {\n    order: 8;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(9) {\n    order: 9;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(10) {\n    order: 10;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(11) {\n    order: 11;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(12) {\n    order: 12;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(13) {\n    order: 13;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(14) {\n    order: 14;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(15) {\n    order: 15;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(16) {\n    order: 16;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(17) {\n    order: 17;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(18) {\n    order: 18;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(19) {\n    order: 19;\n  }\n  .md-typeset .tabbed-labels > label:nth-child(20) {\n    order: 20;\n  }\n}\n.md-typeset .tabbed-labels > label:hover {\n  color: var(--md-default-fg-color);\n}\n.md-typeset .tabbed-labels > label > [href]:first-child {\n  color: inherit;\n}\n.md-typeset .tabbed-labels--linked > label {\n  padding: 0;\n}\n.md-typeset .tabbed-labels--linked > label > a {\n  display: block;\n  padding: 0.78125em 1.25em 0.625em;\n}\n.md-typeset .tabbed-content {\n  width: 100%;\n}\n@media print {\n  .md-typeset .tabbed-content {\n    display: contents;\n  }\n}\n.md-typeset .tabbed-block {\n  display: none;\n}\n@media print {\n  .md-typeset .tabbed-block {\n    display: block;\n  }\n  .md-typeset .tabbed-block:nth-child(1) {\n    order: 1;\n  }\n  .md-typeset .tabbed-block:nth-child(2) {\n    order: 2;\n  }\n  .md-typeset .tabbed-block:nth-child(3) {\n    order: 3;\n  }\n  .md-typeset .tabbed-block:nth-child(4) {\n    order: 4;\n  }\n  .md-typeset .tabbed-block:nth-child(5) {\n    order: 5;\n  }\n  .md-typeset .tabbed-block:nth-child(6) {\n    order: 6;\n  }\n  .md-typeset .tabbed-block:nth-child(7) {\n    order: 7;\n  }\n  .md-typeset .tabbed-block:nth-child(8) {\n    order: 8;\n  }\n  .md-typeset .tabbed-block:nth-child(9) {\n    order: 9;\n  }\n  .md-typeset .tabbed-block:nth-child(10) {\n    order: 10;\n  }\n  .md-typeset .tabbed-block:nth-child(11) {\n    order: 11;\n  }\n  .md-typeset .tabbed-block:nth-child(12) {\n    order: 12;\n  }\n  .md-typeset .tabbed-block:nth-child(13) {\n    order: 13;\n  }\n  .md-typeset .tabbed-block:nth-child(14) {\n    order: 14;\n  }\n  .md-typeset .tabbed-block:nth-child(15) {\n    order: 15;\n  }\n  .md-typeset .tabbed-block:nth-child(16) {\n    order: 16;\n  }\n  .md-typeset .tabbed-block:nth-child(17) {\n    order: 17;\n  }\n  .md-typeset .tabbed-block:nth-child(18) {\n    order: 18;\n  }\n  .md-typeset .tabbed-block:nth-child(19) {\n    order: 19;\n  }\n  .md-typeset .tabbed-block:nth-child(20) {\n    order: 20;\n  }\n}\n.md-typeset .tabbed-block > pre:first-child,\n.md-typeset .tabbed-block > .highlight:first-child > pre {\n  margin: 0;\n}\n.md-typeset .tabbed-block > pre:first-child > code,\n.md-typeset .tabbed-block > .highlight:first-child > pre > code {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n.md-typeset .tabbed-block > .highlight:first-child > .filename {\n  margin: 0;\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n.md-typeset .tabbed-block > .highlight:first-child > .highlighttable {\n  margin: 0;\n}\n.md-typeset .tabbed-block > .highlight:first-child > .highlighttable > tbody > tr > .filename span.filename,\n.md-typeset .tabbed-block > .highlight:first-child > .highlighttable > tbody > tr > .linenos {\n  margin: 0;\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n.md-typeset .tabbed-block > .highlight:first-child > .highlighttable > tbody > tr > .code > div > pre > code {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n.md-typeset .tabbed-block > .highlight:first-child + .result {\n  margin-top: -0.125em;\n}\n.md-typeset .tabbed-block > .tabbed-set {\n  margin: 0;\n}\n.md-typeset .tabbed-button {\n  display: block;\n  align-self: center;\n  width: 0.9rem;\n  height: 0.9rem;\n  margin-top: 0.1rem;\n  color: var(--md-default-fg-color--light);\n  pointer-events: initial;\n  cursor: pointer;\n  border-radius: 100%;\n  transition: background-color 250ms;\n}\n.md-typeset .tabbed-button:hover {\n  color: var(--md-accent-fg-color);\n  background-color: var(--md-accent-fg-color--transparent);\n}\n.md-typeset .tabbed-button::after {\n  display: block;\n  width: 100%;\n  height: 100%;\n  content: \"\";\n  background-color: currentcolor;\n  mask-image: var(--md-tabbed-icon--prev);\n  mask-repeat: no-repeat;\n  mask-position: center;\n  mask-size: contain;\n  transition: background-color 250ms, transform 250ms;\n}\n.md-typeset .tabbed-control {\n  position: absolute;\n  display: flex;\n  justify-content: start;\n  width: 1.2rem;\n  height: 1.9rem;\n  pointer-events: none;\n  background: linear-gradient(to right, var(--md-default-bg-color) 60%, transparent);\n  transition: opacity 125ms;\n}\n[dir=rtl] .md-typeset .tabbed-control {\n  transform: rotate(180deg);\n}\n.md-typeset .tabbed-control[hidden] {\n  opacity: 0;\n}\n.md-typeset .tabbed-control--next {\n  right: 0;\n  justify-content: end;\n  background: linear-gradient(to left, var(--md-default-bg-color) 60%, transparent);\n}\n.md-typeset .tabbed-control--next .tabbed-button::after {\n  mask-image: var(--md-tabbed-icon--next);\n}\n\n@media screen and (max-width: 44.984375em) {\n  .md-content__inner > .tabbed-set .tabbed-labels {\n    max-width: 100vw;\n    padding-inline-start: 0.8rem;\n    margin: 0 -0.8rem;\n    scroll-padding-inline-start: 0.8rem;\n  }\n  .md-content__inner > .tabbed-set .tabbed-labels::after {\n    padding-inline-end: 0.8rem;\n    content: \"\";\n  }\n  .md-content__inner > .tabbed-set .tabbed-labels ~ .tabbed-control--prev {\n    width: 2rem;\n    padding-inline-start: 0.8rem;\n    margin-inline-start: -0.8rem;\n  }\n  .md-content__inner > .tabbed-set .tabbed-labels ~ .tabbed-control--next {\n    width: 2rem;\n    padding-inline-end: 0.8rem;\n    margin-inline-end: -0.8rem;\n  }\n}\n@media screen {\n  .md-typeset .tabbed-set > input:nth-child(20):checked ~ .tabbed-labels > :nth-child(20), .md-typeset .tabbed-set > input:nth-child(19):checked ~ .tabbed-labels > :nth-child(19), .md-typeset .tabbed-set > input:nth-child(18):checked ~ .tabbed-labels > :nth-child(18), .md-typeset .tabbed-set > input:nth-child(17):checked ~ .tabbed-labels > :nth-child(17), .md-typeset .tabbed-set > input:nth-child(16):checked ~ .tabbed-labels > :nth-child(16), .md-typeset .tabbed-set > input:nth-child(15):checked ~ .tabbed-labels > :nth-child(15), .md-typeset .tabbed-set > input:nth-child(14):checked ~ .tabbed-labels > :nth-child(14), .md-typeset .tabbed-set > input:nth-child(13):checked ~ .tabbed-labels > :nth-child(13), .md-typeset .tabbed-set > input:nth-child(12):checked ~ .tabbed-labels > :nth-child(12), .md-typeset .tabbed-set > input:nth-child(11):checked ~ .tabbed-labels > :nth-child(11), .md-typeset .tabbed-set > input:nth-child(10):checked ~ .tabbed-labels > :nth-child(10), .md-typeset .tabbed-set > input:nth-child(9):checked ~ .tabbed-labels > :nth-child(9), .md-typeset .tabbed-set > input:nth-child(8):checked ~ .tabbed-labels > :nth-child(8), .md-typeset .tabbed-set > input:nth-child(7):checked ~ .tabbed-labels > :nth-child(7), .md-typeset .tabbed-set > input:nth-child(6):checked ~ .tabbed-labels > :nth-child(6), .md-typeset .tabbed-set > input:nth-child(5):checked ~ .tabbed-labels > :nth-child(5), .md-typeset .tabbed-set > input:nth-child(4):checked ~ .tabbed-labels > :nth-child(4), .md-typeset .tabbed-set > input:nth-child(3):checked ~ .tabbed-labels > :nth-child(3), .md-typeset .tabbed-set > input:nth-child(2):checked ~ .tabbed-labels > :nth-child(2), .md-typeset .tabbed-set > input:nth-child(1):checked ~ .tabbed-labels > :nth-child(1) {\n    color: var(--md-default-fg-color);\n  }\n  .no-js .md-typeset .tabbed-set > input:nth-child(20):checked ~ .tabbed-labels > :nth-child(20), .md-typeset .no-js .tabbed-set > input:nth-child(20):checked ~ .tabbed-labels > :nth-child(20), .no-js .md-typeset .tabbed-set > input:nth-child(19):checked ~ .tabbed-labels > :nth-child(19), .md-typeset .no-js .tabbed-set > input:nth-child(19):checked ~ .tabbed-labels > :nth-child(19), .no-js .md-typeset .tabbed-set > input:nth-child(18):checked ~ .tabbed-labels > :nth-child(18), .md-typeset .no-js .tabbed-set > input:nth-child(18):checked ~ .tabbed-labels > :nth-child(18), .no-js .md-typeset .tabbed-set > input:nth-child(17):checked ~ .tabbed-labels > :nth-child(17), .md-typeset .no-js .tabbed-set > input:nth-child(17):checked ~ .tabbed-labels > :nth-child(17), .no-js .md-typeset .tabbed-set > input:nth-child(16):checked ~ .tabbed-labels > :nth-child(16), .md-typeset .no-js .tabbed-set > input:nth-child(16):checked ~ .tabbed-labels > :nth-child(16), .no-js .md-typeset .tabbed-set > input:nth-child(15):checked ~ .tabbed-labels > :nth-child(15), .md-typeset .no-js .tabbed-set > input:nth-child(15):checked ~ .tabbed-labels > :nth-child(15), .no-js .md-typeset .tabbed-set > input:nth-child(14):checked ~ .tabbed-labels > :nth-child(14), .md-typeset .no-js .tabbed-set > input:nth-child(14):checked ~ .tabbed-labels > :nth-child(14), .no-js .md-typeset .tabbed-set > input:nth-child(13):checked ~ .tabbed-labels > :nth-child(13), .md-typeset .no-js .tabbed-set > input:nth-child(13):checked ~ .tabbed-labels > :nth-child(13), .no-js .md-typeset .tabbed-set > input:nth-child(12):checked ~ .tabbed-labels > :nth-child(12), .md-typeset .no-js .tabbed-set > input:nth-child(12):checked ~ .tabbed-labels > :nth-child(12), .no-js .md-typeset .tabbed-set > input:nth-child(11):checked ~ .tabbed-labels > :nth-child(11), .md-typeset .no-js .tabbed-set > input:nth-child(11):checked ~ .tabbed-labels > :nth-child(11), .no-js .md-typeset .tabbed-set > input:nth-child(10):checked ~ .tabbed-labels > :nth-child(10), .md-typeset .no-js .tabbed-set > input:nth-child(10):checked ~ .tabbed-labels > :nth-child(10), .no-js .md-typeset .tabbed-set > input:nth-child(9):checked ~ .tabbed-labels > :nth-child(9), .md-typeset .no-js .tabbed-set > input:nth-child(9):checked ~ .tabbed-labels > :nth-child(9), .no-js .md-typeset .tabbed-set > input:nth-child(8):checked ~ .tabbed-labels > :nth-child(8), .md-typeset .no-js .tabbed-set > input:nth-child(8):checked ~ .tabbed-labels > :nth-child(8), .no-js .md-typeset .tabbed-set > input:nth-child(7):checked ~ .tabbed-labels > :nth-child(7), .md-typeset .no-js .tabbed-set > input:nth-child(7):checked ~ .tabbed-labels > :nth-child(7), .no-js .md-typeset .tabbed-set > input:nth-child(6):checked ~ .tabbed-labels > :nth-child(6), .md-typeset .no-js .tabbed-set > input:nth-child(6):checked ~ .tabbed-labels > :nth-child(6), .no-js .md-typeset .tabbed-set > input:nth-child(5):checked ~ .tabbed-labels > :nth-child(5), .md-typeset .no-js .tabbed-set > input:nth-child(5):checked ~ .tabbed-labels > :nth-child(5), .no-js .md-typeset .tabbed-set > input:nth-child(4):checked ~ .tabbed-labels > :nth-child(4), .md-typeset .no-js .tabbed-set > input:nth-child(4):checked ~ .tabbed-labels > :nth-child(4), .no-js .md-typeset .tabbed-set > input:nth-child(3):checked ~ .tabbed-labels > :nth-child(3), .md-typeset .no-js .tabbed-set > input:nth-child(3):checked ~ .tabbed-labels > :nth-child(3), .no-js .md-typeset .tabbed-set > input:nth-child(2):checked ~ .tabbed-labels > :nth-child(2), .md-typeset .no-js .tabbed-set > input:nth-child(2):checked ~ .tabbed-labels > :nth-child(2), .no-js .md-typeset .tabbed-set > input:nth-child(1):checked ~ .tabbed-labels > :nth-child(1), .md-typeset .no-js .tabbed-set > input:nth-child(1):checked ~ .tabbed-labels > :nth-child(1) {\n    border-color: var(--md-default-fg-color);\n  }\n}\n\n.md-typeset .tabbed-set > input:nth-child(20).focus-visible ~ .tabbed-labels > :nth-child(20), .md-typeset .tabbed-set > input:nth-child(19).focus-visible ~ .tabbed-labels > :nth-child(19), .md-typeset .tabbed-set > input:nth-child(18).focus-visible ~ .tabbed-labels > :nth-child(18), .md-typeset .tabbed-set > input:nth-child(17).focus-visible ~ .tabbed-labels > :nth-child(17), .md-typeset .tabbed-set > input:nth-child(16).focus-visible ~ .tabbed-labels > :nth-child(16), .md-typeset .tabbed-set > input:nth-child(15).focus-visible ~ .tabbed-labels > :nth-child(15), .md-typeset .tabbed-set > input:nth-child(14).focus-visible ~ .tabbed-labels > :nth-child(14), .md-typeset .tabbed-set > input:nth-child(13).focus-visible ~ .tabbed-labels > :nth-child(13), .md-typeset .tabbed-set > input:nth-child(12).focus-visible ~ .tabbed-labels > :nth-child(12), .md-typeset .tabbed-set > input:nth-child(11).focus-visible ~ .tabbed-labels > :nth-child(11), .md-typeset .tabbed-set > input:nth-child(10).focus-visible ~ .tabbed-labels > :nth-child(10), .md-typeset .tabbed-set > input:nth-child(9).focus-visible ~ .tabbed-labels > :nth-child(9), .md-typeset .tabbed-set > input:nth-child(8).focus-visible ~ .tabbed-labels > :nth-child(8), .md-typeset .tabbed-set > input:nth-child(7).focus-visible ~ .tabbed-labels > :nth-child(7), .md-typeset .tabbed-set > input:nth-child(6).focus-visible ~ .tabbed-labels > :nth-child(6), .md-typeset .tabbed-set > input:nth-child(5).focus-visible ~ .tabbed-labels > :nth-child(5), .md-typeset .tabbed-set > input:nth-child(4).focus-visible ~ .tabbed-labels > :nth-child(4), .md-typeset .tabbed-set > input:nth-child(3).focus-visible ~ .tabbed-labels > :nth-child(3), .md-typeset .tabbed-set > input:nth-child(2).focus-visible ~ .tabbed-labels > :nth-child(2), .md-typeset .tabbed-set > input:nth-child(1).focus-visible ~ .tabbed-labels > :nth-child(1) {\n  color: var(--md-accent-fg-color);\n}\n\n.md-typeset .tabbed-set > input:nth-child(20):checked ~ .tabbed-content > :nth-child(20), .md-typeset .tabbed-set > input:nth-child(19):checked ~ .tabbed-content > :nth-child(19), .md-typeset .tabbed-set > input:nth-child(18):checked ~ .tabbed-content > :nth-child(18), .md-typeset .tabbed-set > input:nth-child(17):checked ~ .tabbed-content > :nth-child(17), .md-typeset .tabbed-set > input:nth-child(16):checked ~ .tabbed-content > :nth-child(16), .md-typeset .tabbed-set > input:nth-child(15):checked ~ .tabbed-content > :nth-child(15), .md-typeset .tabbed-set > input:nth-child(14):checked ~ .tabbed-content > :nth-child(14), .md-typeset .tabbed-set > input:nth-child(13):checked ~ .tabbed-content > :nth-child(13), .md-typeset .tabbed-set > input:nth-child(12):checked ~ .tabbed-content > :nth-child(12), .md-typeset .tabbed-set > input:nth-child(11):checked ~ .tabbed-content > :nth-child(11), .md-typeset .tabbed-set > input:nth-child(10):checked ~ .tabbed-content > :nth-child(10), .md-typeset .tabbed-set > input:nth-child(9):checked ~ .tabbed-content > :nth-child(9), .md-typeset .tabbed-set > input:nth-child(8):checked ~ .tabbed-content > :nth-child(8), .md-typeset .tabbed-set > input:nth-child(7):checked ~ .tabbed-content > :nth-child(7), .md-typeset .tabbed-set > input:nth-child(6):checked ~ .tabbed-content > :nth-child(6), .md-typeset .tabbed-set > input:nth-child(5):checked ~ .tabbed-content > :nth-child(5), .md-typeset .tabbed-set > input:nth-child(4):checked ~ .tabbed-content > :nth-child(4), .md-typeset .tabbed-set > input:nth-child(3):checked ~ .tabbed-content > :nth-child(3), .md-typeset .tabbed-set > input:nth-child(2):checked ~ .tabbed-content > :nth-child(2), .md-typeset .tabbed-set > input:nth-child(1):checked ~ .tabbed-content > :nth-child(1) {\n  display: block;\n}\n\n:root {\n  --md-tasklist-icon: svg-load(\"octicons/check-circle-fill-24.svg\");\n  --md-tasklist-icon--checked: svg-load(\"octicons/check-circle-fill-24.svg\");\n}\n\n.md-typeset .task-list-item {\n  position: relative;\n  list-style-type: none;\n}\n.md-typeset .task-list-item [type=checkbox] {\n  position: absolute;\n  inset-inline-start: -2em;\n  top: 0.45em;\n}\n.md-typeset .task-list-control [type=checkbox] {\n  z-index: -1;\n  opacity: 0;\n}\n.md-typeset .task-list-indicator::before {\n  position: absolute;\n  inset-inline-start: -1.5em;\n  top: 0.15em;\n  width: 1.25em;\n  height: 1.25em;\n  content: \"\";\n  background-color: var(--md-default-fg-color--lightest);\n  mask-image: var(--md-tasklist-icon);\n  mask-repeat: no-repeat;\n  mask-position: center;\n  mask-size: contain;\n}\n.md-typeset [type=checkbox]:checked + .task-list-indicator::before {\n  background-color: #00e676;\n  mask-image: var(--md-tasklist-icon--checked);\n}\n\n.rst-versions {\n  font-family: var(--md-text-font-family);\n}\n.rst-versions.rst-badge {\n  top: 50px;\n  bottom: inherit !important;\n  height: auto;\n  font-size: 0.85rem;\n}\n\n@media print {\n  [id=__comments] {\n    display: none;\n  }\n  .giscus {\n    display: none;\n  }\n}\n:root > * {\n  --md-mermaid-font-family: var(--md-text-font-family), sans-serif;\n  --md-mermaid-edge-color: var(--md-code-fg-color);\n  --md-mermaid-node-bg-color: var(--md-accent-fg-color--transparent);\n  --md-mermaid-node-fg-color: var(--md-accent-fg-color);\n  --md-mermaid-label-bg-color: var(--md-default-bg-color);\n  --md-mermaid-label-fg-color: var(--md-code-fg-color);\n  --md-mermaid-sequence-actor-bg-color: var(--md-mermaid-label-bg-color);\n  --md-mermaid-sequence-actor-fg-color: var(--md-mermaid-label-fg-color);\n  --md-mermaid-sequence-actor-border-color: var(--md-mermaid-node-fg-color);\n  --md-mermaid-sequence-actor-line-color: var(--md-default-fg-color--lighter);\n  --md-mermaid-sequence-actorman-bg-color: var(--md-mermaid-label-bg-color);\n  --md-mermaid-sequence-actorman-line-color: var(--md-mermaid-node-fg-color);\n  --md-mermaid-sequence-box-bg-color: var(--md-mermaid-node-bg-color);\n  --md-mermaid-sequence-box-fg-color: var(--md-mermaid-edge-color);\n  --md-mermaid-sequence-label-bg-color: var(--md-mermaid-node-bg-color);\n  --md-mermaid-sequence-label-fg-color: var(--md-mermaid-node-fg-color);\n  --md-mermaid-sequence-loop-bg-color: var(--md-mermaid-node-bg-color);\n  --md-mermaid-sequence-loop-fg-color: var(--md-mermaid-edge-color);\n  --md-mermaid-sequence-loop-border-color: var(--md-mermaid-node-fg-color);\n  --md-mermaid-sequence-message-fg-color: var(--md-mermaid-edge-color);\n  --md-mermaid-sequence-message-line-color: var(--md-mermaid-edge-color);\n  --md-mermaid-sequence-note-bg-color: var(--md-mermaid-label-bg-color);\n  --md-mermaid-sequence-note-fg-color: var(--md-mermaid-edge-color);\n  --md-mermaid-sequence-note-border-color: var(--md-mermaid-label-fg-color);\n  --md-mermaid-sequence-number-bg-color: var(--md-mermaid-node-fg-color);\n  --md-mermaid-sequence-number-fg-color: var(--md-accent-bg-color);\n}\n\n.mermaid {\n  margin: 1em 0;\n  line-height: normal;\n}\n\n:root > * {\n  --md-graphviz-edge-color: var(--md-default-fg-color);\n  --md-graphviz-node-bg-color: var(--md-accent-fg-color--transparent);\n  --md-graphviz-node-fg-color: var(--md-accent-fg-color);\n  --md-graphviz-label-bg-color: var(--md-default-bg-color);\n  --md-graphviz-label-fg-color: var(--md-code-fg-color);\n  --md-graphviz-a-hover-color: var(--md-primary-fg-color);\n}\n\n.graphviz {\n  margin: 1em 0;\n}\n\n.graphviz a:hover > text {\n  fill: var(--md-graphviz-hover-color) !important;\n}\n\n.md-typeset .grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(min(100%, 16rem), 1fr));\n  grid-gap: 0.4rem;\n  margin: 1em 0;\n}\n.md-typeset .grid.cards > :is(ul, ol) {\n  display: contents;\n}\n.md-typeset .grid.cards > :is(ul, ol) > li,\n.md-typeset .grid > .card {\n  display: block;\n  padding: 0.8rem;\n  margin: 0;\n  border: 0.05rem solid var(--md-default-fg-color--lightest);\n  border-radius: 0.1rem;\n  transition: border 250ms, box-shadow 250ms;\n}\n.md-typeset .grid.cards > :is(ul, ol) > li:is(:focus-within, :hover),\n.md-typeset .grid > .card:is(:focus-within, :hover) {\n  border-color: transparent;\n  box-shadow: var(--md-shadow-z2);\n}\n.md-typeset .grid.cards > :is(ul, ol) > li > hr,\n.md-typeset .grid > .card > hr {\n  margin-block: 1em;\n}\n.md-typeset .grid.cards > :is(ul, ol) > li > :first-child,\n.md-typeset .grid > .card > :first-child {\n  margin-top: 0;\n}\n.md-typeset .grid.cards > :is(ul, ol) > li > :last-child,\n.md-typeset .grid > .card > :last-child {\n  margin-bottom: 0;\n}\n.md-typeset .grid > * {\n  margin-block: 0;\n}\n.md-typeset .grid > :is(.admonition, .md-typeset details, details) {\n  margin-block: 0;\n}\n.md-typeset .grid > pre,\n.md-typeset .grid > .highlight > *,\n.md-typeset .grid > .highlighttable {\n  margin-block: 0;\n}\n.md-typeset .grid > .highlight > pre:only-child,\n.md-typeset .grid > .highlight > pre > code {\n  height: 100%;\n}\n.md-typeset .grid > .highlighttable,\n.md-typeset .grid > .highlighttable > tbody,\n.md-typeset .grid > .highlighttable > tbody > tr,\n.md-typeset .grid > .highlighttable > tbody > tr > .code,\n.md-typeset .grid > .highlighttable > tbody > tr > .code > .highlight,\n.md-typeset .grid > .highlighttable > tbody > tr > .code > .highlight > pre,\n.md-typeset .grid > .highlighttable > tbody > tr > .code > .highlight > pre > code {\n  height: 100%;\n}\n.md-typeset .grid > .tabbed-set {\n  margin-block: 0;\n}\n\n@media screen and (min-width: 45em) {\n  .md-typeset .inline {\n    float: inline-start;\n    width: 11.7rem;\n    margin-inline-end: 0.8rem;\n    margin-top: 0;\n    margin-bottom: 0.8rem;\n  }\n  .md-typeset .inline.end {\n    float: inline-end;\n    margin-inline: 0.8rem 0;\n  }\n}\n\n.md-typeset .align-left {\n  text-align: left;\n}\n.md-typeset .align-right {\n  text-align: right;\n}\n.md-typeset .align-center {\n  clear: both;\n  text-align: center;\n}\n.md-typeset .align-top {\n  vertical-align: top;\n}\n.md-typeset .align-middle {\n  vertical-align: middle;\n}\n.md-typeset .align-bottom {\n  vertical-align: bottom;\n}\n.md-typeset .figure.align-left,\n.md-typeset figure.align-left,\n.md-typeset img.align-left,\n.md-typeset object.align-left,\n.md-typeset table.align-left {\n  margin-right: auto;\n}\n.md-typeset .figure.align-center,\n.md-typeset figure.align-center,\n.md-typeset img.align-center,\n.md-typeset object.align-center,\n.md-typeset table.align-center {\n  margin-right: auto;\n  margin-left: auto;\n}\n.md-typeset .figure.align-right,\n.md-typeset figure.align-right,\n.md-typeset img.align-right,\n.md-typeset object.align-right,\n.md-typeset table.align-right {\n  margin-left: auto;\n}\n.md-typeset .figure.align-center,\n.md-typeset .figure.align-right,\n.md-typeset figure.align-center,\n.md-typeset figure.align-right,\n.md-typeset img.align-center,\n.md-typeset img.align-right,\n.md-typeset object.align-center,\n.md-typeset object.align-right {\n  display: block;\n}\n.md-typeset .figure.align-left,\n.md-typeset .figure.align-right,\n.md-typeset figure.align-left,\n.md-typeset figure.align-right,\n.md-typeset table.align-left,\n.md-typeset table.align-center,\n.md-typeset table.align-right {\n  text-align: inherit;\n}\n.md-typeset .rubric {\n  font-weight: 700;\n}\n.md-typeset .viewcode-block .viewcode-back {\n  float: right;\n}\n.md-typeset .versionmodified {\n  font-style: italic;\n}\n.md-typeset div.line-block {\n  display: block;\n}\n.md-typeset div.line-block div.line-block {\n  margin-left: 1.5em;\n}\n.md-typeset aside.footnote,\n.md-typeset div.citation {\n  display: grid;\n  grid-auto-columns: minmax(auto, max-content);\n}\n.md-typeset aside.footnote > span.label,\n.md-typeset div.citation > span.label {\n  grid-column: 1;\n}\n.md-typeset aside.footnote > span.backrefs,\n.md-typeset div.citation > span.backrefs {\n  grid-column: 2;\n}\n.md-typeset aside.footnote > span:last-of-type,\n.md-typeset div.citation > span:last-of-type {\n  padding-right: 0.5em;\n}\n.md-typeset aside.footnote > :not(span.backrefs, span.label),\n.md-typeset div.citation > :not(span.backrefs, span.label) {\n  grid-column: 3;\n}\n.md-typeset aside.footnote > :not(span.backrefs, span.label):first-of-type,\n.md-typeset div.citation > :not(span.backrefs, span.label):first-of-type {\n  margin-top: 0;\n}\n.md-typeset aside.footnote > :not(span.backrefs, span.label):last-child,\n.md-typeset div.citation > :not(span.backrefs, span.label):last-child {\n  margin-bottom: 0;\n}\n.md-typeset aside.footnote > :not(span.backrefs, span.label):last-child::after,\n.md-typeset div.citation > :not(span.backrefs, span.label):last-child::after {\n  clear: both;\n  content: \"\";\n}\n\n.md-typeset :is(dl.objdesc, dl.api-field) > dt {\n  font-family: var(--md-code-font-family);\n  background: var(--md-code-bg-color);\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt code {\n  padding: 0;\n  border-radius: 0;\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt .sig-name:not(.sig-name-nonprimary) {\n  padding: 0;\n  font-weight: 700;\n  color: var(--md-code-hl-name-color);\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt .sig-param {\n  font-style: normal;\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt .sig-param .n:not(.desctype) {\n  color: var(--md-default-fg-color--light);\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt dl,\n.md-typeset :is(dl.objdesc, dl.api-field) > dt dd {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt .sig-param a.reference .n:not(.desctype):hover {\n  color: var(--md-accent-fg-color);\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt.sig-wrap .sig-param-decl::before {\n  white-space: pre;\n  content: \"\\a     \";\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt.sig-wrap .sig-paren ~ .sig-paren::before {\n  white-space: pre;\n  content: \"\\a\";\n}\n.md-typeset dl.objdesc > dd > dl.field-list > dt > .colon {\n  display: none;\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt a.reference > .sig-name,\n.md-typeset :is(dl.objdesc, dl.api-field) > dt a.reference.sig-name,\n.md-typeset :is(dl.objdesc, dl.api-field) > dt a.reference:not(.desctype) > .n,\n.md-typeset .sig-inline a.reference > .sig-name,\n.md-typeset .sig-inline a.reference.sig-name,\n.md-typeset .sig-inline a.reference:not(.desctype) > .n {\n  color: var(--md-typeset-a-color);\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt a.reference > .sig-name:hover,\n.md-typeset :is(dl.objdesc, dl.api-field) > dt a.reference.sig-name:hover,\n.md-typeset :is(dl.objdesc, dl.api-field) > dt a.reference:not(.desctype) > .n:hover,\n.md-typeset .sig-inline a.reference > .sig-name:hover,\n.md-typeset .sig-inline a.reference.sig-name:hover,\n.md-typeset .sig-inline a.reference:not(.desctype) > .n:hover {\n  color: var(--md-accent-fg-color);\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt .desctype,\n.md-typeset :is(dl.objdesc, dl.api-field) > dt .desctype > a.reference,\n.md-typeset .sig-inline .desctype,\n.md-typeset .sig-inline .desctype > a.reference {\n  color: var(--md-code-hl-special-color);\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt .desctype .n,\n.md-typeset :is(dl.objdesc, dl.api-field) > dt .desctype > a.reference .n,\n.md-typeset .sig-inline .desctype .n,\n.md-typeset .sig-inline .desctype > a.reference .n {\n  color: inherit;\n}\n.md-typeset :is(dl.objdesc, dl.api-field) > dt .desctype:is(a.reference):hover,\n.md-typeset :is(dl.objdesc, dl.api-field) > dt .desctype > a.reference:hover,\n.md-typeset .sig-inline .desctype:is(a.reference):hover,\n.md-typeset .sig-inline .desctype > a.reference:hover {\n  color: var(--md-accent-fg-color);\n}\n.md-typeset dl.objdesc > dt {\n  padding-top: 0.5em;\n  padding-right: 0.5em;\n  padding-left: 0.5em;\n  font-family: var(--md-code-font-family);\n  background: var(--md-code-bg-color);\n}\n.md-typeset dl.objdesc > dt,\n.md-typeset dl.objdesc > dt code {\n  font-size: 0.75rem;\n}\n.md-typeset dl.objdesc > dt .property {\n  font-style: normal;\n  font-weight: 700;\n  color: var(--md-code-hl-keyword-color);\n}\n.md-typeset dl.objdesc > dt .sig-prename {\n  padding: 0;\n  color: var(--md-code-hl-name-color);\n}\n.md-typeset dl.objdesc > dt .viewcode-link,\n.md-typeset dl.objdesc > dt .viewcode-back {\n  float: right;\n  text-align: right;\n}\n.md-typeset dl.objdesc > dt.api-include-path,\n.md-typeset dl.objdesc > dt.api-include-path code {\n  font-size: 0.65rem;\n}\n.md-typeset dl.objdesc > dt:first-child {\n  padding-top: 0.5em;\n}\n.md-typeset dl.objdesc > dt:last-of-type {\n  padding-bottom: 0.5em;\n}\n.md-typeset dl.objdesc > dd dl.field-list > dt {\n  margin-bottom: 1em;\n  font-size: 1em;\n  font-weight: 700;\n}\n.md-typeset dl.objdesc > dd dd.noindent {\n  margin-left: 0;\n}\n.md-typeset dl.api-field > dt {\n  display: table;\n}\n.md-typeset dl.api-field > dt a.headerlink {\n  position: relative;\n  left: 0.5em;\n  width: 0;\n  margin-left: 0;\n}\n.md-typeset dl.api-field > dt,\n.md-typeset dl.api-field > dt code {\n  font-size: 0.65rem;\n}\n.md-typeset dl.api-field > dt.api-parameter-kind {\n  float: right;\n  font-family: var(--md-text-font-family);\n}\n.md-typeset dl.api-field > dt.api-parameter-kind::before {\n  content: \"[\";\n}\n.md-typeset dl.api-field > dt.api-parameter-kind::after {\n  content: \"]\";\n}\n.md-typeset dl.objdesc.summary > dd,\n.md-typeset dl.objdesc.summary > dd > p:first-child {\n  margin-top: 0;\n}\n.md-typeset .sig-inline:is(.c-texpr, .cpp-texpr) {\n  font-family: unset;\n  background-color: unset;\n}\n\n.md-nav__link {\n  white-space: nowrap;\n}\n\n:root > * {\n  --objinfo-icon-fg-alias: #e65100;\n  --objinfo-icon-fg-default: #424242;\n  --objinfo-icon-fg-data: #1565c0;\n  --objinfo-icon-fg-procedure: #6a1b9a;\n  --objinfo-icon-fg-sub-data: #2e7d32;\n  --objinfo-icon-bg-default: var(--md-default-bg-color);\n}\n\n@media screen {\n  [data-md-color-scheme=slate] {\n    --objinfo-icon-fg-alias: #ffb74d;\n    --objinfo-icon-fg-default: #e0e0e0;\n    --objinfo-icon-fg-data: #64b5f6;\n    --objinfo-icon-fg-procedure: #ce93d8;\n    --objinfo-icon-fg-sub-data: #81c784;\n  }\n}\n.objinfo-icon {\n  display: inline-table;\n  flex-shrink: 0;\n  width: 16px;\n  height: 16px;\n  margin-right: 8px;\n  font-family: var(--md-text-font-family);\n  font-weight: 500;\n  line-height: 16px;\n  color: var(--objinfo-icon-fg-default);\n  text-align: center;\n  vertical-align: middle;\n  background-color: var(--objinfo-icon-bg-default);\n  border: 1px solid var(--objinfo-icon-fg-default);\n  border-radius: 2px;\n}\n.objinfo-icon__alias {\n  color: var(--objinfo-icon-bg-default);\n  background-color: var(--objinfo-icon-fg-alias);\n  border: 1px solid var(--objinfo-icon-fg-alias);\n}\n.objinfo-icon__procedure {\n  color: var(--objinfo-icon-bg-default);\n  background-color: var(--objinfo-icon-fg-procedure);\n  border: 1px solid var(--objinfo-icon-fg-procedure);\n}\n.objinfo-icon__data {\n  color: var(--objinfo-icon-bg-default);\n  background-color: var(--objinfo-icon-fg-data);\n  border: 1px solid var(--objinfo-icon-fg-data);\n}\n.objinfo-icon__sub-data {\n  color: var(--objinfo-icon-bg-default);\n  background-color: var(--objinfo-icon-fg-sub-data);\n  border: 1px solid var(--objinfo-icon-fg-sub-data);\n}\n\n.search-result-objlabel {\n  float: right;\n  padding: 2px;\n  border: 1px solid var(--md-default-fg-color--light);\n  border-radius: 2px;\n}\n\ntable.longtable.docutils.data.align-default tbody > tr > td > p > a.reference.internal > code.xref.py.py-obj.docutils.literal.notranslate > span.pre {\n  word-break: normal;\n}", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Enforce correct box model and prevent adjustments of font size after\n// orientation changes in IE and iOS\nhtml {\n  box-sizing: border-box;\n  text-size-adjust: none;\n}\n\n// All elements shall inherit the document default\n*,\n*::before,\n*::after {\n  box-sizing: inherit;\n\n  // [reduced motion]: Disable all transitions\n  @media (prefers-reduced-motion) {\n    transition: none !important; // stylelint-disable-line\n  }\n}\n\n// Remove margin in all browsers\nbody {\n  margin: 0;\n}\n\n// Reset tap outlines on iOS and Android\na,\nbutton,\nlabel,\ninput {\n  -webkit-tap-highlight-color: transparent;\n}\n\n// Reset link styles\na {\n  color: inherit;\n  text-decoration: none;\n}\n\n// Normalize horizontal separator styles\nhr {\n  box-sizing: content-box;\n  display: block;\n  height: px2rem(1px);\n  padding: 0;\n  overflow: visible;\n  border: 0;\n}\n\n// Normalize font-size in all browsers\nsmall {\n  font-size: 80%;\n}\n\n// Prevent subscript and superscript from affecting line-height\nsub,\nsup {\n  line-height: 1em;\n}\n\n// Remove border on image\nimg {\n  border-style: none;\n}\n\n// Reset table styles\ntable {\n  border-spacing: 0;\n  border-collapse: separate;\n}\n\n// Reset table cell styles\ntd,\nth {\n  font-weight: 400;\n  vertical-align: top;\n}\n\n// Reset button styles\nbutton {\n  padding: 0;\n  margin: 0;\n  font-family: inherit;\n  font-size: inherit;\n  background: transparent;\n  border: 0;\n}\n\n// Reset input styles\ninput {\n  border: 0;\n  outline: none;\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Color variables\n:root {\n  @extend %root;\n\n  // Primary color shades\n  --md-primary-fg-color:               hsla(#{hex2hsl($clr-indigo-500)}, 1);\n  --md-primary-fg-color--light:        hsla(#{hex2hsl($clr-indigo-400)}, 1);\n  --md-primary-fg-color--dark:         hsla(#{hex2hsl($clr-indigo-700)}, 1);\n  --md-primary-bg-color:               hsla(0, 0%, 100%, 1);\n  --md-primary-bg-color--light:        hsla(0, 0%, 100%, 0.7);\n\n  // Accent color shades\n  --md-accent-fg-color:                hsla(#{hex2hsl($clr-indigo-a200)}, 1);\n  --md-accent-fg-color--transparent:   hsla(#{hex2hsl($clr-indigo-a200)}, 0.1);\n  --md-accent-bg-color:                hsla(0, 0%, 100%, 1);\n  --md-accent-bg-color--light:         hsla(0, 0%, 100%, 0.7);\n}\n\n// ----------------------------------------------------------------------------\n\n// Allow to explicitly use color schemes in nested content\n[data-md-color-scheme=\"default\"] {\n  @extend %root;\n\n  // Indicate that the site is rendered with a light color scheme\n  color-scheme: light;\n\n  // Hide images for dark mode\n  img[src$=\"#only-dark\"],\n  img[src$=\"#gh-dark-mode-only\"] {\n    display: none;\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Placeholders\n// ----------------------------------------------------------------------------\n\n// Default theme, i.e. light mode\n%root {\n\n  // Color hue in the range [0,360] - change this variable to alter the tone\n  // of the theme, e.g. to make it more redish or greenish\n  --md-hue: 225deg;\n\n  // Default color shades\n  --md-default-fg-color:               hsla(0, 0%, 0%, 0.87);\n  --md-default-fg-color--light:        hsla(0, 0%, 0%, 0.54);\n  --md-default-fg-color--lighter:      hsla(0, 0%, 0%, 0.32);\n  --md-default-fg-color--lightest:     hsla(0, 0%, 0%, 0.07);\n  --md-default-bg-color:               hsla(0, 0%, 100%, 1);\n  --md-default-bg-color--light:        hsla(0, 0%, 100%, 0.7);\n  --md-default-bg-color--lighter:      hsla(0, 0%, 100%, 0.3);\n  --md-default-bg-color--lightest:     hsla(0, 0%, 100%, 0.12);\n\n  // Code color shades\n  --md-code-fg-color:                  hsla(200, 18%, 26%, 1);\n  --md-code-bg-color:                  hsla(200, 0%, 96%, 1);\n\n  // Code highlighting color shades\n  --md-code-hl-color:                  hsla(#{hex2hsl($clr-blue-a200)}, 1);\n  --md-code-hl-color--light:           hsla(#{hex2hsl($clr-blue-a200)}, 0.1);\n\n  // Code highlighting syntax color shades\n  --md-code-hl-number-color:           hsla(0, 67%, 50%, 1);\n  --md-code-hl-special-color:          hsla(340, 83%, 47%, 1);\n  --md-code-hl-function-color:         hsla(291, 45%, 50%, 1);\n  --md-code-hl-constant-color:         hsla(250, 63%, 60%, 1);\n  --md-code-hl-keyword-color:          hsla(219, 54%, 51%, 1);\n  --md-code-hl-string-color:           hsla(150, 63%, 30%, 1);\n  --md-code-hl-name-color:             var(--md-code-fg-color);\n  --md-code-hl-operator-color:         var(--md-default-fg-color--light);\n  --md-code-hl-punctuation-color:      var(--md-default-fg-color--light);\n  --md-code-hl-comment-color:          var(--md-default-fg-color--light);\n  --md-code-hl-generic-color:          var(--md-default-fg-color--light);\n  --md-code-hl-variable-color:         var(--md-default-fg-color--light);\n\n  // Typeset color shades\n  --md-typeset-color:                  var(--md-default-fg-color);\n\n  // Typeset `a` color shades\n  --md-typeset-a-color:                var(--md-primary-fg-color);\n\n  // Typeset `del` and `ins` color shades\n  --md-typeset-del-color:              hsla(6, 90%, 60%, 0.15);\n  --md-typeset-ins-color:              hsla(150, 90%, 44%, 0.15);\n\n  // Typeset `kbd` color shades\n  --md-typeset-kbd-color:              hsla(0, 0%, 98%, 1);\n  --md-typeset-kbd-accent-color:       hsla(0, 100%, 100%, 1);\n  --md-typeset-kbd-border-color:       hsla(0, 0%, 72%, 1);\n\n  // Typeset `mark` color shades\n  --md-typeset-mark-color:             hsla(#{hex2hsl($clr-yellow-a200)}, 0.5);\n\n  // Typeset `table` color shades\n  --md-typeset-table-color:            hsla(0, 0%, 0%, 0.12);\n  --md-typeset-table-color--light:     hsla(0, 0%, 0%, 0.035);\n\n  // Admonition color shades\n  --md-admonition-fg-color:            var(--md-default-fg-color);\n  --md-admonition-bg-color:            var(--md-default-bg-color);\n\n  // Warning color shades\n  --md-warning-fg-color:              hsla(0, 0%, 0%, 0.87);\n  --md-warning-bg-color:              hsla(60, 100%, 80%, 1);\n\n  // Footer color shades\n  --md-footer-fg-color:                hsla(0, 0%, 100%, 1);\n  --md-footer-fg-color--light:         hsla(0, 0%, 100%, 0.7);\n  --md-footer-fg-color--lighter:       hsla(0, 0%, 100%, 0.45);\n  --md-footer-bg-color:                hsla(0, 0%, 0%, 0.87);\n  --md-footer-bg-color--dark:          hsla(0, 0%, 0%, 0.32);\n\n  // Shadow depth 1\n  --md-shadow-z1:\n    0 #{px2rem(4px)} #{px2rem(10px)} hsla(0, 0%, 0%, 0.05),\n    0 0              #{px2rem(1px)}  hsla(0, 0%, 0%, 0.1);\n\n  // Shadow depth 2\n  --md-shadow-z2:\n    0 #{px2rem(4px)} #{px2rem(10px)} hsla(0, 0%, 0%, 0.1),\n    0 0              #{px2rem(1px)}  hsla(0, 0%, 0%, 0.25);\n\n  // Shadow depth 3\n  --md-shadow-z3:\n    0 #{px2rem(4px)} #{px2rem(10px)} hsla(0, 0%, 0%, 0.2),\n    0 0              #{px2rem(1px)}  hsla(0, 0%, 0%, 0.35);\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Icon\n.md-icon {\n\n  // SVG defaults\n  svg {\n    display: block;\n    width: px2rem(24px);\n    height: px2rem(24px);\n    fill: currentcolor;\n  }\n}\n\n// sphinx-immaterial: change display for inline icons\n.si-icon-inline::before {\n  display: inline-flex;\n  width: px2em(18px);\n  height: px2em(18px);\n  vertical-align: text-top;\n  content: \"\";\n  background-color: var(--md-default-fg-color);\n  mask-repeat: no-repeat;\n  mask-position: center;\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules: font definitions\n// ----------------------------------------------------------------------------\n\n// Enable font-smoothing in Webkit and FF\nbody {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n\n  // Font with fallback for body copy\n  --md-text-font-family:\n    var(--md-text-font, _),\n    -apple-system, BlinkMacSystemFont, Helvetica, Arial, sans-serif;\n\n  // Font with fallback for code\n  --md-code-font-family:\n    var(--md-code-font, _),\n    SFMono-Regular, Consolas, Menlo, monospace;\n}\n\n// Define default fonts\nbody,\ninput,\naside {\n  font-family: var(--md-text-font-family);\n  font-feature-settings: \"kern\", \"liga\";\n  color: var(--md-typeset-color);\n}\n\n// Define monospaced fonts\ncode,\npre,\nkbd {\n  font-family: var(--md-code-font-family);\n  font-feature-settings: \"kern\";\n}\n\n// ----------------------------------------------------------------------------\n// Rules: typesetted content\n// ----------------------------------------------------------------------------\n\n// General variables\n:root {\n  --md-typeset-table-sort-icon: svg-load(\"material/sort.svg\");\n  --md-typeset-table-sort-icon--asc: svg-load(\"material/sort-ascending.svg\");\n  --md-typeset-table-sort-icon--desc: svg-load(\"material/sort-descending.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Content that is typeset - if possible, all margins, paddings and font sizes\n// should be set in ems, so nested blocks (e.g. admonitions) render correctly.\n.md-typeset {\n  font-size: px2rem(16px);\n  line-height: 1.6;\n  overflow-wrap: break-word;\n  color-adjust: exact;\n\n  // [print]: We'll use a smaller `font-size` for printing, so code examples\n  // don't break too early, and `16px` looks too big anyway.\n  @media print {\n    font-size: px2rem(13.6px);\n  }\n\n  // Default spacing\n  ul,\n  ol,\n  dl,\n  figure,\n  blockquote,\n  pre {\n    margin-block: 1em;\n  }\n\n  // Headline on level 1\n  h1 {\n    margin: 0 0 px2em(40px, 32px);\n    font-size: px2em(32px);\n    font-weight: 300;\n    line-height: 1.3;\n    color: var(--md-default-fg-color--light);\n    letter-spacing: -0.01em;\n  }\n\n  // Headline on level 2\n  h2 {\n    margin: px2em(40px, 25px) 0 px2em(16px, 25px);\n    font-size: px2em(25px);\n    font-weight: 300;\n    line-height: 1.4;\n    letter-spacing: -0.01em;\n  }\n\n  // Headline on level 3\n  h3 {\n    margin: px2em(32px, 20px) 0 px2em(16px, 20px);\n    font-size: px2em(20px);\n    font-weight: 400;\n    line-height: 1.5;\n    letter-spacing: -0.01em;\n  }\n\n  // Headline on level 3 following level 2\n  h2 + h3 {\n    margin-top: px2em(16px, 20px);\n  }\n\n  // Headline on level 4\n  h4 {\n    margin: px2em(16px) 0;\n    font-weight: 700;\n    letter-spacing: -0.01em;\n  }\n\n  // Headline on level 5-6\n  h5,\n  h6 {\n    margin: px2em(16px, 12.8px) 0;\n    font-size: px2em(12.8px);\n    font-weight: 700;\n    color: var(--md-default-fg-color--light);\n    letter-spacing: -0.01em;\n  }\n\n  // Headline on level 5\n  h5 {\n    text-transform: uppercase;\n\n    // Don't uppercase code blocks\n    code {\n      text-transform: none;\n    }\n  }\n\n  // Horizontal separator\n  hr {\n    display: flow-root;\n    margin: 1.5em 0;\n    border-bottom: px2rem(1px) solid var(--md-default-fg-color--lightest);\n  }\n\n  // Text link\n  a {\n    color: var(--md-typeset-a-color);\n    word-break: break-word;\n\n    // Also enable color transition on pseudo elements\n    &,\n    &::before {\n      transition: color 125ms;\n    }\n\n    // Text link on focus/hover\n    &:is(:focus, :hover) {\n      color: var(--md-accent-fg-color);\n\n      // Inline code block\n      code {\n        background-color: var(--md-accent-fg-color--transparent);\n      }\n    }\n\n    // Inline code block\n    code {\n      color: currentcolor;\n      transition: background-color 125ms;\n    }\n\n    // Show outline for keyboard devices\n    &.focus-visible {\n      outline-color: var(--md-accent-fg-color);\n      outline-offset: px2rem(4px);\n    }\n  }\n\n  // Code block\n  code,\n  pre,\n  kbd {\n    font-variant-ligatures: none;\n    color: var(--md-code-fg-color);\n    direction: ltr;\n\n    // [print]: Wrap text and hide scollbars\n    @media print {\n      white-space: pre-wrap;\n    }\n  }\n\n  // Inline code block\n  code {\n    padding: 0 px2em(4px, 13.6px);\n    font-size: px2em(13.6px);\n    word-break: break-word;\n    background-color: var(--md-code-bg-color);\n    border-radius: px2rem(2px);\n    box-decoration-break: clone;\n\n    // Hide outline for pointer devices\n    &:not(.focus-visible) {\n      outline: none;\n      -webkit-tap-highlight-color: transparent;\n    }\n  }\n\n  // Unformatted content\n  pre {\n    position: relative;\n    display: flow-root;\n    line-height: 1.4;\n\n    // Code block\n    > code {\n      display: block;\n      padding: px2em(10.5px, 13.6px) px2em(16px, 13.6px);\n      margin: 0;\n      overflow: auto;\n      word-break: normal;\n      touch-action: auto;\n      scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n      scrollbar-width: thin;\n      outline-color: var(--md-accent-fg-color);\n      box-shadow: none;\n      box-decoration-break: slice;\n\n      // Code block on hover\n      &:hover {\n        scrollbar-color: var(--md-accent-fg-color) transparent;\n      }\n\n      // Webkit scrollbar\n      &::-webkit-scrollbar {\n        width: px2rem(4px);\n        height: px2rem(4px);\n      }\n\n      // Webkit scrollbar thumb\n      &::-webkit-scrollbar-thumb {\n        background-color: var(--md-default-fg-color--lighter);\n\n        // Webkit scrollbar thumb on hover\n        &:hover {\n          background-color: var(--md-accent-fg-color);\n        }\n      }\n    }\n  }\n\n  // sphinx-immaterial: reset margin for code-blocks with a caption\n  .code-block-caption + .notranslate {\n    pre,\n    .highlighttable {\n      margin-top: 0;\n    }\n  }\n\n  // Keyboard key\n  kbd {\n    display: inline-block;\n    padding: 0 px2em(8px, 12px);\n    font-size: px2em(12px);\n    color: var(--md-default-fg-color);\n    word-break: break-word;\n    vertical-align: text-top;\n    background-color: var(--md-typeset-kbd-color);\n    border-radius: px2rem(2px);\n    box-shadow:\n      0 px2rem(2px)  0 px2rem(1px) var(--md-typeset-kbd-border-color),\n      0 px2rem(2px)  0             var(--md-typeset-kbd-border-color),\n      0 px2rem(-2px) px2rem(4px)   var(--md-typeset-kbd-accent-color) inset;\n  }\n\n  // Text highlighting marker\n  mark {\n    color: inherit;\n    word-break: break-word;\n    background-color: var(--md-typeset-mark-color);\n    box-decoration-break: clone;\n  }\n\n  // Abbreviation\n  abbr {\n    text-decoration: none;\n    cursor: help;\n    border-bottom: px2rem(1px) dotted var(--md-default-fg-color--light);\n  }\n\n  // Small text\n  small {\n    opacity: 0.75;\n  }\n\n  // Superscript and subscript\n  sup,\n  sub {\n    margin-inline-start: px2em(1px, 12.8px);\n  }\n\n  // Blockquotes, possibly nested\n  blockquote {\n    padding-inline-start: px2rem(12px);\n    margin-inline: 0;\n    color: var(--md-default-fg-color--light);\n    border-inline-start: px2rem(4px) solid var(--md-default-fg-color--lighter);\n  }\n\n  // Unordered list\n  ul {\n    list-style-type: disc;\n\n    // Hack: allow to override `list-style-type` via `type`, without breaking\n    // compatibility for explicitly setting it in CSS - see https://t.ly/izJyH\n    &[type] {\n      list-style-type: revert-layer;\n    }\n  }\n\n  // Unordered and ordered list\n  ul,\n  ol {\n    padding: 0;\n    margin-inline-start: px2em(10px);\n\n    // Adjust display mode if not hidden\n    &:not([hidden]) {\n      display: flow-root;\n    }\n\n    // 2nd layer nested ordered list\n    ol {\n      list-style-type: lower-alpha;\n\n      // 3rd layer nested ordered list\n      ol {\n        list-style-type: lower-roman;\n\n        // 4th layer nested ordered list\n        ol {\n          list-style-type: upper-alpha;\n\n          // 5th layer nested ordered list\n          ol {\n            list-style-type: upper-roman;\n          }\n        }\n      }\n    }\n\n    // Hack: allow to override `list-style-type` via `type`, without breaking\n    // compatibility for explicitly setting it in CSS - see https://t.ly/izJyH\n    &[type] {\n      list-style-type: revert-layer;\n    }\n\n    // List element\n    li {\n      margin-inline-start: px2em(20px);\n      margin-bottom: 0.5em;\n\n      // Adjust spacing\n      p,\n      blockquote {\n        margin: 0.5em 0;\n      }\n\n      // Adjust spacing on last child\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      // Nested list\n      :is(ul, ol) {\n        margin-block: 0.5em;\n        margin-inline-start: px2em(10px);\n      }\n    }\n  }\n\n  // Definition list\n  dd {\n    margin-block: 1em 1.5em;\n    margin-inline-start: px2em(30px);\n  }\n\n  // Image or video\n  img,\n  svg,\n  video {\n    max-width: 100%;\n    height: auto;\n  }\n\n  // Image\n  img {\n\n    // Adjust spacing when left-aligned\n    &[align=\"left\"] {\n      margin: 1em;\n      margin-left: 0;\n    }\n\n    // Adjust spacing when right-aligned\n    &[align=\"right\"] {\n      margin: 1em;\n      margin-right: 0;\n    }\n\n    // Adjust spacing when sole children\n    &[align]:only-child {\n      margin-top: 0;\n    }\n  }\n\n  // Figure\n  figure {\n    display: flow-root;\n    width: fit-content;\n    max-width: 100%;\n    margin: 1em auto;\n    text-align: center;\n\n    // Figure images\n    img {\n      display: block;\n      margin: 0 auto;\n    }\n  }\n\n  // Figure caption\n  figcaption {\n    max-width: px2rem(480px);\n    margin: 1em auto;\n    font-style: italic;\n  }\n\n  // Limit width to container\n  iframe {\n    max-width: 100%;\n  }\n\n  // Data table\n  table.data:not(.plain) {\n    // sphinx-immaterial: set `display: block` rather than `display:\n    // inline-block` in order to allow center and right alignment to\n    // work.\n    display: block;\n    // sphinx-immaterial: set `width: max-content;` to ensure the\n    // table doesn't always fill the full width of its parent, as\n    // happens by default with `display: block`.\n    width: max-content;\n    max-width: 100%;\n    overflow: auto;\n    font-size: px2rem(12.8px);\n    touch-action: auto;\n    background-color: var(--md-default-bg-color);\n    border: px2rem(1px) solid var(--md-typeset-table-color);\n    border-radius: px2rem(2px);\n\n    // [print]: Reset display mode so table header wraps when printing\n    @media print {\n      display: table;\n    }\n\n    // Due to margin collapse because of the necessary inline-block hack, we\n    // cannot increase the bottom margin on the table, so we just increase the\n    // top margin on the following element\n    + * {\n      margin-top: 1.5em;\n    }\n\n    // Elements in table heading and cell\n    :is(th, td) > * {\n\n      // Adjust spacing on first child\n      &:first-child {\n        margin-top: 0;\n      }\n\n      // Adjust spacing on last child\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n\n    // Table heading and cell\n    :is(th, td):not([align], .align-center, .align-left, .align-right) {\n      text-align: left;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        text-align: right;\n      }\n    }\n\n    // Table heading\n    th {\n      min-width: px2rem(100px);\n      padding: px2em(12px, 12.8px) px2em(16px, 12.8px);\n      font-weight: 700;\n      vertical-align: top;\n    }\n\n    // Table cell\n    td {\n      padding: px2em(12px, 12.8px) px2em(16px, 12.8px);\n      vertical-align: top;\n      border-top: px2rem(1px) solid var(--md-typeset-table-color);\n    }\n\n    // Table body row\n    tbody tr {\n      transition: background-color 125ms;\n\n      // Table row on hover\n      &:hover {\n        background-color: var(--md-typeset-table-color--light);\n        box-shadow: 0 px2rem(1px) 0 var(--md-default-bg-color) inset;\n      }\n    }\n\n    // Text link in table\n    a {\n      word-break: normal;\n    }\n  }\n\n  // Sortable table\n  table th[role=\"columnheader\"] {\n    cursor: pointer;\n\n    // Sort icon\n    &::after {\n      display: inline-block;\n      width: 1.2em;\n      height: 1.2em;\n      margin-inline-start: 0.5em;\n      vertical-align: text-bottom;\n      content: \"\";\n      mask-image: var(--md-typeset-table-sort-icon);\n      mask-repeat: no-repeat;\n      mask-size: contain;\n      transition: background-color 125ms;\n    }\n\n    // Show sort icon on hover\n    &:hover::after {\n      background-color: var(--md-default-fg-color--lighter);\n    }\n\n    // Sort ascending icon\n    &[aria-sort=\"ascending\"]::after {\n      background-color: var(--md-default-fg-color--light);\n      mask-image: var(--md-typeset-table-sort-icon--asc);\n    }\n\n    // Sort descending icon\n    &[aria-sort=\"descending\"]::after {\n      background-color: var(--md-default-fg-color--light);\n      mask-image: var(--md-typeset-table-sort-icon--desc);\n    }\n  }\n\n  // Data table scroll wrapper\n  &__scrollwrap {\n    margin: 1em px2rem(-16px);\n    overflow-x: auto;\n    touch-action: auto;\n  }\n\n  // Data table wrapper\n  &__table {\n    display: inline-block;\n    padding: 0 px2rem(16px);\n    margin-bottom: 0.5em;\n\n    // [print]: Reset display mode so table header wraps when printing\n    @media print {\n      display: block;\n    }\n\n    // Data table\n    html & table {\n      display: table;\n      width: 100%;\n      margin: 0;\n      overflow: hidden;\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: top-level\n// ----------------------------------------------------------------------------\n\n// [mobile -]: Align with body copy\n@include break-to-device(mobile) {\n\n  // Top-level unformatted content\n  .md-content__inner > pre {\n    margin: 1em px2rem(-16px);\n\n    // Code block\n    code {\n      border-radius: 0;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n@use \"sass:list\";\n@use \"sass:map\";\n@use \"sass:math\";\n\n// ----------------------------------------------------------------------------\n// Variables\n// ----------------------------------------------------------------------------\n\n///\n/// Device-specific breakpoints\n///\n/// @example\n///   $break-devices: (\n///     mobile: (\n///       portrait:  220px  479px,\n///       landscape: 480px  719px\n///     ),\n///     tablet: (\n///       portrait:  720px  959px,\n///       landscape: 960px  1219px\n///     ),\n///     screen: (\n///       small:     1220px 1599px,\n///       medium:    1600px 1999px,\n///       large:     2000px\n///     )\n///   );\n///\n$break-devices: () !default;\n\n// ----------------------------------------------------------------------------\n// Helpers\n// ----------------------------------------------------------------------------\n\n///\n/// Choose minimum and maximum device widths\n///\n@function break-select-min-max($devices) {\n  $min: 1000000;\n  $max: 0;\n  @each $key, $value in $devices {\n    @while type-of($value) == map {\n      $value: break-select-min-max($value);\n    }\n    @if type-of($value) == list {\n      @each $number in $value {\n        @if type-of($number) == number {\n          $min: math.min($number, $min);\n          @if $max {\n            $max: math.max($number, $max);\n          }\n        } @else {\n          @error \"Invalid number: #{$number}\";\n        }\n      }\n    } @else if type-of($value) == number {\n      $min: math.min($value, $min);\n      $max: null;\n    } @else {\n      @error \"Invalid value: #{$value}\";\n    }\n  }\n  @return $min, $max;\n}\n\n///\n/// Select minimum and maximum widths for a device breakpoint\n///\n@function break-select-device($device) {\n  $current: $break-devices;\n  @for $n from 1 through length($device) {\n    @if type-of($current) == map {\n      $current: map.get($current, list.nth($device, $n));\n    } @else {\n      @error \"Invalid device map: #{$devices}\";\n    }\n  }\n  @if type-of($current) == list or type-of($current) == number {\n    $current: (default: $current);\n  }\n  @return break-select-min-max($current);\n}\n\n// ----------------------------------------------------------------------------\n// Mixins\n// ----------------------------------------------------------------------------\n\n///\n/// A minimum-maximum media query breakpoint\n///\n@mixin break-at($breakpoint) {\n  @if type-of($breakpoint) == number {\n    @media screen and (min-width: $breakpoint) {\n      @content;\n    }\n  } @else if type-of($breakpoint) == list {\n    $min: list.nth($breakpoint, 1);\n    $max: list.nth($breakpoint, 2);\n    @if type-of($min) == number and type-of($max) == number {\n      @media screen and (min-width: $min) and (max-width: $max) {\n        @content;\n      }\n    } @else {\n      @error \"Invalid breakpoint: #{$breakpoint}\";\n    }\n  } @else {\n    @error \"Invalid breakpoint: #{$breakpoint}\";\n  }\n}\n\n///\n/// An orientation media query breakpoint\n///\n@mixin break-at-orientation($breakpoint) {\n  @if type-of($breakpoint) == string {\n    @media screen and (orientation: $breakpoint) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid breakpoint: #{$breakpoint}\";\n  }\n}\n\n///\n/// A maximum-aspect-ratio media query breakpoint\n///\n@mixin break-at-ratio($breakpoint) {\n  @if type-of($breakpoint) == number {\n    @media screen and (max-aspect-ratio: $breakpoint) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid breakpoint: #{$breakpoint}\";\n  }\n}\n\n///\n/// A minimum-maximum media query device breakpoint\n///\n@mixin break-at-device($device) {\n  @if type-of($device) == string {\n    $device: $device,;\n  }\n  @if type-of($device) == list {\n    $breakpoint: break-select-device($device);\n    @if list.nth($breakpoint, 2) {\n      $min: list.nth($breakpoint, 1);\n      $max: list.nth($breakpoint, 2);\n\n      @media screen and (min-width: $min) and (max-width: $max) {\n        @content;\n      }\n    } @else {\n      @error \"Invalid device: #{$device}\";\n    }\n  } @else {\n    @error \"Invalid device: #{$device}\";\n  }\n}\n\n///\n/// A minimum media query device breakpoint\n///\n@mixin break-from-device($device) {\n  @if type-of($device) == string {\n    $device: $device,;\n  }\n  @if type-of($device) == list {\n    $breakpoint: break-select-device($device);\n    $min: list.nth($breakpoint, 1);\n\n    @media screen and (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid device: #{$device}\";\n  }\n}\n\n///\n/// A maximum media query device breakpoint\n///\n@mixin break-to-device($device) {\n  @if type-of($device) == string {\n    $device: $device,;\n  }\n  @if type-of($device) == list {\n    $breakpoint: break-select-device($device);\n    $max: list.nth($breakpoint, 2);\n\n    @media screen and (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid device: #{$device}\";\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Author, i.e., GitHub user\n  .md-author {\n    position: relative;\n    display: block;\n    flex-shrink: 0;\n    width: px2rem(32px);\n    height: px2rem(32px);\n    overflow: hidden;\n    border-radius: 100%;\n    transition:\n      color 125ms,\n      transform 125ms;\n\n    // Author image\n    img {\n      display: block;\n    }\n\n    // More authors\n    &--more {\n      font-size: px2rem(12px);\n      font-weight: 700;\n      line-height: px2rem(32px);\n      color: var(--md-default-fg-color--lighter);\n      text-align: center;\n      background: var(--md-default-fg-color--lightest);\n    }\n\n    // Enlarge image\n    &--long {\n      width: px2rem(48px);\n      height: px2rem(48px);\n    }\n  }\n\n  // Author link\n  a.md-author {\n    transform: scale(1);\n\n    // Author image\n    img {\n      filter: grayscale(100%) opacity(75%);\n      // Hack: also apply this here, in order to mitigate browser glitches in\n      // Chrome and Edge when hovering the avatar - see https://t.ly/Q3ECC\n      border-radius: 100%;\n      transition: filter 125ms;\n    }\n\n    // Author on focus/hover\n    &:is(:focus, :hover) {\n      z-index: 1;\n      transform: scale(1.1);\n\n      // Author image\n      img {\n        filter: grayscale(0%);\n      }\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Banner for announcements and warnings\n.md-banner {\n  overflow: auto;\n  color: var(--md-footer-fg-color);\n  background-color: var(--md-footer-bg-color);\n\n  // [print]: Hide banner\n  @media print {\n    display: none;\n  }\n\n  // Banner with warning\n  &--warning {\n    color: var(--md-warning-fg-color);\n    background-color: var(--md-warning-bg-color);\n  }\n\n  // Banner wrapper\n  &__inner {\n    padding: 0 px2rem(16px);\n    margin: px2rem(12px) auto;\n    font-size: px2rem(14px);\n  }\n\n  // Banner button\n  &__button {\n    float: inline-end;\n    color: inherit;\n    cursor: pointer;\n    transition: opacity 250ms;\n\n    // [no-js]: Hide button\n    .no-js & {\n      display: none;\n    }\n\n    // Button on hover\n    &:hover {\n      opacity: 0.7;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules: base grid and containers\n// ----------------------------------------------------------------------------\n\n// Stretch container to viewport and set base `font-size`\nhtml {\n  height: 100%;\n  overflow-x: hidden;\n  // Hack: normally, we would set the base `font-size` to `62.5%`, so we can\n  // base all calculations on `10px`, but Chromium and Chrome define a minimal\n  // `font-size` of `12px` if the system language is set to Chinese. For this\n  // reason we just double the `font-size` and set it to `20px`.\n  //\n  // See https://github.com/squidfunk/mkdocs-material/issues/911\n  font-size: 125%;\n\n  // [screen medium +]: Set base `font-size` to `11px`\n  @include break-from-device(screen medium) {\n    font-size: 137.5%;\n  }\n\n  // [screen large +]: Set base `font-size` to `12px`\n  @include break-from-device(screen large) {\n    font-size: 150%;\n  }\n}\n\n// Stretch body to container - flexbox is used, so the footer will always be\n// aligned to the bottom of the viewport\nbody {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  min-height: 100%;\n  // Hack: reset `font-size` to `10px`, so the spacing for all inline elements\n  // is correct again. Otherwise the spacing would be based on `20px`.\n  font-size: px2rem(10px);\n  background-color: var(--md-default-bg-color);\n\n  // [print]: Omit flexbox layout due to a Firefox bug (https://mzl.la/39DgR3m)\n  @media print {\n    display: block;\n  }\n\n  // Body in locked state\n  &[data-md-scrolllock] {\n\n    // [tablet portrait -]: Omit scroll bubbling\n    @include break-to-device(tablet portrait) {\n      position: fixed;\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n\n// Grid container - this class is applied to wrapper elements within the\n// header, content area and footer, and makes sure that their width is limited\n// to `1220px`, and they are rendered centered if the screen is larger.\n.md-grid {\n  max-width: px2rem(1220px);\n  margin-inline: auto;\n}\n\n// Main container\n.md-container {\n  display: flex;\n  flex-grow: 1;\n  flex-direction: column;\n\n  // [print]: Omit flexbox layout due to a Firefox bug (https://mzl.la/39DgR3m)\n  @media print {\n    display: block;\n  }\n}\n\n// Main area - stretch to remaining space of container\n.md-main {\n  flex-grow: 1;\n\n  // Main area wrapper\n  &__inner {\n    display: flex;\n    height: 100%;\n    margin-top: px2rem(24px + 6px);\n  }\n}\n\n// Add ellipsis in case of overflowing text\n.md-ellipsis {\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n// ----------------------------------------------------------------------------\n// Rules: navigational elements\n// ----------------------------------------------------------------------------\n\n// Toggle - this class is applied to checkbox elements, which are used to\n// implement the CSS-only drawer and navigation, as well as the search\n.md-toggle {\n  display: none;\n}\n\n// Option - this class is applied to radio elements, which are used to\n// implement the color palette toggle\n.md-option {\n  position: absolute;\n  width: 0;\n  height: 0;\n  opacity: 0;\n\n  // Option label for checked radio button\n  &:checked + label:not([hidden]) {\n    display: block;\n  }\n\n  // Show outline for keyboard devices\n  &.focus-visible + label {\n    outline-style: auto;\n    outline-color: var(--md-accent-fg-color);\n  }\n}\n\n// Skip link\n.md-skip {\n  position: fixed;\n  // Hack: if we don't set the negative `z-index`, the skip link will force the\n  // creation of new layers when code blocks are near the header on scrolling\n  z-index: -1;\n  padding: px2rem(6px) px2rem(10px);\n  margin: px2rem(10px);\n  font-size: px2rem(12.8px);\n  color: var(--md-default-bg-color);\n  background-color: var(--md-default-fg-color);\n  border-radius: px2rem(2px);\n  outline-color: var(--md-accent-fg-color);\n  opacity: 0;\n  transform: translateY(px2rem(8px));\n\n  // Show skip link on focus\n  &:focus {\n    z-index: 10;\n    opacity: 1;\n    transition:\n      transform 250ms cubic-bezier(0.4, 0, 0.2, 1),\n      opacity   175ms 75ms;\n    transform: translateY(0);\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: print styles\n// ----------------------------------------------------------------------------\n\n// Add margins to page\n@page {\n  margin: 25mm;\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Clipboard button variables\n:root {\n  --md-clipboard-icon: svg-load(\"material/content-copy.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Clipboard button\n.md-clipboard {\n  position: absolute;\n  top: px2em(8px);\n  right: px2em(8px);\n  z-index: 1;\n  width: px2em(24px);\n  height: px2em(24px);\n  color: var(--md-default-fg-color--lightest);\n  cursor: pointer;\n  border-radius: px2rem(2px);\n  outline-color: var(--md-accent-fg-color);\n  outline-offset: px2rem(2px);\n  transition: color 250ms;\n\n  // [print]: Hide button\n  @media print {\n    display: none;\n  }\n\n  // Hide outline for pointer devices\n  &:not(.focus-visible) {\n    outline: none;\n    -webkit-tap-highlight-color: transparent;\n  }\n\n  // Darken color on code block hover\n  :hover > & {\n    color: var(--md-default-fg-color--light);\n  }\n\n  // Button on focus/hover\n  &:is(:focus, :hover) {\n    color: var(--md-accent-fg-color);\n  }\n\n  // Button icon - the width and height are defined in `em`, so the size is\n  // automatically adjusted for nested code blocks (e.g. in admonitions)\n  &::after {\n    display: block;\n    width: px2em(18px);\n    height: px2em(18px);\n    margin: 0 auto;\n    content: \"\";\n    background-color: currentcolor;\n    mask-image: var(--md-clipboard-icon);\n    mask-repeat: no-repeat;\n    mask-position: center;\n    mask-size: contain;\n  }\n\n  // Inline clipboard button\n  &--inline {\n    cursor: pointer;\n\n    // Code block\n    code {\n      transition:\n        color            250ms,\n        background-color 250ms;\n    }\n\n    // Code block on focus/hover\n    &:is(:focus, :hover) code {\n      color: var(--md-accent-fg-color);\n      background-color: var(--md-accent-fg-color--transparent);\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Code blocks\n.md-code {\n\n  // Code block content - when line spans are enabled, we can opt into using\n  // grid-based rendering, which makes highlighted lines stretch.\n  .md-typeset &__content {\n    display: grid;\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Keyframes\n// ----------------------------------------------------------------------------\n\n// Show consent\n@keyframes consent {\n  0% {\n    opacity: 0;\n    transform: translateY(100%);\n  }\n\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n// Show consent overlay\n@keyframes overlay {\n  0% {\n    opacity: 0;\n  }\n\n  100% {\n    opacity: 1;\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Consent\n.md-consent {\n\n  // Consent overlay\n  &__overlay {\n    position: fixed;\n    top: 0;\n    z-index: 5;\n    width: 100%;\n    height: 100%;\n    background-color: hsla(0, 0%, 0%, 0.54);\n    backdrop-filter: blur(px2rem(2px));\n    opacity: 1;\n    animation: overlay 250ms both;\n  }\n\n  // Consent wrapper\n  &__inner {\n    position: fixed;\n    bottom: 0;\n    z-index: 5;\n    width: 100%;\n    max-height: 100%;\n    padding: 0;\n    overflow: auto;\n    background-color: var(--md-default-bg-color);\n    border: 0;\n    border-radius: px2rem(2px);\n    box-shadow:\n      0 0           px2rem(4px) rgba(0, 0, 0, 0.1),\n      0 px2rem(4px) px2rem(8px) rgba(0, 0, 0, 0.2);\n    animation: consent 500ms cubic-bezier(0.1, 0.7, 0.1, 1) both;\n  }\n\n  // Consent form\n  &__form {\n    padding: px2rem(16px);\n  }\n\n  // Consent settings\n  &__settings {\n    display: none;\n    margin: 1em 0;\n\n    // Show settings\n    input:checked + & {\n      display: block;\n    }\n  }\n\n  // Consent controls\n  &__controls {\n    margin-bottom: px2rem(16px);\n\n    // Consent control button\n    .md-typeset & .md-button {\n      display: inline;\n\n      // [tablet +]: Align buttons horizontally\n      @include break-to-device(mobile) {\n        display: block;\n        width: 100%;\n        margin-top: px2rem(8px);\n        text-align: center;\n      }\n    }\n  }\n\n  // Ensure users realize that labels are clickaböe\n  label {\n    cursor: pointer;\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Content area\n.md-content {\n  flex-grow: 1;\n  // Hack: we must use `min-width: 0`, so the content area is capped by the\n  // dimensions of its parent. Otherwise, long code blocks might lead to a\n  // wider content area which will overflow. See https://bit.ly/3bP3f8k\n  min-width: 0;\n\n  // Content wrapper\n  &__inner {\n    padding-top: px2rem(12px);\n    margin: 0 px2rem(16px) px2rem(24px);\n\n    // [screen +]: Adjust spacing between content area and sidebars\n    @include break-from-device(screen) {\n\n      // Sidebar with navigation is visible\n      .md-sidebar--primary:not([hidden]) ~ .md-content > & {\n        margin-inline-start: px2rem(24px);\n      }\n\n      // Sidebar with table of contents is visible\n      .md-sidebar--secondary:not([hidden]) ~ .md-content > & {\n        margin-inline-end: px2rem(24px);\n      }\n    }\n\n    // Hack: add pseudo element for spacing, as the overflow of the content\n    // container may not be hidden due to an imminent offset error on targets\n    &::before {\n      display: block;\n      height: px2rem(8px);\n      content: \"\";\n    }\n\n    // Adjust spacing on last child\n    > :last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  // Button inside of the content area - these buttons are meant for actions on\n  // a document-level, i.e. linking to related source code files, printing etc.\n  &__button {\n    float: inline-end;\n    padding: 0;\n    margin: px2rem(8px) 0;\n    margin-inline-start: px2rem(8px);\n\n    // [print]: Hide buttons\n    @media print {\n      display: none;\n    }\n\n    // Adjust default link color for icons\n    .md-typeset & {\n      color: var(--md-default-fg-color--lighter);\n    }\n\n    // Align with body copy located next to icon\n    svg {\n      display: inline;\n      vertical-align: top;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        transform: scaleX(-1);\n      }\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Dialog\n.md-dialog {\n  position: fixed;\n  inset-inline-end: px2rem(16px);\n  bottom: px2rem(16px);\n  z-index: 4;\n  min-width: px2rem(222px);\n  padding: px2rem(8px) px2rem(12px);\n  pointer-events: none;\n  background-color: var(--md-default-fg-color);\n  border-radius: px2rem(2px);\n  box-shadow: var(--md-shadow-z3);\n  opacity: 0;\n  transition:\n    transform 0ms   400ms,\n    opacity   400ms;\n  transform: translateY(100%);\n\n  // [print]: Hide dialog\n  @media print {\n    display: none;\n  }\n\n  // Active dialog\n  &--active {\n    pointer-events: initial;\n    opacity: 1;\n    transition:\n      transform 400ms cubic-bezier(0.075, 0.85, 0.175, 1),\n      opacity   400ms;\n    transform: translateY(0);\n  }\n\n  // Dialog wrapper\n  &__inner {\n    font-size: px2rem(14px);\n    color: var(--md-default-bg-color);\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Was this page helpful?\n.md-feedback {\n  margin: 2em 0 1em;\n  text-align: center;\n\n  // Feedback fieldset\n  fieldset {\n    padding: 0;\n    margin: 0;\n    border: none;\n  }\n\n  // Feedback title\n  &__title {\n    margin: 1em auto;\n    font-weight: 700;\n  }\n\n  // Feedback wrapper\n  &__inner {\n    position: relative;\n  }\n\n  // Feedback list\n  &__list {\n    position: relative;\n    display: flex;\n    flex-wrap: wrap;\n    place-content: baseline center;\n\n    // Feedback icon on hover\n    &:hover .md-icon:not(:disabled) {\n      color: var(--md-default-fg-color--lighter);\n    }\n\n    // Adjust height after submission\n    :disabled & {\n      min-height: px2rem(36px);\n    }\n  }\n\n  // Feedback icon\n  &__icon {\n    flex-shrink: 0;\n    margin: 0 px2rem(2px);\n    color: var(--md-default-fg-color--light);\n    cursor: pointer;\n    transition: color 125ms;\n\n    // Feedback icon on hover\n    &:not(:disabled).md-icon:hover {\n      color: var(--md-accent-fg-color);\n    }\n\n    // Feedback icon after submit\n    &:disabled {\n      color: var(--md-default-fg-color--lightest);\n      pointer-events: none;\n    }\n  }\n\n  // Feedback note\n  &__note {\n    position: relative;\n    opacity: 0;\n    transition:\n      transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1),\n      opacity   150ms;\n    transform: translateY(px2rem(8px));\n\n    // Feedback note value\n    > * {\n      max-width: px2rem(320px);\n      margin: 0 auto;\n    }\n\n    // Show after submission\n    :disabled & {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n\n  // [print]: Hide feedback\n  @media print {\n    display: none;\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Footer\n.md-footer {\n  color: var(--md-footer-fg-color);\n  background-color: var(--md-footer-bg-color);\n\n  // [print]: Hide footer\n  @media print {\n    display: none;\n  }\n\n  // Footer wrapper\n  &__inner {\n    justify-content: space-between;\n    padding: px2rem(4px);\n    overflow: auto;\n\n    // Footer is visible\n    &:not([hidden]) {\n      display: flex;\n    }\n  }\n\n  // Footer link to previous and next page\n  &__link {\n    display: flex;\n    // Hack: some browsers induce ellipsis on flex children that are set to\n    // `overflow: hidden` and `text-overflow: ellipsis`. Enforcing growth by\n    // a tiny factor seems to get rid of the ellipsis and renders the text as\n    // it should - see https://bit.ly/2ZUCXQ8\n    flex-grow: 0.01;\n    align-items: end;\n    max-width: 100%;\n    margin-block: px2rem(20px) px2rem(8px);\n    overflow: hidden;\n    outline-color: var(--md-accent-fg-color);\n    transition: opacity 250ms;\n\n    // Footer link on focus/hover\n    &:is(:focus, :hover) {\n      opacity: 0.7;\n    }\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & svg {\n      transform: scaleX(-1);\n    }\n\n    // [mobile -]: Adjust width to 25/75 and hide title\n    @include break-to-device(mobile) {\n\n      // Footer link to previous page\n      &--prev {\n        flex-shrink: 0;\n\n        // Hide footer title\n        .md-footer__title {\n          display: none;\n        }\n      }\n    }\n\n    // Footer link to next page\n    &--next {\n      margin-inline-start: auto;\n      text-align: right;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        text-align: left;\n      }\n    }\n  }\n\n  // Footer title\n  &__title {\n    flex-grow: 1;\n    max-width: calc(100% - #{px2rem(48px)});\n    padding: 0 px2rem(20px);\n    margin-bottom: px2rem(14px);\n    font-size: px2rem(18px);\n    white-space: nowrap;\n  }\n\n  // Footer link button\n  &__button {\n    padding: px2rem(8px);\n    margin: px2rem(4px);\n  }\n\n  // Footer link direction (i.e. prev and next)\n  &__direction {\n    font-size: px2rem(12.8px);\n    opacity: 0.7;\n  }\n}\n\n// Footer metadata\n.md-footer-meta {\n  background-color: var(--md-footer-bg-color--dark);\n\n  // Footer metadata wrapper\n  &__inner {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: space-between;\n    padding: px2rem(4px);\n  }\n\n  // Lighten color for non-hovered text links\n  html &.md-typeset a {\n    color: var(--md-footer-fg-color--light);\n\n    // Text link on focus/hover\n    &:is(:focus, :hover) {\n      color: var(--md-footer-fg-color);\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n\n// Copyright and theme information\n.md-copyright {\n  width: 100%;\n  padding: px2rem(8px) 0;\n  margin: auto px2rem(12px);\n  font-size: px2rem(12.8px);\n  color: var(--md-footer-fg-color--lighter);\n\n  // [tablet portrait +]: Show copyright and social links in one line\n  @include break-from-device(tablet portrait) {\n    width: auto;\n  }\n\n  // Footer copyright highlight - this is the upper part of the copyright and\n  // theme information, which will include a darker color than the theme link\n  &__highlight {\n    color: var(--md-footer-fg-color--light);\n  }\n}\n\n// ----------------------------------------------------------------------------\n\n// Social links\n.md-social {\n  display: inline-flex;\n  gap: px2rem(4px);\n  padding: px2rem(4px) 0 px2rem(12px);\n  margin: 0 px2rem(8px);\n\n  // [tablet portrait +]: Show copyright and social links in one line\n  @include break-from-device(tablet portrait) {\n    padding: px2rem(12px) 0;\n  }\n\n  // Footer social link\n  &__link {\n    display: inline-block;\n    width: px2rem(32px);\n    height: px2rem(32px);\n    text-align: center;\n\n    // Adjust line-height to match height for correct alignment\n    &::before {\n      line-height: 1.9;\n    }\n\n    // Fill icon with current color\n    svg {\n      max-height: px2rem(16px);\n      vertical-align: -25%;\n      fill: currentcolor;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Form button\n  .md-button {\n    display: inline-block;\n    padding: px2em(10px) px2em(32px);\n    font-weight: 700;\n    color: var(--md-primary-fg-color);\n    cursor: pointer;\n    border: px2rem(2px) solid currentcolor;\n    border-radius: px2rem(2px);\n    transition:\n      color            125ms,\n      background-color 125ms,\n      border-color     125ms;\n\n    // Primary button\n    &--primary {\n      color: var(--md-primary-bg-color);\n      background-color: var(--md-primary-fg-color);\n      border-color: var(--md-primary-fg-color);\n    }\n\n    // Button on focus/hover\n    &:is(:focus, :hover) {\n      color: var(--md-accent-bg-color);\n      background-color: var(--md-accent-fg-color);\n      border-color: var(--md-accent-fg-color);\n    }\n  }\n\n  // Form input\n  .md-input {\n    height: px2rem(36px);\n    padding: 0 px2rem(12px);\n    font-size: px2rem(16px);\n    border-bottom: px2rem(2px) solid var(--md-default-fg-color--lighter);\n    border-start-start-radius: px2rem(2px);\n    border-start-end-radius: px2rem(2px);\n    box-shadow: var(--md-shadow-z1);\n    transition:\n      border     250ms,\n      box-shadow 250ms;\n\n    // Input on focus/hover\n    &:is(:focus, :hover) {\n      border-bottom-color: var(--md-accent-fg-color);\n      box-shadow: var(--md-shadow-z2);\n    }\n\n    // Stretch to full width\n    &--stretch {\n      width: 100%;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Header - by default, the header will be sticky and stay always on top of the\n// viewport. If this behavior is not desired, just set `position: static`.\n.md-header {\n  position: sticky;\n  inset-inline: 0;\n  top: 0;\n  z-index: 4;\n  display: block;\n  color: var(--md-primary-bg-color);\n  background-color: var(--md-primary-fg-color);\n  // Hack: reduce jitter by adding a transparent box shadow of the same size\n  // so the size of the layer doesn't change during animation\n  box-shadow:\n    0 0           px2rem(4px) rgba(0, 0, 0, 0),\n    0 px2rem(4px) px2rem(8px) rgba(0, 0, 0, 0);\n\n  // [print]: Hide header\n  @media print {\n    display: none;\n  }\n\n  // Header is hidden\n  &[hidden] {\n    transition:\n      transform  250ms cubic-bezier(0.8, 0, 0.6, 1),\n      box-shadow 250ms;\n    transform: translateY(-100%);\n  }\n\n  // Header in shadow state, i.e. shadow is visible\n  &--shadow {\n    box-shadow:\n      0 0           px2rem(4px) rgba(0, 0, 0, 0.1),\n      0 px2rem(4px) px2rem(8px) rgba(0, 0, 0, 0.2);\n    transition:\n      transform  250ms cubic-bezier(0.1, 0.7, 0.1, 1),\n      box-shadow 250ms;\n  }\n\n  // Header wrapper\n  &__inner {\n    display: flex;\n    align-items: center;\n    padding: 0 px2rem(4px);\n  }\n\n  // Header button\n  &__button {\n    position: relative;\n    z-index: 1;\n    padding: px2rem(8px);\n    margin: px2rem(4px);\n    color: currentcolor;\n    vertical-align: middle;\n    cursor: pointer;\n    outline-color: var(--md-accent-fg-color);\n    transition: opacity 250ms;\n\n    // Button on hover\n    &:hover {\n      opacity: 0.7;\n    }\n\n    // Header button is visible\n    &:not([hidden]) {\n      display: inline-block;\n    }\n\n    // Hide outline for pointer devices\n    &:not(.focus-visible) {\n      outline: none;\n      -webkit-tap-highlight-color: transparent;\n    }\n\n    // Button with logo, pointing to `config.site_url`\n    &.md-logo {\n      padding: px2rem(8px);\n      margin: px2rem(4px);\n\n      // [tablet -]: Hide button\n      @include break-to-device(tablet) {\n        display: none;\n      }\n\n      // Image or icon\n      :is(img, svg) {\n        display: block;\n        width: auto;\n        height: px2rem(24px);\n        fill: currentcolor;\n      }\n    }\n\n    // Button for search\n    &[for=\"__search\"] {\n\n      // [tablet landscape +]: Hide button\n      @include break-from-device(tablet landscape) {\n        display: none;\n      }\n\n      // [no-js]: Hide button\n      .no-js & {\n        display: none;\n      }\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & svg {\n        transform: scaleX(-1);\n      }\n    }\n\n    // Button for drawer\n    &[for=\"__drawer\"] {\n\n      // [screen +]: Hide button\n      @include break-from-device(screen) {\n        display: none;\n      }\n    }\n  }\n\n  // Header topic\n  &__topic {\n    position: absolute;\n    display: flex;\n    max-width: 100%;\n    white-space: nowrap;\n    transition:\n      transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1),\n      opacity   150ms;\n\n    // Second header topic - title of the current page\n    & + & {\n      z-index: -1;\n      pointer-events: none;\n      opacity: 0;\n      transition:\n        transform 400ms cubic-bezier(1, 0.7, 0.1, 0.1),\n        opacity   150ms;\n      transform: translateX(px2rem(25px));\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        transform: translateX(px2rem(-25px));\n      }\n    }\n\n    // Adjust font weight of site title\n    &:first-child {\n      font-weight: 700;\n    }\n  }\n\n  // Header title\n  &__title {\n    flex-grow: 1;\n    height: px2rem(48px);\n    margin-inline: px2rem(20px) px2rem(8px);\n    font-size: px2rem(18px);\n    line-height: px2rem(48px);\n\n    // Header title in active state, i.e. page title is visible\n    &--active .md-header__topic {\n      z-index: -1;\n      pointer-events: none;\n      opacity: 0;\n      transition:\n        transform 400ms cubic-bezier(1, 0.7, 0.1, 0.1),\n        opacity   150ms;\n      transform: translateX(px2rem(-25px));\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        transform: translateX(px2rem(25px));\n      }\n\n      // Second header topic - title of the current page\n      + .md-header__topic {\n        z-index: 0;\n        pointer-events: initial;\n        opacity: 1;\n        transition:\n          transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1),\n          opacity   150ms;\n        transform: translateX(0);\n      }\n    }\n\n    // Add ellipsis in case of overflowing text\n    > .md-header__ellipsis {\n      position: relative;\n      width: 100%;\n      height: 100%;\n    }\n  }\n\n  // Header option\n  &__option {\n    display: flex;\n    flex-shrink: 0;\n    max-width: 100%;\n    white-space: nowrap;\n    transition:\n      max-width  0ms 250ms,\n      opacity  250ms 250ms;\n\n    // Hide toggle when search is active\n    [data-md-toggle=\"search\"]:checked ~ .md-header & {\n      max-width: 0;\n      opacity: 0;\n      transition:\n        max-width 0ms,\n        opacity   0ms;\n    }\n\n    // Hack: Firefox 117 introduces a bug where the browser scrolls the page by\n    // a small amount to the top every time the header button is focused. After\n    // investigating, we're confident that it seems to be caused by the input\n    // field being too close to the border - see https://t.ly/APO8l\n    > input {\n      bottom: 0;\n    }\n  }\n\n  // Repository information container\n  &__source {\n    display: none;\n\n    // [tablet landscape +]: Show repository information\n    @include break-from-device(tablet landscape) {\n      display: block;\n      width: px2rem(234px);\n      max-width: px2rem(234px);\n      margin-inline-start: px2rem(20px);\n    }\n\n    // [screen +]: Adjust spacing of search bar\n    @include break-from-device(screen) {\n      margin-inline-start: px2rem(28px);\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2020 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Hero teaser\n.md-hero {\n  overflow: hidden;\n  font-size: px2rem(20px);\n  color: var(--md-primary-bg-color);\n  background-color: var(--md-primary-fg-color);\n  transition: background 250ms;\n\n  // Inner wrapper\n  &__inner {\n    padding: px2rem(16px) px2rem(16px) px2rem(8px);\n    margin-top: px2rem(20px);\n    transition:\n      transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1),\n      opacity   250ms;\n    transition-delay: 100ms;\n\n    // [tablet -]: Compensate for missing tabs\n    @include break-to-device(tablet) {\n      margin-top: px2rem(48px);\n      margin-bottom: px2rem(24px);\n    }\n\n    // Fade-out tabs background upon scrolling\n    [data-md-state=\"hidden\"] & {\n      pointer-events: none;\n      opacity: 0;\n      transition:\n        transform   0ms 400ms,\n        opacity   100ms   0ms;\n      transform: translateY(px2rem(12.5px));\n    }\n\n    // Adjust bottom spacing if there are no tabs\n    .md-hero--expand & {\n      margin-bottom: px2rem(24px);\n    }\n  }\n}\n", "// ==========================================================================\n//\n// Name:        UI Color Palette\n// Description: The color palette of material design.\n// Version:     2.3.1\n//\n// Author:      <PERSON>\n// Git:         https://github.com/mrmlnc/material-color\n//\n// twitter:     @mrmlnc\n//\n// ==========================================================================\n\n\n//\n// List of base colors\n//\n\n// $clr-red\n// $clr-pink\n// $clr-purple\n// $clr-deep-purple\n// $clr-indigo\n// $clr-blue\n// $clr-light-blue\n// $clr-cyan\n// $clr-teal\n// $clr-green\n// $clr-light-green\n// $clr-lime\n// $clr-yellow\n// $clr-amber\n// $clr-orange\n// $clr-deep-orange\n// $clr-brown\n// $clr-grey\n// $clr-blue-grey\n// $clr-black\n// $clr-white\n\n\n//\n// Red\n//\n\n$clr-red-list: (\n  \"base\": #f44336,\n  \"50\":   #ffebee,\n  \"100\":  #ffcdd2,\n  \"200\":  #ef9a9a,\n  \"300\":  #e57373,\n  \"400\":  #ef5350,\n  \"500\":  #f44336,\n  \"600\":  #e53935,\n  \"700\":  #d32f2f,\n  \"800\":  #c62828,\n  \"900\":  #b71c1c,\n  \"a100\": #ff8a80,\n  \"a200\": #ff5252,\n  \"a400\": #ff1744,\n  \"a700\": #d50000\n);\n\n$clr-red:      map-get($clr-red-list, \"base\");\n\n$clr-red-50:   map-get($clr-red-list, \"50\");\n$clr-red-100:  map-get($clr-red-list, \"100\");\n$clr-red-200:  map-get($clr-red-list, \"200\");\n$clr-red-300:  map-get($clr-red-list, \"300\");\n$clr-red-400:  map-get($clr-red-list, \"400\");\n$clr-red-500:  map-get($clr-red-list, \"500\");\n$clr-red-600:  map-get($clr-red-list, \"600\");\n$clr-red-700:  map-get($clr-red-list, \"700\");\n$clr-red-800:  map-get($clr-red-list, \"800\");\n$clr-red-900:  map-get($clr-red-list, \"900\");\n$clr-red-a100: map-get($clr-red-list, \"a100\");\n$clr-red-a200: map-get($clr-red-list, \"a200\");\n$clr-red-a400: map-get($clr-red-list, \"a400\");\n$clr-red-a700: map-get($clr-red-list, \"a700\");\n\n\n//\n// Pink\n//\n\n$clr-pink-list: (\n  \"base\": #e91e63,\n  \"50\":   #fce4ec,\n  \"100\":  #f8bbd0,\n  \"200\":  #f48fb1,\n  \"300\":  #f06292,\n  \"400\":  #ec407a,\n  \"500\":  #e91e63,\n  \"600\":  #d81b60,\n  \"700\":  #c2185b,\n  \"800\":  #ad1457,\n  \"900\":  #880e4f,\n  \"a100\": #ff80ab,\n  \"a200\": #ff4081,\n  \"a400\": #f50057,\n  \"a700\": #c51162\n);\n\n$clr-pink:      map-get($clr-pink-list, \"base\");\n\n$clr-pink-50:   map-get($clr-pink-list, \"50\");\n$clr-pink-100:  map-get($clr-pink-list, \"100\");\n$clr-pink-200:  map-get($clr-pink-list, \"200\");\n$clr-pink-300:  map-get($clr-pink-list, \"300\");\n$clr-pink-400:  map-get($clr-pink-list, \"400\");\n$clr-pink-500:  map-get($clr-pink-list, \"500\");\n$clr-pink-600:  map-get($clr-pink-list, \"600\");\n$clr-pink-700:  map-get($clr-pink-list, \"700\");\n$clr-pink-800:  map-get($clr-pink-list, \"800\");\n$clr-pink-900:  map-get($clr-pink-list, \"900\");\n$clr-pink-a100: map-get($clr-pink-list, \"a100\");\n$clr-pink-a200: map-get($clr-pink-list, \"a200\");\n$clr-pink-a400: map-get($clr-pink-list, \"a400\");\n$clr-pink-a700: map-get($clr-pink-list, \"a700\");\n\n\n//\n// Purple\n//\n\n$clr-purple-list: (\n  \"base\": #9c27b0,\n  \"50\":   #f3e5f5,\n  \"100\":  #e1bee7,\n  \"200\":  #ce93d8,\n  \"300\":  #ba68c8,\n  \"400\":  #ab47bc,\n  \"500\":  #9c27b0,\n  \"600\":  #8e24aa,\n  \"700\":  #7b1fa2,\n  \"800\":  #6a1b9a,\n  \"900\":  #4a148c,\n  \"a100\": #ea80fc,\n  \"a200\": #e040fb,\n  \"a400\": #d500f9,\n  \"a700\": #aa00ff\n);\n\n$clr-purple:      map-get($clr-purple-list, \"base\");\n\n$clr-purple-50:   map-get($clr-purple-list, \"50\");\n$clr-purple-100:  map-get($clr-purple-list, \"100\");\n$clr-purple-200:  map-get($clr-purple-list, \"200\");\n$clr-purple-300:  map-get($clr-purple-list, \"300\");\n$clr-purple-400:  map-get($clr-purple-list, \"400\");\n$clr-purple-500:  map-get($clr-purple-list, \"500\");\n$clr-purple-600:  map-get($clr-purple-list, \"600\");\n$clr-purple-700:  map-get($clr-purple-list, \"700\");\n$clr-purple-800:  map-get($clr-purple-list, \"800\");\n$clr-purple-900:  map-get($clr-purple-list, \"900\");\n$clr-purple-a100: map-get($clr-purple-list, \"a100\");\n$clr-purple-a200: map-get($clr-purple-list, \"a200\");\n$clr-purple-a400: map-get($clr-purple-list, \"a400\");\n$clr-purple-a700: map-get($clr-purple-list, \"a700\");\n\n\n//\n// Deep purple\n//\n\n$clr-deep-purple-list: (\n  \"base\": #673ab7,\n  \"50\":   #ede7f6,\n  \"100\":  #d1c4e9,\n  \"200\":  #b39ddb,\n  \"300\":  #9575cd,\n  \"400\":  #7e57c2,\n  \"500\":  #673ab7,\n  \"600\":  #5e35b1,\n  \"700\":  #512da8,\n  \"800\":  #4527a0,\n  \"900\":  #311b92,\n  \"a100\": #b388ff,\n  \"a200\": #7c4dff,\n  \"a400\": #651fff,\n  \"a700\": #6200ea\n);\n\n$clr-deep-purple:      map-get($clr-deep-purple-list, \"base\");\n\n$clr-deep-purple-50:   map-get($clr-deep-purple-list, \"50\");\n$clr-deep-purple-100:  map-get($clr-deep-purple-list, \"100\");\n$clr-deep-purple-200:  map-get($clr-deep-purple-list, \"200\");\n$clr-deep-purple-300:  map-get($clr-deep-purple-list, \"300\");\n$clr-deep-purple-400:  map-get($clr-deep-purple-list, \"400\");\n$clr-deep-purple-500:  map-get($clr-deep-purple-list, \"500\");\n$clr-deep-purple-600:  map-get($clr-deep-purple-list, \"600\");\n$clr-deep-purple-700:  map-get($clr-deep-purple-list, \"700\");\n$clr-deep-purple-800:  map-get($clr-deep-purple-list, \"800\");\n$clr-deep-purple-900:  map-get($clr-deep-purple-list, \"900\");\n$clr-deep-purple-a100: map-get($clr-deep-purple-list, \"a100\");\n$clr-deep-purple-a200: map-get($clr-deep-purple-list, \"a200\");\n$clr-deep-purple-a400: map-get($clr-deep-purple-list, \"a400\");\n$clr-deep-purple-a700: map-get($clr-deep-purple-list, \"a700\");\n\n\n//\n// Indigo\n//\n\n$clr-indigo-list: (\n  \"base\": #3f51b5,\n  \"50\":   #e8eaf6,\n  \"100\":  #c5cae9,\n  \"200\":  #9fa8da,\n  \"300\":  #7986cb,\n  \"400\":  #5c6bc0,\n  \"500\":  #3f51b5,\n  \"600\":  #3949ab,\n  \"700\":  #303f9f,\n  \"800\":  #283593,\n  \"900\":  #1a237e,\n  \"a100\": #8c9eff,\n  \"a200\": #536dfe,\n  \"a400\": #3d5afe,\n  \"a700\": #304ffe\n);\n\n$clr-indigo:      map-get($clr-indigo-list, \"base\");\n\n$clr-indigo-50:   map-get($clr-indigo-list, \"50\");\n$clr-indigo-100:  map-get($clr-indigo-list, \"100\");\n$clr-indigo-200:  map-get($clr-indigo-list, \"200\");\n$clr-indigo-300:  map-get($clr-indigo-list, \"300\");\n$clr-indigo-400:  map-get($clr-indigo-list, \"400\");\n$clr-indigo-500:  map-get($clr-indigo-list, \"500\");\n$clr-indigo-600:  map-get($clr-indigo-list, \"600\");\n$clr-indigo-700:  map-get($clr-indigo-list, \"700\");\n$clr-indigo-800:  map-get($clr-indigo-list, \"800\");\n$clr-indigo-900:  map-get($clr-indigo-list, \"900\");\n$clr-indigo-a100: map-get($clr-indigo-list, \"a100\");\n$clr-indigo-a200: map-get($clr-indigo-list, \"a200\");\n$clr-indigo-a400: map-get($clr-indigo-list, \"a400\");\n$clr-indigo-a700: map-get($clr-indigo-list, \"a700\");\n\n\n//\n// Blue\n//\n\n$clr-blue-list: (\n  \"base\": #2196f3,\n  \"50\":   #e3f2fd,\n  \"100\":  #bbdefb,\n  \"200\":  #90caf9,\n  \"300\":  #64b5f6,\n  \"400\":  #42a5f5,\n  \"500\":  #2196f3,\n  \"600\":  #1e88e5,\n  \"700\":  #1976d2,\n  \"800\":  #1565c0,\n  \"900\":  #0d47a1,\n  \"a100\": #82b1ff,\n  \"a200\": #448aff,\n  \"a400\": #2979ff,\n  \"a700\": #2962ff\n);\n\n$clr-blue:      map-get($clr-blue-list, \"base\");\n\n$clr-blue-50:   map-get($clr-blue-list, \"50\");\n$clr-blue-100:  map-get($clr-blue-list, \"100\");\n$clr-blue-200:  map-get($clr-blue-list, \"200\");\n$clr-blue-300:  map-get($clr-blue-list, \"300\");\n$clr-blue-400:  map-get($clr-blue-list, \"400\");\n$clr-blue-500:  map-get($clr-blue-list, \"500\");\n$clr-blue-600:  map-get($clr-blue-list, \"600\");\n$clr-blue-700:  map-get($clr-blue-list, \"700\");\n$clr-blue-800:  map-get($clr-blue-list, \"800\");\n$clr-blue-900:  map-get($clr-blue-list, \"900\");\n$clr-blue-a100: map-get($clr-blue-list, \"a100\");\n$clr-blue-a200: map-get($clr-blue-list, \"a200\");\n$clr-blue-a400: map-get($clr-blue-list, \"a400\");\n$clr-blue-a700: map-get($clr-blue-list, \"a700\");\n\n\n//\n// Light Blue\n//\n\n$clr-light-blue-list: (\n  \"base\": #03a9f4,\n  \"50\":   #e1f5fe,\n  \"100\":  #b3e5fc,\n  \"200\":  #81d4fa,\n  \"300\":  #4fc3f7,\n  \"400\":  #29b6f6,\n  \"500\":  #03a9f4,\n  \"600\":  #039be5,\n  \"700\":  #0288d1,\n  \"800\":  #0277bd,\n  \"900\":  #01579b,\n  \"a100\": #80d8ff,\n  \"a200\": #40c4ff,\n  \"a400\": #00b0ff,\n  \"a700\": #0091ea\n);\n\n$clr-light-blue:      map-get($clr-light-blue-list, \"base\");\n\n$clr-light-blue-50:   map-get($clr-light-blue-list, \"50\");\n$clr-light-blue-100:  map-get($clr-light-blue-list, \"100\");\n$clr-light-blue-200:  map-get($clr-light-blue-list, \"200\");\n$clr-light-blue-300:  map-get($clr-light-blue-list, \"300\");\n$clr-light-blue-400:  map-get($clr-light-blue-list, \"400\");\n$clr-light-blue-500:  map-get($clr-light-blue-list, \"500\");\n$clr-light-blue-600:  map-get($clr-light-blue-list, \"600\");\n$clr-light-blue-700:  map-get($clr-light-blue-list, \"700\");\n$clr-light-blue-800:  map-get($clr-light-blue-list, \"800\");\n$clr-light-blue-900:  map-get($clr-light-blue-list, \"900\");\n$clr-light-blue-a100: map-get($clr-light-blue-list, \"a100\");\n$clr-light-blue-a200: map-get($clr-light-blue-list, \"a200\");\n$clr-light-blue-a400: map-get($clr-light-blue-list, \"a400\");\n$clr-light-blue-a700: map-get($clr-light-blue-list, \"a700\");\n\n\n//\n// Cyan\n//\n\n$clr-cyan-list: (\n  \"base\": #00bcd4,\n  \"50\":   #e0f7fa,\n  \"100\":  #b2ebf2,\n  \"200\":  #80deea,\n  \"300\":  #4dd0e1,\n  \"400\":  #26c6da,\n  \"500\":  #00bcd4,\n  \"600\":  #00acc1,\n  \"700\":  #0097a7,\n  \"800\":  #00838f,\n  \"900\":  #006064,\n  \"a100\": #84ffff,\n  \"a200\": #18ffff,\n  \"a400\": #00e5ff,\n  \"a700\": #00b8d4\n);\n\n$clr-cyan:      map-get($clr-cyan-list, \"base\");\n\n$clr-cyan-50:   map-get($clr-cyan-list, \"50\");\n$clr-cyan-100:  map-get($clr-cyan-list, \"100\");\n$clr-cyan-200:  map-get($clr-cyan-list, \"200\");\n$clr-cyan-300:  map-get($clr-cyan-list, \"300\");\n$clr-cyan-400:  map-get($clr-cyan-list, \"400\");\n$clr-cyan-500:  map-get($clr-cyan-list, \"500\");\n$clr-cyan-600:  map-get($clr-cyan-list, \"600\");\n$clr-cyan-700:  map-get($clr-cyan-list, \"700\");\n$clr-cyan-800:  map-get($clr-cyan-list, \"800\");\n$clr-cyan-900:  map-get($clr-cyan-list, \"900\");\n$clr-cyan-a100: map-get($clr-cyan-list, \"a100\");\n$clr-cyan-a200: map-get($clr-cyan-list, \"a200\");\n$clr-cyan-a400: map-get($clr-cyan-list, \"a400\");\n$clr-cyan-a700: map-get($clr-cyan-list, \"a700\");\n\n\n//\n// Teal\n//\n\n$clr-teal-list: (\n  \"base\": #009688,\n  \"50\":   #e0f2f1,\n  \"100\":  #b2dfdb,\n  \"200\":  #80cbc4,\n  \"300\":  #4db6ac,\n  \"400\":  #26a69a,\n  \"500\":  #009688,\n  \"600\":  #00897b,\n  \"700\":  #00796b,\n  \"800\":  #00695c,\n  \"900\":  #004d40,\n  \"a100\": #a7ffeb,\n  \"a200\": #64ffda,\n  \"a400\": #1de9b6,\n  \"a700\": #00bfa5\n);\n\n$clr-teal:      map-get($clr-teal-list, \"base\");\n\n$clr-teal-50:   map-get($clr-teal-list, \"50\");\n$clr-teal-100:  map-get($clr-teal-list, \"100\");\n$clr-teal-200:  map-get($clr-teal-list, \"200\");\n$clr-teal-300:  map-get($clr-teal-list, \"300\");\n$clr-teal-400:  map-get($clr-teal-list, \"400\");\n$clr-teal-500:  map-get($clr-teal-list, \"500\");\n$clr-teal-600:  map-get($clr-teal-list, \"600\");\n$clr-teal-700:  map-get($clr-teal-list, \"700\");\n$clr-teal-800:  map-get($clr-teal-list, \"800\");\n$clr-teal-900:  map-get($clr-teal-list, \"900\");\n$clr-teal-a100: map-get($clr-teal-list, \"a100\");\n$clr-teal-a200: map-get($clr-teal-list, \"a200\");\n$clr-teal-a400: map-get($clr-teal-list, \"a400\");\n$clr-teal-a700: map-get($clr-teal-list, \"a700\");\n\n\n//\n// Green\n//\n\n$clr-green-list: (\n  \"base\": #4caf50,\n  \"50\":   #e8f5e9,\n  \"100\":  #c8e6c9,\n  \"200\":  #a5d6a7,\n  \"300\":  #81c784,\n  \"400\":  #66bb6a,\n  \"500\":  #4caf50,\n  \"600\":  #43a047,\n  \"700\":  #388e3c,\n  \"800\":  #2e7d32,\n  \"900\":  #1b5e20,\n  \"a100\": #b9f6ca,\n  \"a200\": #69f0ae,\n  \"a400\": #00e676,\n  \"a700\": #00c853\n);\n\n$clr-green:      map-get($clr-green-list, \"base\");\n\n$clr-green-50:   map-get($clr-green-list, \"50\");\n$clr-green-100:  map-get($clr-green-list, \"100\");\n$clr-green-200:  map-get($clr-green-list, \"200\");\n$clr-green-300:  map-get($clr-green-list, \"300\");\n$clr-green-400:  map-get($clr-green-list, \"400\");\n$clr-green-500:  map-get($clr-green-list, \"500\");\n$clr-green-600:  map-get($clr-green-list, \"600\");\n$clr-green-700:  map-get($clr-green-list, \"700\");\n$clr-green-800:  map-get($clr-green-list, \"800\");\n$clr-green-900:  map-get($clr-green-list, \"900\");\n$clr-green-a100: map-get($clr-green-list, \"a100\");\n$clr-green-a200: map-get($clr-green-list, \"a200\");\n$clr-green-a400: map-get($clr-green-list, \"a400\");\n$clr-green-a700: map-get($clr-green-list, \"a700\");\n\n\n//\n// Light green\n//\n\n$clr-light-green-list: (\n  \"base\": #8bc34a,\n  \"50\":   #f1f8e9,\n  \"100\":  #dcedc8,\n  \"200\":  #c5e1a5,\n  \"300\":  #aed581,\n  \"400\":  #9ccc65,\n  \"500\":  #8bc34a,\n  \"600\":  #7cb342,\n  \"700\":  #689f38,\n  \"800\":  #558b2f,\n  \"900\":  #33691e,\n  \"a100\": #ccff90,\n  \"a200\": #b2ff59,\n  \"a400\": #76ff03,\n  \"a700\": #64dd17\n);\n\n$clr-light-green:      map-get($clr-light-green-list, \"base\");\n\n$clr-light-green-50:   map-get($clr-light-green-list, \"50\");\n$clr-light-green-100:  map-get($clr-light-green-list, \"100\");\n$clr-light-green-200:  map-get($clr-light-green-list, \"200\");\n$clr-light-green-300:  map-get($clr-light-green-list, \"300\");\n$clr-light-green-400:  map-get($clr-light-green-list, \"400\");\n$clr-light-green-500:  map-get($clr-light-green-list, \"500\");\n$clr-light-green-600:  map-get($clr-light-green-list, \"600\");\n$clr-light-green-700:  map-get($clr-light-green-list, \"700\");\n$clr-light-green-800:  map-get($clr-light-green-list, \"800\");\n$clr-light-green-900:  map-get($clr-light-green-list, \"900\");\n$clr-light-green-a100: map-get($clr-light-green-list, \"a100\");\n$clr-light-green-a200: map-get($clr-light-green-list, \"a200\");\n$clr-light-green-a400: map-get($clr-light-green-list, \"a400\");\n$clr-light-green-a700: map-get($clr-light-green-list, \"a700\");\n\n\n//\n// Lime\n//\n\n$clr-lime-list: (\n  \"base\": #cddc39,\n  \"50\":   #f9fbe7,\n  \"100\":  #f0f4c3,\n  \"200\":  #e6ee9c,\n  \"300\":  #dce775,\n  \"400\":  #d4e157,\n  \"500\":  #cddc39,\n  \"600\":  #c0ca33,\n  \"700\":  #afb42b,\n  \"800\":  #9e9d24,\n  \"900\":  #827717,\n  \"a100\": #f4ff81,\n  \"a200\": #eeff41,\n  \"a400\": #c6ff00,\n  \"a700\": #aeea00\n);\n\n$clr-lime:      map-get($clr-lime-list, \"base\");\n\n$clr-lime-50:   map-get($clr-lime-list, \"50\");\n$clr-lime-100:  map-get($clr-lime-list, \"100\");\n$clr-lime-200:  map-get($clr-lime-list, \"200\");\n$clr-lime-300:  map-get($clr-lime-list, \"300\");\n$clr-lime-400:  map-get($clr-lime-list, \"400\");\n$clr-lime-500:  map-get($clr-lime-list, \"500\");\n$clr-lime-600:  map-get($clr-lime-list, \"600\");\n$clr-lime-700:  map-get($clr-lime-list, \"700\");\n$clr-lime-800:  map-get($clr-lime-list, \"800\");\n$clr-lime-900:  map-get($clr-lime-list, \"900\");\n$clr-lime-a100: map-get($clr-lime-list, \"a100\");\n$clr-lime-a200: map-get($clr-lime-list, \"a200\");\n$clr-lime-a400: map-get($clr-lime-list, \"a400\");\n$clr-lime-a700: map-get($clr-lime-list, \"a700\");\n\n\n//\n// Yellow\n//\n\n$clr-yellow-list: (\n  \"base\": #ffeb3b,\n  \"50\":   #fffde7,\n  \"100\":  #fff9c4,\n  \"200\":  #fff59d,\n  \"300\":  #fff176,\n  \"400\":  #ffee58,\n  \"500\":  #ffeb3b,\n  \"600\":  #fdd835,\n  \"700\":  #fbc02d,\n  \"800\":  #f9a825,\n  \"900\":  #f57f17,\n  \"a100\": #ffff8d,\n  \"a200\": #ffff00,\n  \"a400\": #ffea00,\n  \"a700\": #ffd600\n);\n\n$clr-yellow:      map-get($clr-yellow-list, \"base\");\n\n$clr-yellow-50:   map-get($clr-yellow-list, \"50\");\n$clr-yellow-100:  map-get($clr-yellow-list, \"100\");\n$clr-yellow-200:  map-get($clr-yellow-list, \"200\");\n$clr-yellow-300:  map-get($clr-yellow-list, \"300\");\n$clr-yellow-400:  map-get($clr-yellow-list, \"400\");\n$clr-yellow-500:  map-get($clr-yellow-list, \"500\");\n$clr-yellow-600:  map-get($clr-yellow-list, \"600\");\n$clr-yellow-700:  map-get($clr-yellow-list, \"700\");\n$clr-yellow-800:  map-get($clr-yellow-list, \"800\");\n$clr-yellow-900:  map-get($clr-yellow-list, \"900\");\n$clr-yellow-a100: map-get($clr-yellow-list, \"a100\");\n$clr-yellow-a200: map-get($clr-yellow-list, \"a200\");\n$clr-yellow-a400: map-get($clr-yellow-list, \"a400\");\n$clr-yellow-a700: map-get($clr-yellow-list, \"a700\");\n\n\n//\n// amber\n//\n\n$clr-amber-list: (\n  \"base\": #ffc107,\n  \"50\":   #fff8e1,\n  \"100\":  #ffecb3,\n  \"200\":  #ffe082,\n  \"300\":  #ffd54f,\n  \"400\":  #ffca28,\n  \"500\":  #ffc107,\n  \"600\":  #ffb300,\n  \"700\":  #ffa000,\n  \"800\":  #ff8f00,\n  \"900\":  #ff6f00,\n  \"a100\": #ffe57f,\n  \"a200\": #ffd740,\n  \"a400\": #ffc400,\n  \"a700\": #ffab00\n);\n\n$clr-amber:      map-get($clr-amber-list, \"base\");\n\n$clr-amber-50:   map-get($clr-amber-list, \"50\");\n$clr-amber-100:  map-get($clr-amber-list, \"100\");\n$clr-amber-200:  map-get($clr-amber-list, \"200\");\n$clr-amber-300:  map-get($clr-amber-list, \"300\");\n$clr-amber-400:  map-get($clr-amber-list, \"400\");\n$clr-amber-500:  map-get($clr-amber-list, \"500\");\n$clr-amber-600:  map-get($clr-amber-list, \"600\");\n$clr-amber-700:  map-get($clr-amber-list, \"700\");\n$clr-amber-800:  map-get($clr-amber-list, \"800\");\n$clr-amber-900:  map-get($clr-amber-list, \"900\");\n$clr-amber-a100: map-get($clr-amber-list, \"a100\");\n$clr-amber-a200: map-get($clr-amber-list, \"a200\");\n$clr-amber-a400: map-get($clr-amber-list, \"a400\");\n$clr-amber-a700: map-get($clr-amber-list, \"a700\");\n\n\n//\n// Orange\n//\n\n$clr-orange-list: (\n  \"base\": #ff9800,\n  \"50\":   #fff3e0,\n  \"100\":  #ffe0b2,\n  \"200\":  #ffcc80,\n  \"300\":  #ffb74d,\n  \"400\":  #ffa726,\n  \"500\":  #ff9800,\n  \"600\":  #fb8c00,\n  \"700\":  #f57c00,\n  \"800\":  #ef6c00,\n  \"900\":  #e65100,\n  \"a100\": #ffd180,\n  \"a200\": #ffab40,\n  \"a400\": #ff9100,\n  \"a700\": #ff6d00\n);\n\n$clr-orange:      map-get($clr-orange-list, \"base\");\n\n$clr-orange-50:   map-get($clr-orange-list, \"50\");\n$clr-orange-100:  map-get($clr-orange-list, \"100\");\n$clr-orange-200:  map-get($clr-orange-list, \"200\");\n$clr-orange-300:  map-get($clr-orange-list, \"300\");\n$clr-orange-400:  map-get($clr-orange-list, \"400\");\n$clr-orange-500:  map-get($clr-orange-list, \"500\");\n$clr-orange-600:  map-get($clr-orange-list, \"600\");\n$clr-orange-700:  map-get($clr-orange-list, \"700\");\n$clr-orange-800:  map-get($clr-orange-list, \"800\");\n$clr-orange-900:  map-get($clr-orange-list, \"900\");\n$clr-orange-a100: map-get($clr-orange-list, \"a100\");\n$clr-orange-a200: map-get($clr-orange-list, \"a200\");\n$clr-orange-a400: map-get($clr-orange-list, \"a400\");\n$clr-orange-a700: map-get($clr-orange-list, \"a700\");\n\n\n//\n// Deep orange\n//\n\n$clr-deep-orange-list: (\n  \"base\": #ff5722,\n  \"50\":   #fbe9e7,\n  \"100\":  #ffccbc,\n  \"200\":  #ffab91,\n  \"300\":  #ff8a65,\n  \"400\":  #ff7043,\n  \"500\":  #ff5722,\n  \"600\":  #f4511e,\n  \"700\":  #e64a19,\n  \"800\":  #d84315,\n  \"900\":  #bf360c,\n  \"a100\": #ff9e80,\n  \"a200\": #ff6e40,\n  \"a400\": #ff3d00,\n  \"a700\": #dd2c00\n);\n\n$clr-deep-orange:      map-get($clr-deep-orange-list, \"base\");\n\n$clr-deep-orange-50:   map-get($clr-deep-orange-list, \"50\");\n$clr-deep-orange-100:  map-get($clr-deep-orange-list, \"100\");\n$clr-deep-orange-200:  map-get($clr-deep-orange-list, \"200\");\n$clr-deep-orange-300:  map-get($clr-deep-orange-list, \"300\");\n$clr-deep-orange-400:  map-get($clr-deep-orange-list, \"400\");\n$clr-deep-orange-500:  map-get($clr-deep-orange-list, \"500\");\n$clr-deep-orange-600:  map-get($clr-deep-orange-list, \"600\");\n$clr-deep-orange-700:  map-get($clr-deep-orange-list, \"700\");\n$clr-deep-orange-800:  map-get($clr-deep-orange-list, \"800\");\n$clr-deep-orange-900:  map-get($clr-deep-orange-list, \"900\");\n$clr-deep-orange-a100: map-get($clr-deep-orange-list, \"a100\");\n$clr-deep-orange-a200: map-get($clr-deep-orange-list, \"a200\");\n$clr-deep-orange-a400: map-get($clr-deep-orange-list, \"a400\");\n$clr-deep-orange-a700: map-get($clr-deep-orange-list, \"a700\");\n\n\n//\n// Brown\n//\n\n$clr-brown-list: (\n  \"base\": #795548,\n  \"50\":   #efebe9,\n  \"100\":  #d7ccc8,\n  \"200\":  #bcaaa4,\n  \"300\":  #a1887f,\n  \"400\":  #8d6e63,\n  \"500\":  #795548,\n  \"600\":  #6d4c41,\n  \"700\":  #5d4037,\n  \"800\":  #4e342e,\n  \"900\":  #3e2723,\n);\n\n$clr-brown:     map-get($clr-brown-list, \"base\");\n\n$clr-brown-50:  map-get($clr-brown-list, \"50\");\n$clr-brown-100: map-get($clr-brown-list, \"100\");\n$clr-brown-200: map-get($clr-brown-list, \"200\");\n$clr-brown-300: map-get($clr-brown-list, \"300\");\n$clr-brown-400: map-get($clr-brown-list, \"400\");\n$clr-brown-500: map-get($clr-brown-list, \"500\");\n$clr-brown-600: map-get($clr-brown-list, \"600\");\n$clr-brown-700: map-get($clr-brown-list, \"700\");\n$clr-brown-800: map-get($clr-brown-list, \"800\");\n$clr-brown-900: map-get($clr-brown-list, \"900\");\n\n\n//\n// Grey\n//\n\n$clr-grey-list: (\n  \"base\": #9e9e9e,\n  \"50\":   #fafafa,\n  \"100\":  #f5f5f5,\n  \"200\":  #eeeeee,\n  \"300\":  #e0e0e0,\n  \"400\":  #bdbdbd,\n  \"500\":  #9e9e9e,\n  \"600\":  #757575,\n  \"700\":  #616161,\n  \"800\":  #424242,\n  \"900\":  #212121,\n);\n\n$clr-grey:     map-get($clr-grey-list, \"base\");\n\n$clr-grey-50:  map-get($clr-grey-list, \"50\");\n$clr-grey-100: map-get($clr-grey-list, \"100\");\n$clr-grey-200: map-get($clr-grey-list, \"200\");\n$clr-grey-300: map-get($clr-grey-list, \"300\");\n$clr-grey-400: map-get($clr-grey-list, \"400\");\n$clr-grey-500: map-get($clr-grey-list, \"500\");\n$clr-grey-600: map-get($clr-grey-list, \"600\");\n$clr-grey-700: map-get($clr-grey-list, \"700\");\n$clr-grey-800: map-get($clr-grey-list, \"800\");\n$clr-grey-900: map-get($clr-grey-list, \"900\");\n\n\n//\n// Blue grey\n//\n\n$clr-blue-grey-list: (\n  \"base\": #607d8b,\n  \"50\":   #eceff1,\n  \"100\":  #cfd8dc,\n  \"200\":  #b0bec5,\n  \"300\":  #90a4ae,\n  \"400\":  #78909c,\n  \"500\":  #607d8b,\n  \"600\":  #546e7a,\n  \"700\":  #455a64,\n  \"800\":  #37474f,\n  \"900\":  #263238,\n);\n\n$clr-blue-grey:     map-get($clr-blue-grey-list, \"base\");\n\n$clr-blue-grey-50:  map-get($clr-blue-grey-list, \"50\");\n$clr-blue-grey-100: map-get($clr-blue-grey-list, \"100\");\n$clr-blue-grey-200: map-get($clr-blue-grey-list, \"200\");\n$clr-blue-grey-300: map-get($clr-blue-grey-list, \"300\");\n$clr-blue-grey-400: map-get($clr-blue-grey-list, \"400\");\n$clr-blue-grey-500: map-get($clr-blue-grey-list, \"500\");\n$clr-blue-grey-600: map-get($clr-blue-grey-list, \"600\");\n$clr-blue-grey-700: map-get($clr-blue-grey-list, \"700\");\n$clr-blue-grey-800: map-get($clr-blue-grey-list, \"800\");\n$clr-blue-grey-900: map-get($clr-blue-grey-list, \"900\");\n\n\n//\n// Black\n//\n\n$clr-black-list: (\n  \"base\": #000\n);\n\n$clr-black: map-get($clr-black-list, \"base\");\n\n\n//\n// White\n//\n\n$clr-white-list: (\n  \"base\": #fff\n);\n\n$clr-white: map-get($clr-white-list, \"base\");\n\n\n//\n// List for all Colors for looping\n//\n\n$clr-list-all: (\n  \"red\":         $clr-red-list,\n  \"pink\":        $clr-pink-list,\n  \"purple\":      $clr-purple-list,\n  \"deep-purple\": $clr-deep-purple-list,\n  \"indigo\":      $clr-indigo-list,\n  \"blue\":        $clr-blue-list,\n  \"light-blue\":  $clr-light-blue-list,\n  \"cyan\":        $clr-cyan-list,\n  \"teal\":        $clr-teal-list,\n  \"green\":       $clr-green-list,\n  \"light-green\": $clr-light-green-list,\n  \"lime\":        $clr-lime-list,\n  \"yellow\":      $clr-yellow-list,\n  \"amber\":       $clr-amber-list,\n  \"orange\":      $clr-orange-list,\n  \"deep-orange\": $clr-deep-orange-list,\n  \"brown\":       $clr-brown-list,\n  \"grey\":        $clr-grey-list,\n  \"blue-grey\":   $clr-blue-grey-list,\n  \"black\":       $clr-black-list,\n  \"white\":       $clr-white-list\n);\n\n\n//\n// Typography\n//\n\n$clr-ui-display-4: $clr-grey-600;\n$clr-ui-display-3: $clr-grey-600;\n$clr-ui-display-2: $clr-grey-600;\n$clr-ui-display-1: $clr-grey-600;\n$clr-ui-headline:  $clr-grey-900;\n$clr-ui-title:     $clr-grey-900;\n$clr-ui-subhead-1: $clr-grey-900;\n$clr-ui-body-2:    $clr-grey-900;\n$clr-ui-body-1:    $clr-grey-900;\n$clr-ui-caption:   $clr-grey-600;\n$clr-ui-menu:      $clr-grey-900;\n$clr-ui-button:    $clr-grey-900;\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Navigation variables\n:root {\n  --md-nav-icon--prev: svg-load(\"material/arrow-left.svg\");\n  --md-nav-icon--next: svg-load(\"material/chevron-right.svg\");\n  --md-toc-icon: svg-load(\"material/table-of-contents.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Navigation\n.md-nav {\n  font-size: px2rem(14px);\n  line-height: 1.3;\n\n  // Navigation title\n  &__title {\n    // sphinx-immaterial: display object description icon as a\n    // separate column, don't allow title text to wrap underneath it.\n    display: flex;\n    // sphinx-immaterial: ensure icons are centered vertically with text.\n    align-items: center;\n    padding: 0 px2rem(12px);\n    overflow: hidden;\n    font-weight: 700;\n    color: var(--md-default-fg-color--light);\n    text-overflow: ellipsis;\n\n    // Navigaton button\n    .md-nav__button {\n      display: none;\n\n      // Stretch images based on height, as it's the smaller dimension\n      img {\n        width: auto;\n        height: 100%;\n      }\n\n      // Button with logo, pointing to `config.site_url`\n      &.md-logo {\n\n        // Image or icon\n        :is(img, svg) {\n          display: block;\n          width: auto;\n          max-width: 100%;\n          height: px2rem(48px);\n          object-fit: contain;\n          fill: currentcolor;\n        }\n      }\n    }\n  }\n\n  // Navigation list\n  &__list {\n    padding: 0;\n    margin: 0;\n    list-style: none;\n  }\n\n  // Navigation link\n  &__link {\n    display: flex;\n    gap: px2rem(8px);\n    align-items: flex-start;\n    // sphinx-immaterial: use variable for margin-height\n    $margin-height: 0.625em;\n\n    margin-top: #{$margin-height};\n    scroll-snap-align: start;\n    transition: color 125ms;\n\n    // sphinx-immaterial: used for toc.sticky feature to create a gap between\n    // sticky header and scrolling content\n    &.md-nav__sticky {\n      box-shadow:\n        0 -#{$margin-height} var(--md-default-bg-color),\n        0 #{$margin-height} var(--md-default-bg-color);\n    }\n\n    // Navigation link that was passed\n    &--passed {\n      color: var(--md-default-fg-color--light);\n    }\n\n    // Active link\n    .md-nav__item &--active {\n\n      // Also enable color transitions on inline code blocks\n      &,\n      code {\n        color: var(--md-typeset-a-color);\n      }\n    }\n\n    // sphinx-immaterial: show nav links corresponding to current viewport\n    &--in-viewport {\n      position: relative;\n\n      &::before {\n        position: absolute;\n        top: 0;\n        right: calc(100% + px2rem(6px));\n        bottom: 0;\n        width: px2rem(1px);\n        height: 100%;\n        content: \"\";\n        background-color: var(--md-primary-fg-color);\n      }\n    }\n\n    // Navigation link title\n    .md-ellipsis {\n      // Hack: Safari exhibits a bug where the text will sometimes disappear\n      // and the element will become unclickable. Setting `position: relative`\n      // seems to fix the issue - see https://bit.ly/3HljM1T\n      position: relative;\n    }\n\n    // Always align navigation icons to the end\n    .md-icon:last-child {\n      margin-inline-start: auto;\n    }\n\n    // Navigation link icon\n    svg {\n      // Hack: Safari has another bug where SVGs disappear on hover - same fix\n      // as above via `position: relative` - see https://t.ly/5fSj1\n      position: relative;\n      flex-shrink: 0;\n      height: 1.3em;\n      fill: currentcolor;\n    }\n\n    // Navigation link on focus/hover\n    &:is([href], [for]):is(:focus, :hover) {\n      color: var(--md-accent-fg-color);\n      cursor: pointer;\n    }\n\n    // Show outline for keyboard devices\n    &.focus-visible {\n      outline-color: var(--md-accent-fg-color);\n      outline-offset: px2rem(4px);\n    }\n\n    // Navigation link for table of contents\n    .md-nav--primary &[for=\"__toc\"] {\n      display: none;\n\n      // Table of contents icon\n      .md-icon::after {\n        display: block;\n        width: 100%;\n        height: 100%;\n        background-color: currentcolor;\n        mask-image: var(--md-toc-icon);\n      }\n\n      // Hide table of contents\n      ~ .md-nav {\n        display: none;\n      }\n    }\n  }\n\n  // Navigation container (for section index pages)\n  &__container > .md-nav__link {\n    margin-top: 0;\n\n    // Stretch first child\n    &:first-child {\n      flex-grow: 1;\n      // Hack: if a very long word is used, it can push the arrow out of sight.\n      // Setting this property contains the text - see https://t.ly/E02vp\n      min-width: 0;\n    }\n  }\n\n  // sphinx-immaterial: used when `toc.sticky` is enabled\n  &__sticky {\n    position: sticky;\n    top: var(--md-nav__header-height, 0);\n    z-index: var(--md-nav__sticky-zindex);\n    background-color: var(--md-default-bg-color);\n  }\n\n  .md-ellipsis {\n    // sphinx-immaterial: don't inherit display:flex (flex-grow still applies)\n    display: block;\n    // sphinx-immaterial: Ensures the md-nav__icon element that comes after is\n    // right-aligned.\n    flex-grow: 1;\n    // sphinx-immaterial: allow wrapping of nav item titles\n    white-space: normal;\n  }\n\n  // Navigation icon\n  &__icon {\n    flex-shrink: 0;\n  }\n\n  // Repository information container\n  &__source {\n    display: none;\n  }\n\n  // [tablet -]: Layered navigation\n  @include break-to-device(tablet) {\n\n    // Primary and nested navigation\n    &--primary,\n    &--primary & {\n      position: absolute;\n      inset-inline: 0;\n      top: 0;\n      z-index: 1;\n      display: flex;\n      flex-direction: column;\n      height: 100%;\n      background-color: var(--md-default-bg-color);\n    }\n\n    // Primary navigation\n    &--primary {\n\n      // sphinx-immaterial: disable `toc.sticky` behavior for layered navigation\n      .md-nav__sticky {\n        position: static;\n        z-index: auto;\n        background-color: transparent;\n        box-shadow: none;\n      }\n\n      // Navigation title and item\n      :is(.md-nav__title, .md-nav__item) {\n        font-size: px2rem(16px);\n        line-height: 1.5;\n      }\n\n      // Navigation title\n      .md-nav__title {\n        position: relative;\n        // sphinx-immaterial: word wrapping can require larger height\n        min-height: px2rem(112px);\n        padding: px2rem(60px) px2rem(16px) px2rem(4px);\n        line-height: px2rem(48px);\n        color: var(--md-default-fg-color--light);\n        white-space: nowrap;\n        cursor: pointer;\n        background-color: var(--md-default-fg-color--lightest);\n\n        // Navigation icon\n        .md-nav__icon {\n          position: absolute;\n          inset-inline-start: px2rem(8px);\n          top: px2rem(8px);\n          display: block;\n          width: px2rem(24px);\n          height: px2rem(24px);\n          margin: px2rem(4px);\n\n          // Navigation icon in link to previous level\n          &::after {\n            display: block;\n            width: 100%;\n            height: 100%;\n            content: \"\";\n            background-color: currentcolor;\n            mask-image: var(--md-nav-icon--prev);\n            mask-repeat: no-repeat;\n            mask-position: center;\n            mask-size: contain;\n          }\n        }\n\n        // Navigation list\n        ~ .md-nav__list {\n          overflow-y: auto;\n          touch-action: pan-y;\n          scroll-snap-type: y mandatory;\n          background-color: var(--md-default-bg-color);\n          box-shadow:\n            0 px2rem(1px) 0 var(--md-default-fg-color--lightest) inset;\n\n          // Omit border on first child\n          > :first-child {\n            border-top: 0;\n          }\n        }\n\n        // Top-level navigation title\n        &[for=\"__drawer\"] {\n          font-weight: 700;\n          color: var(--md-primary-bg-color);\n          background-color: var(--md-primary-fg-color);\n        }\n\n        // Button with logo, pointing to `config.site_url`\n        .md-logo {\n          position: absolute;\n          inset-inline: px2rem(4px);\n          top: px2rem(4px);\n          display: block;\n          padding: px2rem(8px);\n          margin: px2rem(4px);\n        }\n      }\n\n      // Navigation list\n      .md-nav__list {\n        flex: 1;\n      }\n\n      // Navigation item\n      .md-nav__item {\n        border-top: px2rem(1px) solid var(--md-default-fg-color--lightest);\n\n        // Navigation link in active navigation\n        &--active > .md-nav__link {\n          color: var(--md-typeset-a-color);\n\n          // Navigation link on focus/hover\n          &:is(:focus, :hover) {\n            color: var(--md-accent-fg-color);\n          }\n        }\n      }\n\n      // Navigation link\n      .md-nav__link {\n        padding: px2rem(12px) px2rem(16px);\n        margin-top: 0;\n\n        // Navigation link icon\n        svg {\n          margin-top: 0.1em;\n        }\n\n        // Adjust spacing on nested link\n        > .md-nav__link {\n          padding: 0;\n        }\n\n        // Navigation icon\n        .md-nav__icon {\n          width: px2rem(24px);\n          height: px2rem(24px);\n          margin-inline-end: px2rem(-4px);\n          font-size: px2rem(24px);\n\n          // Navigation icon in link to next level\n          &::after {\n            display: block;\n            width: 100%;\n            height: 100%;\n            content: \"\";\n            background-color: currentcolor;\n            mask-image: var(--md-nav-icon--next);\n            mask-repeat: no-repeat;\n            mask-position: center;\n            mask-size: contain;\n          }\n        }\n      }\n\n      // Flip icon vertically\n      .md-nav__icon {\n\n        // Adjust for right-to-left languages\n        [dir=\"rtl\"] &::after {\n          transform: scale(-1);\n        }\n      }\n\n      // Table of contents contained in primary navigation\n      .md-nav--secondary {\n\n        // Navigation on level 2-6\n        .md-nav {\n          position: static;\n          background-color: transparent;\n\n          // Navigation link on level 3\n          .md-nav__link {\n            padding-inline-start: px2rem(28px);\n          }\n\n          // Navigation link on level 4\n          .md-nav .md-nav__link {\n            padding-inline-start: px2rem(40px);\n          }\n\n          // Navigation link on level 5\n          .md-nav .md-nav .md-nav__link {\n            padding-inline-start: px2rem(52px);\n          }\n\n          // Navigation link on level 6\n          .md-nav .md-nav .md-nav .md-nav__link {\n            padding-inline-start: px2rem(64px);\n          }\n        }\n      }\n    }\n\n    // Table of contents\n    &--secondary {\n      background-color: transparent;\n    }\n\n    // Hide nested navigation\n    &__toggle ~ & {\n      display: flex;\n      opacity: 0;\n      transition:\n        transform 250ms cubic-bezier(0.8, 0, 0.6, 1),\n        opacity   125ms 50ms;\n      transform: translateX(100%);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        transform: translateX(-100%);\n      }\n    }\n\n    // Show nested navigation when toggle is active\n    &__toggle:checked ~ & {\n      opacity: 1;\n      transition:\n        transform 250ms cubic-bezier(0.4, 0, 0.2, 1),\n        opacity   125ms 125ms;\n      transform: translateX(0);\n\n      // Navigation list\n      > .md-nav__list {\n        // Hack: promote to own layer to reduce jitter\n        backface-visibility: hidden;\n      }\n    }\n\n    // sphinx-immaterial: ensure long titles in mobile/tablet layout\n    // are truncated with an ellipsis rather than wrapping.\n    .md-nav__title .md-ellipsis {\n      white-space: nowrap;\n\n      // In Chrome, <wbr> elements override `white-space: nowrap`, but\n      // setting them to `display: none` prevents that.\n      wbr {\n        display: none;\n      }\n    }\n  }\n\n  // [tablet portrait -]: Layered navigation with table of contents\n  @include break-to-device(tablet portrait) {\n\n    // sphinx-immaterial: hide nested nav items of current page, since\n    // they are redundant with integrated toc.\n    &__current-nested {\n      display: none !important;\n    }\n\n    // Show link to table of contents\n    &--primary &__link[for=\"__toc\"] {\n      display: flex;\n\n      // Show table of contents icon\n      .md-icon::after {\n        content: \"\";\n      }\n\n      // Hide navigation link to current page\n      + .md-nav__link {\n        display: none;\n      }\n\n      // Show table of contents\n      ~ .md-nav {\n        display: flex;\n      }\n    }\n\n    // Repository information container\n    &__source {\n      display: block;\n      padding: 0 px2rem(4px);\n      color: var(--md-primary-bg-color);\n      background-color: var(--md-primary-fg-color--dark);\n    }\n  }\n\n  // [tablet landscape]: Layered navigation with table of contents\n  @include break-at-device(tablet landscape) {\n\n    // Show link to integrated table of contents\n    &--integrated &__link[for=\"__toc\"] {\n      display: flex;\n\n      // Show table of contents icon\n      .md-icon::after {\n        content: \"\";\n      }\n\n      // Hide navigation link to current page\n      + .md-nav__link {\n        display: none;\n      }\n\n      // Show table of contents\n      ~ .md-nav {\n        display: flex;\n      }\n    }\n  }\n\n  // [tablet landscape +]: Tree-like table of contents\n  @include break-from-device(tablet landscape) {\n    margin-bottom: px2rem(-8px);\n\n    // sphinx-immaterial: hide integreated toc, since it is redundant with any nested items.\n    &__current-toc {\n      display: none !important;\n    }\n\n    // Table of contents\n    &--secondary {\n\n      // Navigation title\n      .md-nav__title {\n        position: sticky;\n        top: 0;\n        // Hack: because of the hack that we need to make .md-ellipsis work in\n        // Safari, we need to set `z-index` here as - see https://bit.ly/3s5M2jm\n        z-index: 1;\n        background: var(--md-default-bg-color);\n        box-shadow: 0 0 px2rem(8px) px2rem(8px) var(--md-default-bg-color);\n\n        // Adjust snapping behavior\n        &[for=\"__toc\"] {\n          scroll-snap-align: start;\n        }\n\n        // Hide navigation icon\n        .md-nav__icon {\n          display: none;\n        }\n      }\n\n      // Adjust spacing for navigation list - same reason as below\n      .md-nav__list {\n        padding-inline-start: px2rem(12px);\n        padding-bottom: px2rem(8px);\n      }\n\n      // Adjust spacing for navigation link - before this change, we set spacing\n      // on the left and right of a navigation item, but this led to the problem\n      // of cropped focus outlines, because we must set `overflow: hidden` on\n      // the navigation list for smooth expand and collapse transitions.\n      .md-nav__item > .md-nav__link {\n        margin-inline-end: px2rem(8px);\n      }\n    }\n  }\n\n  // [screen +]: Tree-like navigation\n  @include break-from-device(screen) {\n    margin-bottom: px2rem(-8px);\n    transition: max-height 250ms cubic-bezier(0.86, 0, 0.07, 1);\n\n    // Primary navigation\n    &--primary {\n\n      // Navigation title\n      .md-nav__title {\n        position: sticky;\n        top: 0;\n        // Hack: because of the hack that we need to make .md-ellipsis work in\n        // Safari, we need to set `z-index` here as - see https://bit.ly/3s5M2jm\n        z-index: 1;\n        background: var(--md-default-bg-color);\n        box-shadow: 0 0 px2rem(8px) px2rem(8px) var(--md-default-bg-color);\n\n        // Adjust snapping behavior\n        &[for=\"__drawer\"] {\n          scroll-snap-align: start;\n        }\n\n        // Hide navigation icon\n        .md-nav__icon {\n          display: none;\n        }\n      }\n\n      // Adjust spacing for navigation list - same reason as below\n      .md-nav__list {\n        padding-inline-start: px2rem(12px);\n        padding-bottom: px2rem(8px);\n      }\n\n      // Adjust spacing for navigation link - before this change, we set spacing\n      // on the left and right of a navigation item, but this led to the problem\n      // of cropped focus outlines, because we must set `overflow: hidden` on\n      // the navigation list for smooth expand and collapse transitions.\n      .md-nav__item > .md-nav__link {\n        margin-inline-end: px2rem(8px);\n      }\n    }\n\n    // Hide nested navigation\n    &__toggle ~ & {\n      display: grid;\n      // Hack: we must set a minimum of 8px to work around a bug introduced in\n      // Safari 18.3, collapsing to a height of 0 – see https://t.ly/ViA3N\n      grid-template-rows: minmax(#{px2rem(8px)}, 0fr);\n      visibility: collapse;\n      opacity: 0;\n      transition:\n        grid-template-rows 250ms cubic-bezier(0.86, 0, 0.07, 1),\n        opacity            250ms,\n        visibility           0ms 250ms;\n\n      // Navigation list\n      > .md-nav__list {\n        overflow: hidden;\n      }\n    }\n\n    // Show nested navigation when toggle is active or indeterminate\n    &__toggle:is(:checked, .md-toggle--indeterminate) ~ & {\n      grid-template-rows: minmax(#{px2rem(8px)}, 1fr);\n      visibility: visible;\n      opacity: 1;\n      transition:\n        grid-template-rows 250ms cubic-bezier(0.86, 0, 0.07, 1),\n        opacity            150ms 100ms,\n        visibility           0ms;\n\n      // sphinx-immaterial: disable `overflow: hidden` when displaying nested\n      // navigation to avoid breaking toc.sticky feature. `overflow: hidden` is\n      // needed for the expand/collapse transition. There is probably room for\n      // improvement in how the transition is handled for this style, but it is\n      // barely noticable anyway.\n      > .md-nav__list {\n        overflow: visible;\n      }\n    }\n\n    // Disable transition for expanded navigation\n    &__toggle.md-toggle--indeterminate ~ & {\n      transition: none;\n    }\n\n    // Hide navigation title in nested navigation\n    &__item--nested > & > &__title {\n      display: none;\n    }\n\n    // Navigation section\n    &__item--section {\n      display: block;\n      margin: 1.25em 0;\n\n      // Adjust spacing on last child\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      // Show navigation link as title\n      > .md-nav__link {\n        font-weight: 700;\n\n        // Make labels discernable from links\n        &[for] {\n          color: var(--md-default-fg-color--light);\n        }\n\n        // Omit clicks if not a section index page\n        &:not(.md-nav__container) {\n          pointer-events: none;\n        }\n\n        // Hide navigation icon\n        > [for],\n        .md-icon {\n          display: none;\n        }\n      }\n\n      // Navigation\n      > .md-nav {\n        display: block;\n        margin-inline-start: px2rem(-12px);\n        visibility: visible;\n        opacity: 1;\n\n        // Adjust spacing on next level item\n        > .md-nav__list > .md-nav__item {\n          padding: 0;\n        }\n      }\n    }\n\n    // Navigation icon\n    &__icon {\n      width: px2rem(18px);\n      height: px2rem(18px);\n      border-radius: 100%;\n      transition: background-color 250ms;\n\n      // Navigation icon on hover\n      &:hover {\n        background-color: var(--md-accent-fg-color--transparent);\n      }\n\n      // Navigation icon content\n      &::after {\n        display: inline-block;\n        width: 100%;\n        height: 100%;\n        vertical-align: px2rem(-2px);\n        content: \"\";\n        background-color: currentcolor;\n        border-radius: 100%;\n        mask-image: var(--md-nav-icon--next);\n        mask-repeat: no-repeat;\n        mask-position: center;\n        mask-size: contain;\n        transition: transform 250ms;\n\n        // Adjust for right-to-left languages\n        [dir=\"rtl\"] & {\n          transform: rotate(180deg);\n        }\n\n        // Navigation icon - rotate icon when toggle is active or indeterminate\n        .md-nav__item--nested .md-nav__toggle:checked ~ .md-nav__link &,\n        .md-nav__item--nested .md-toggle--indeterminate ~ .md-nav__link & {\n          transform: rotate(90deg);\n        }\n      }\n    }\n\n    // Modifier for when navigation tabs are rendered\n    &--lifted {\n\n      // Hide site title\n      > .md-nav__title {\n        display: none;\n      }\n\n      // Hide level 0 navigation items\n      > .md-nav__list > .md-nav__item {\n        display: none;\n\n        // Active parent navigation item\n        &--active {\n          display: block;\n\n          // Show navigation link as title\n          > .md-nav__link {\n            position: sticky;\n            top: 0;\n            z-index: var(--md-nav__sticky-zindex, 1);\n            margin-top: 0;\n            background: var(--md-default-bg-color);\n            box-shadow: 0 0 px2rem(8px) px2rem(8px) var(--md-default-bg-color);\n\n            // Omit clicks if not a section index page\n            &:not(.md-nav__container) {\n              pointer-events: none;\n            }\n          }\n\n          // Adjust spacing for navigation section\n          &.md-nav__item--section {\n            margin: 0;\n          }\n        }\n\n        // Adjust spacing for nested navigation\n        > .md-nav:not(.md-nav--secondary) {\n          margin-inline-start: px2rem(-12px);\n        }\n\n        // Make labels discernable from links\n        > [for] {\n          color: var(--md-default-fg-color--light);\n        }\n      }\n\n      // Hack: Always show active navigation tab on breakpoint screen, despite\n      // of checkbox being checked or not - see https://t.ly/Qc311\n      .md-nav[data-md-level=\"1\"] {\n        grid-template-rows: minmax(#{px2rem(8px)}, 1fr);\n        visibility: visible;\n        opacity: 1;\n      }\n    }\n\n    // Modifier for when table of contents is rendered in primary navigation\n    &--integrated > .md-nav__list > .md-nav__item--active {\n\n      // Show integrated table of contents\n      .md-nav--secondary {\n        display: block;\n        margin-bottom: 1.25em;\n        visibility: visible;\n        border-inline-start: px2rem(1px) solid var(--md-primary-fg-color);\n        opacity: 1;\n\n        // Navigation list\n        > .md-nav__list {\n          padding-bottom: 0;\n          overflow: visible;\n        }\n\n        // Hide table of contents title\n        > .md-nav__title {\n          display: none;\n        }\n      }\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Pagination\n.md-pagination {\n  display: flex;\n  gap: px2rem(8px);\n  align-items: center;\n  justify-content: center;\n  font-size: px2rem(16px);\n  font-weight: 700;\n\n  // Pagination item\n  > * {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    min-width: px2rem(36px);\n    height: px2rem(36px);\n    text-align: center;\n    border-radius: px2rem(4px);\n  }\n\n  // Active pagination item\n  &__current {\n    color: var(--md-default-fg-color--light);\n    background-color: var(--md-default-fg-color--lightest);\n  }\n\n  // Pagination link\n  &__link {\n    transition:\n      color            125ms,\n      background-color 125ms;\n\n    // Pagination link on focus/hover\n    &:is(:focus, :hover) {\n      color: var(--md-accent-fg-color);\n      background-color: var(--md-accent-fg-color--transparent);\n\n      // Pagination icon\n      svg {\n        color: var(--md-accent-fg-color);\n      }\n    }\n\n    // Show outline for keyboard devices\n    &.focus-visible {\n      outline-color: var(--md-accent-fg-color);\n      outline-offset: px2rem(4px);\n    }\n\n    // Pagination icon\n    svg {\n      display: block;\n      width: px2rem(24px);\n      max-height: 100%;\n      color: var(--md-default-fg-color--lighter);\n      fill: currentcolor;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Post\n.md-post {\n\n  // Post backlink\n  &__back {\n    padding-bottom: px2rem(24px);\n    margin-bottom: px2rem(24px);\n    border-bottom: px2rem(1px) solid var(--md-default-fg-color--lightest);\n\n    // [tablet -]: Hide post backlink\n    @include break-to-device(tablet) {\n      display: none;\n    }\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n\n      // Flip icon vertically\n      svg {\n        transform: scaleX(-1);\n      }\n    }\n  }\n\n  // Post authors\n  &__authors {\n    display: flex;\n    flex-direction: column;\n    gap: px2rem(12px);\n    margin: 0 px2rem(12px) px2rem(24px);\n  }\n\n  // Post metadata\n  .md-post__meta {\n\n    // Navigation link\n    a {\n      transition: color 125ms;\n\n      // Navigation link on focus/hover\n      &:is(:focus, :hover) {\n        color: var(--md-accent-fg-color);\n      }\n    }\n  }\n\n  // Post navigation title @todo - generalize\n  &__title {\n    font-weight: 700;\n    color: var(--md-default-fg-color--light);\n  }\n\n  // Post excerpt\n  &--excerpt {\n    margin-bottom: px2rem(64px);\n\n    // Post excerpt header\n    .md-post__header {\n      display: flex;\n      gap: px2rem(12px);\n      align-items: center;\n      min-height: px2rem(32px);\n    }\n\n    // Post excerpt authors\n    .md-post__authors {\n      display: inline-flex;\n      flex-direction: row;\n      gap: px2rem(4px);\n      align-items: center;\n      min-height: px2rem(48px);\n      margin: 0;\n    }\n\n    // Post excerpt metadata\n    .md-post__meta .md-meta__list {\n      margin-inline-end: px2rem(8px);\n    }\n\n    // Post excerpt content\n    .md-post__content > :first-child {\n      --md-scroll-margin: #{px2rem(120px)};\n\n      margin-top: 0;\n    }\n  }\n\n  // Add margin to table of contents\n  > .md-nav--secondary {\n    margin: 1em 0;\n  }\n}\n\n// ----------------------------------------------------------------------------\n\n// Post author profile\n.md-profile {\n  display: flex;\n  gap: px2rem(12px);\n  align-items: center;\n  width: 100%;\n  font-size: px2rem(14px);\n  line-height: 1.4;\n\n  // Post author description\n  &__description {\n    flex-grow: 1;\n  }\n}\n\n// ----------------------------------------------------------------------------\n\n// Content area for post\n.md-content--post {\n  display: flex;\n\n  // [tablet -]: Switch to inverted column layout\n  @include break-to-device(tablet) {\n    flex-flow: column-reverse;\n  }\n\n  // Content wrapper\n  > .md-content__inner {\n    flex-grow: 1;\n    min-width: 0;\n\n    // [screen +]: Adjust spacing between content area and sidebars\n    @include break-from-device(screen) {\n      margin-inline-start: px2rem(24px);\n    }\n  }\n}\n\n// Sidebar for post\n.md-sidebar.md-sidebar--post {\n\n  // [tablet -]: Adjust spacing\n  @include break-to-device(tablet) {\n    position: initial;\n    width: 100%;\n    padding: 0;\n\n    .md-sidebar__scrollwrap {\n      overflow: visible;\n    }\n\n    .md-sidebar__inner {\n      padding: 0;\n    }\n\n    .md-post__meta {\n      margin-inline: px2rem(12px);\n    }\n\n    .md-nav__item {\n      display: inline;\n      border: none;\n    }\n\n    .md-nav__list {\n      display: inline-flex;\n      flex-wrap: wrap;\n      gap: px2rem(12px);\n      padding-block: px2rem(12px);\n    }\n\n    .md-nav__link {\n      padding: 0;\n    }\n\n    .md-nav {\n      position: initial;\n      height: auto;\n      margin-bottom: 0;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Progress variables\n:root {\n  --md-progress-value: 0;\n  --md-progress-delay: 400ms;\n}\n\n// ----------------------------------------------------------------------------\n\n// Progress indicator\n.md-progress {\n  position: fixed;\n  top: 0;\n  z-index: 4;\n  width: 100%;\n  height: px2rem(1.5px);\n  background: var(--md-primary-bg-color);\n  opacity:\n    min(\n      clamp(0, var(--md-progress-value), 1),\n      clamp(0, 100 - var(--md-progress-value), 1)\n    );\n  transition:\n    transform 500ms cubic-bezier(0.19, 1, 0.22, 1),\n    opacity   250ms var(--md-progress-delay);\n  transform: scaleX(calc(var(--md-progress-value) * 1%));\n  transform-origin: left;\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Search variables\n:root {\n  --md-search-result-icon: svg-load(\"material/file-search-outline.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Search\n.md-search {\n  position: relative;\n\n  // [tablet landscape +]: Header-embedded search\n  @include break-from-device(tablet landscape) {\n    padding: px2rem(4px) 0;\n  }\n\n  // [no-js]: Hide search\n  .no-js & {\n    display: none;\n  }\n\n  // Search overlay\n  &__overlay {\n    z-index: 1;\n    opacity: 0;\n\n    // [tablet portrait -]: Search modal\n    @include break-to-device(tablet portrait) {\n      position: absolute;\n      inset-inline-start: px2rem(-44px);\n      top: px2rem(-20px);\n      width: px2rem(40px);\n      height: px2rem(40px);\n      overflow: hidden;\n      pointer-events: none;\n      background-color: var(--md-default-bg-color);\n      border-radius: px2rem(20px);\n      transition:\n        transform 300ms 100ms,\n        opacity   200ms 200ms;\n      transform-origin: center;\n\n      // Show overlay when search is active\n      [data-md-toggle=\"search\"]:checked ~ .md-header & {\n        opacity: 1;\n        transition:\n          transform 400ms,\n          opacity   100ms;\n      }\n    }\n\n    // [tablet landscape +]: Header-embedded search\n    @include break-from-device(tablet landscape) {\n      position: fixed;\n      inset-inline-start: 0;\n      top: 0;\n      width: 0;\n      height: 0;\n      cursor: pointer;\n      background-color: hsla(0, 0%, 0%, 0.54);\n      transition:\n        width     0ms 250ms,\n        height    0ms 250ms,\n        opacity 250ms;\n\n      // Show overlay when search is active\n      [data-md-toggle=\"search\"]:checked ~ .md-header & {\n        width: 100%;\n        // Hack: when the header is translated upon scrolling, a new layer is\n        // induced, which means that the height will now refer to the height of\n        // the header, albeit positioning is fixed. This should be mitigated\n        // in all cases when setting the height to 2x the viewport.\n        height: 200vh;\n        opacity: 1;\n        transition:\n          width     0ms,\n          height    0ms,\n          opacity 250ms;\n      }\n    }\n\n    // Adjust appearance when search is active\n    [data-md-toggle=\"search\"]:checked ~ .md-header & {\n\n      // [mobile portrait -]: Scale up 45 times\n      @include break-to-device(mobile portrait) {\n        transform: scale(45);\n      }\n\n      // [mobile landscape]: Scale up 60 times\n      @include break-at-device(mobile landscape) {\n        transform: scale(60);\n      }\n\n      // [tablet portrait]: Scale up 75 times\n      @include break-at-device(tablet portrait) {\n        transform: scale(75);\n      }\n    }\n  }\n\n  // Search wrapper\n  &__inner {\n    // Hack: promote to own layer to reduce jitter\n    backface-visibility: hidden;\n\n    // [tablet portrait -]: Search modal\n    @include break-to-device(tablet portrait) {\n      position: fixed;\n      inset-inline-start: 0;\n      top: 0;\n      z-index: 2;\n      width: 0;\n      height: 0;\n      overflow: hidden;\n      opacity: 0;\n      transition:\n        width       0ms 300ms,\n        height      0ms 300ms,\n        transform 150ms 150ms cubic-bezier(0.4, 0, 0.2, 1),\n        opacity   150ms 150ms;\n      transform: translateX(5%);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        transform: translateX(-5%);\n      }\n\n      // Adjust appearance when search is active\n      [data-md-toggle=\"search\"]:checked ~ .md-header & {\n        width: 100%;\n        height: 100%;\n        opacity: 1;\n        transition:\n          width       0ms   0ms,\n          height      0ms   0ms,\n          transform 150ms 150ms cubic-bezier(0.1, 0.7, 0.1, 1),\n          opacity   150ms 150ms;\n        transform: translateX(0);\n      }\n    }\n\n    // [tablet landscape +]: Header-embedded search\n    @include break-from-device(tablet landscape) {\n      position: relative;\n      float: inline-end;\n      width: px2rem(234px);\n      padding: px2rem(2px) 0;\n      transition: width 250ms cubic-bezier(0.1, 0.7, 0.1, 1);\n    }\n\n    // Adjust appearance when search is active\n    [data-md-toggle=\"search\"]:checked ~ .md-header & {\n\n      // [tablet landscape]: Omit overlaying header title\n      @include break-at-device(tablet landscape) {\n        width: px2rem(468px);\n      }\n\n      // [screen +]: Match width of content area\n      @include break-from-device(screen) {\n        width: px2rem(688px);\n      }\n    }\n  }\n\n  // Search form\n  &__form {\n    position: relative;\n    z-index: 2;\n    height: px2rem(48px);\n    background-color: var(--md-default-bg-color);\n    box-shadow: 0 0 px2rem(12px) transparent;\n    transition:\n      color            250ms,\n      background-color 250ms;\n\n    // [tablet landscape +]: Header-embedded search\n    @include break-from-device(tablet landscape) {\n      height: px2rem(36px);\n      background-color: hsla(0, 0%, 0%, 0.26);\n      border-radius: px2rem(2px);\n\n      // Search form on hover\n      &:hover {\n        background-color: hsla(0, 0%, 100%, 0.12);\n      }\n    }\n\n    // Adjust appearance when search is active\n    [data-md-toggle=\"search\"]:checked ~ .md-header & {\n      color: var(--md-default-fg-color);\n      background-color: var(--md-default-bg-color);\n      border-radius: px2rem(2px) px2rem(2px) 0 0;\n      box-shadow: 0 0 px2rem(12px) hsla(0, 0%, 0%, 0.07);\n    }\n  }\n\n  // Search input\n  &__input {\n    position: relative;\n    z-index: 2;\n    width: 100%;\n    height: 100%;\n    padding-inline: px2rem(72px) px2rem(44px);\n    font-size: px2rem(18px);\n    text-overflow: ellipsis;\n    background: transparent;\n\n    // Search placeholder\n    &::placeholder {\n      transition: color 250ms;\n    }\n\n    // Search icon and placeholder\n    ~ .md-search__icon,\n    &::placeholder {\n      color: var(--md-default-fg-color--light);\n    }\n\n    // Remove the \"x\" rendered by Internet Explorer\n    &::-ms-clear {\n      display: none;\n    }\n\n    // [tablet portrait -]: Search modal\n    @include break-to-device(tablet portrait) {\n      width: 100%;\n      height: px2rem(48px);\n      font-size: px2rem(18px);\n    }\n\n    // [tablet landscape +]: Header-embedded search\n    @include break-from-device(tablet landscape) {\n      padding-inline-start: px2rem(44px);\n      font-size: px2rem(16px);\n      color: inherit;\n\n      // Search placeholder\n      &::placeholder {\n        color: var(--md-primary-bg-color--light);\n      }\n\n      // Search icon\n      + .md-search__icon {\n        color: var(--md-primary-bg-color);\n      }\n\n      // Adjust appearance when search is active\n      [data-md-toggle=\"search\"]:checked ~ .md-header & {\n        text-overflow: clip;\n\n        // Search icon and placeholder\n        + .md-search__icon {\n          color: var(--md-default-fg-color--light);\n        }\n\n        // Search placeholder\n        &::placeholder {\n          color: transparent;\n        }\n      }\n    }\n  }\n\n  // Search icon\n  &__icon {\n    display: inline-block;\n    width: px2rem(24px);\n    height: px2rem(24px);\n    cursor: pointer;\n    transition:\n      color   250ms,\n      opacity 250ms;\n\n    // Search icon on hover\n    &:hover {\n      opacity: 0.7;\n    }\n\n    // Search focus button\n    &[for=\"__search\"] {\n      position: absolute;\n      inset-inline-start: px2rem(10px);\n      top: px2rem(6px);\n      z-index: 2;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & svg {\n        transform: scaleX(-1);\n      }\n\n      // [tablet portrait -]: Search modal\n      @include break-to-device(tablet portrait) {\n        inset-inline-start: px2rem(16px);\n        top: px2rem(12px);\n\n        // Hide the magnifying glass\n        svg:first-child {\n          display: none;\n        }\n      }\n\n      // [tablet landscape +]: Header-embedded search\n      @include break-from-device(tablet landscape) {\n        pointer-events: none;\n\n        // Hide the back arrow\n        svg:last-child {\n          display: none;\n        }\n      }\n    }\n  }\n\n  // Search options\n  &__options {\n    position: absolute;\n    inset-inline-end: px2rem(10px);\n    top: px2rem(6px);\n    z-index: 2;\n    pointer-events: none;\n\n    // [tablet portrait -]: Search modal\n    @include break-to-device(tablet portrait) {\n      inset-inline-end: px2rem(16px);\n      top: px2rem(12px);\n    }\n\n    // Search option buttons\n    > .md-icon {\n      margin-inline-start: px2rem(4px);\n      color: var(--md-default-fg-color--light);\n      opacity: 0;\n      transition:\n        transform 150ms cubic-bezier(0.1, 0.7, 0.1, 1),\n        opacity   150ms;\n      transform: scale(0.75);\n\n      // Hide outline for pointer devices\n      &:not(.focus-visible) {\n        outline: none;\n        -webkit-tap-highlight-color: transparent;\n      }\n\n      // Show buttons when search is active and input non-empty\n      [data-md-toggle=\"search\"]:checked ~ .md-header // stylelint-disable-line\n      .md-search__input:valid ~ & {\n        pointer-events: initial;\n        opacity: 1;\n        transform: scale(1);\n\n        // Search focus icon\n        &:hover {\n          opacity: 0.7;\n        }\n      }\n    }\n  }\n\n  // Search suggestions\n  &__suggest {\n    position: absolute;\n    top: 0;\n    display: flex;\n    align-items: center;\n    width: 100%;\n    height: 100%;\n    padding-inline: px2rem(72px) px2rem(44px);\n    font-size: px2rem(18px);\n    color: var(--md-default-fg-color--lighter);\n    white-space: nowrap;\n    opacity: 0;\n    transition: opacity 50ms;\n\n    // [tablet landscape +]: Header-embedded search\n    @include break-from-device(tablet landscape) {\n      padding-inline-start: px2rem(44px);\n      font-size: px2rem(16px);\n    }\n\n    // Show suggestions when search is active\n    [data-md-toggle=\"search\"]:checked ~ .md-header & {\n      opacity: 1;\n      transition: opacity 300ms 100ms;\n    }\n  }\n\n  // Search output\n  &__output {\n    position: absolute;\n    z-index: 1;\n    width: 100%;\n    overflow: hidden;\n    border-end-start-radius: px2rem(2px);\n    border-end-end-radius: px2rem(2px);\n\n    // [tablet portrait -]: Search modal\n    @include break-to-device(tablet portrait) {\n      top: px2rem(48px);\n      bottom: 0;\n    }\n\n    // [tablet landscape +]: Header-embedded search\n    @include break-from-device(tablet landscape) {\n      top: px2rem(38px);\n      opacity: 0;\n      transition: opacity 400ms;\n\n      // Show output when search is active\n      [data-md-toggle=\"search\"]:checked ~ .md-header & {\n        box-shadow: var(--md-shadow-z3);\n        opacity: 1;\n      }\n    }\n  }\n\n  // Search scroll wrapper\n  &__scrollwrap {\n    height: 100%;\n    overflow-y: auto;\n    // Hack: Chrome 88+ has weird overscroll behavior. Overall, scroll snapping\n    // seems to be something that is not ready for prime time on some browsers.\n    // scroll-snap-type: y mandatory;\n    touch-action: pan-y;\n    background-color: var(--md-default-bg-color);\n    // Hack: promote to own layer to reduce jitter\n    backface-visibility: hidden;\n\n    // Mitigiate excessive repaints on non-retina devices\n    @media (max-resolution: 1dppx) {\n      transform: translateZ(0);\n    }\n\n    // [tablet landscape]: Set fixed width to omit unnecessary reflow\n    @include break-at-device(tablet landscape) {\n      width: px2rem(468px);\n    }\n\n    // [screen +]: Set fixed width to omit unnecessary reflow\n    @include break-from-device(screen) {\n      width: px2rem(688px);\n    }\n\n    // [tablet landscape +]: Limit height to viewport\n    @include break-from-device(tablet landscape) {\n      max-height: 0;\n      scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n      scrollbar-width: thin;\n\n      // Show scroll wrapper when search is active\n      [data-md-toggle=\"search\"]:checked ~ .md-header & {\n        max-height: 75vh;\n      }\n\n      // Search scroll wrapper on hover\n      &:hover {\n        scrollbar-color: var(--md-accent-fg-color) transparent;\n      }\n\n      // Webkit scrollbar\n      &::-webkit-scrollbar {\n        width: px2rem(4px);\n        height: px2rem(4px);\n      }\n\n      // Webkit scrollbar thumb\n      &::-webkit-scrollbar-thumb {\n        background-color: var(--md-default-fg-color--lighter);\n\n        // Webkit scrollbar thumb on hover\n        &:hover {\n          background-color: var(--md-accent-fg-color);\n        }\n      }\n    }\n  }\n}\n\n// Search result\n.md-search-result {\n  color: var(--md-default-fg-color);\n  word-break: break-word;\n\n  // Search result metadata\n  &__meta {\n    padding: 0 px2rem(16px);\n    font-size: px2rem(12.8px);\n    line-height: px2rem(36px);\n    color: var(--md-default-fg-color--light);\n    scroll-snap-align: start;\n    background-color: var(--md-default-fg-color--lightest);\n\n    // [tablet landscape +]: Adjust spacing\n    @include break-from-device(tablet landscape) {\n      padding-inline-start: px2rem(44px);\n    }\n  }\n\n  // Search result list\n  &__list {\n    padding: 0;\n    margin: 0;\n    list-style: none;\n    // Hack: omit accidental text selection on fast toggle of more button\n    user-select: none;\n  }\n\n  // Search result item\n  &__item {\n    box-shadow: 0 px2rem(-1px) var(--md-default-fg-color--lightest);\n\n    // Omit border on first child\n    &:first-child {\n      box-shadow: none;\n    }\n  }\n\n  // Search result link\n  &__link {\n    display: block;\n    scroll-snap-align: start;\n    outline: none;\n    transition: background-color 250ms;\n\n    // Search result link on focus/hover\n    &:is(:focus, :hover) {\n      background-color: var(--md-accent-fg-color--transparent);\n    }\n\n    // Adjust spacing on last child of last link\n    &:last-child p:last-child {\n      margin-bottom: px2rem(12px);\n    }\n  }\n\n  // Search result more container\n  &__more > summary {\n    position: sticky;\n    top: 0;\n    z-index: 1;\n    display: block;\n    cursor: pointer;\n    scroll-snap-align: start;\n    outline: none;\n\n    // Hide native details marker\n    &::marker {\n      display: none;\n    }\n\n    // Hide native details marker - legacy, must be split into a separate rule,\n    // so older browsers don't consider the selector list as invalid\n    &::-webkit-details-marker {\n      display: none;\n    }\n\n    // Search result more button\n    > div {\n      padding: px2em(12px) px2rem(16px);\n      font-size: px2rem(12.8px);\n      color: var(--md-typeset-a-color);\n      transition:\n        color            250ms,\n        background-color 250ms;\n\n      // [tablet landscape +]: Adjust spacing\n      @include break-from-device(tablet landscape) {\n        padding-inline-start: px2rem(44px);\n      }\n    }\n\n    // Search result more link on focus/hover\n    &:is(:focus, :hover) > div {\n      color: var(--md-accent-fg-color);\n      background-color: var(--md-accent-fg-color--transparent);\n    }\n  }\n\n  // Adjust background for more container in open state\n  &__more[open] > summary {\n    background-color: var(--md-default-bg-color);\n    // box-shadow: 0 px2rem(-1px) hsla(0, 0%, 0%, 0.07) inset;\n  }\n\n  // Search result article\n  &__article {\n    position: relative;\n    padding: 0 px2rem(16px);\n    overflow: hidden;\n\n    // [tablet landscape +]: Adjust spacing\n    @include break-from-device(tablet landscape) {\n      padding-inline-start: px2rem(44px);\n    }\n  }\n\n  // Search result icon\n  &__icon {\n    position: absolute;\n    inset-inline-start: 0;\n    width: px2rem(24px);\n    height: px2rem(24px);\n    margin: px2rem(10px);\n    color: var(--md-default-fg-color--light);\n\n    // [tablet portrait -]: Hide icon\n    @include break-to-device(tablet portrait) {\n      display: none;\n    }\n\n    // Search result icon content\n    &::after {\n      display: inline-block;\n      width: 100%;\n      height: 100%;\n      content: \"\";\n      background-color: currentcolor;\n      mask-image: var(--md-search-result-icon);\n      mask-repeat: no-repeat;\n      mask-position: center;\n      mask-size: contain;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        transform: scaleX(-1);\n      }\n    }\n  }\n\n  // Typesetted content\n  .md-typeset {\n    font-size: px2rem(12.8px);\n    line-height: 1.6;\n    color: var(--md-default-fg-color--light);\n\n    // Search result article title\n    h1 {\n      margin: px2rem(11px) 0;\n      font-size: px2rem(16px);\n      font-weight: 400;\n      line-height: 1.4;\n      color: var(--md-default-fg-color);\n\n      // Search term highlighting\n      mark {\n        text-decoration: none;\n      }\n    }\n\n    // Search result section title\n    h2 {\n      margin: 0.5em 0;\n      font-size: px2rem(12.8px);\n      font-weight: 700;\n      line-height: 1.6;\n      color: var(--md-default-fg-color);\n\n      // Search term highlighting\n      mark {\n        text-decoration: none;\n      }\n    }\n  }\n\n  // Search result terms\n  &__terms {\n    display: block;\n    margin: 0.5em 0;\n    font-size: px2rem(12.8px);\n    font-style: italic;\n    color: var(--md-default-fg-color);\n  }\n\n  // Search term highlighting\n  mark {\n    color: var(--md-accent-fg-color);\n    text-decoration: underline;\n    background-color: transparent;\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Selection\n.md-select {\n  position: relative;\n  z-index: 1;\n\n  // Selection tooltip\n  &__inner {\n    position: absolute;\n    top: calc(100% - #{px2rem(4px)});\n    left: 50%;\n    max-height: 0;\n    margin-top: px2rem(4px);\n    color: var(--md-default-fg-color);\n    background-color: var(--md-default-bg-color);\n    border-radius: px2rem(2px);\n    box-shadow: var(--md-shadow-z2);\n    opacity: 0;\n    transition:\n      transform  250ms 375ms,\n      opacity    250ms 250ms,\n      max-height   0ms 500ms;\n    transform: translate3d(-50%, px2rem(6px), 0);\n\n    // Selection bubble on parent focus/hover\n    .md-select:is(:focus-within, :hover) & {\n      max-height: px2rem(200px);\n      opacity: 1;\n      transition:\n        transform  250ms cubic-bezier(0.1, 0.7, 0.1, 1),\n        opacity    250ms,\n        max-height   0ms;\n      transform: translate3d(-50%, 0, 0);\n    }\n\n    // Selection bubble handle\n    &::after {\n      position: absolute;\n      top: 0;\n      left: 50%;\n      width: 0;\n      height: 0;\n      margin-top: px2rem(-4px);\n      margin-left: px2rem(-4px);\n      content: \"\";\n      border: px2rem(4px) solid transparent;\n      border-top: 0;\n      border-bottom-color: var(--md-default-bg-color);\n    }\n  }\n\n  // Selection list\n  &__list {\n    max-height: inherit;\n    padding: 0;\n    margin: 0;\n    overflow: auto;\n    font-size: px2rem(16px);\n    list-style-type: none;\n    border-radius: px2rem(2px);\n  }\n\n  // Selection item\n  &__item {\n    line-height: px2rem(36px);\n  }\n\n  // Selection link\n  &__link {\n    display: block;\n    width: 100%;\n    padding-inline: px2rem(12px) px2rem(24px);\n    cursor: pointer;\n    scroll-snap-align: start;\n    outline: none;\n    transition:\n      background-color 250ms,\n      color            250ms;\n\n    // Link on focus/hover\n    &:is(:focus, :hover) {\n      color: var(--md-accent-fg-color);\n    }\n\n    // Link on focus\n    &:focus {\n      background-color: var(--md-default-fg-color--lightest);\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Sidebar\n.md-sidebar {\n  position: sticky;\n  top: px2rem(48px);\n  flex-shrink: 0;\n  align-self: flex-start;\n  width: px2rem(242px);\n  padding: px2rem(24px) 0;\n\n  // [print]: Hide sidebar\n  @media print {\n    display: none;\n  }\n\n  // Primary sidebar with navigation\n  &--primary {\n\n    // [tablet -]: Show navigation as drawer\n    @include break-to-device(tablet) {\n      position: fixed;\n      inset-inline-start: px2rem(-242px);\n      top: 0;\n      z-index: 5;\n      display: block;\n      width: px2rem(242px);\n      height: 100%;\n      background-color: var(--md-default-bg-color);\n      transition:\n        transform  250ms cubic-bezier(0.4, 0, 0.2, 1),\n        box-shadow 250ms;\n      transform: translateX(0);\n\n      // Show sidebar when drawer is active\n      [data-md-toggle=\"drawer\"]:checked ~ .md-container & {\n        box-shadow: var(--md-shadow-z3);\n        transform: translateX(px2rem(242px));\n\n        // Adjust for right-to-left languages\n        [dir=\"rtl\"] & {\n          transform: translateX(px2rem(-242px));\n        }\n      }\n\n      // Stretch scroll wrapper for primary sidebar\n      .md-sidebar__scrollwrap {\n        position: absolute;\n        inset: 0;\n        margin: 0;\n        overflow: hidden;\n        scroll-snap-type: none;\n      }\n    }\n  }\n\n  // [screen +]: Show navigation as sidebar\n  @include break-from-device(screen) {\n    height: 0;\n\n    // [no-js]: Switch to native sticky behavior\n    .no-js & {\n      height: auto;\n    }\n\n    // Adjust spacing for sticky navigation tabs\n    .md-header--lifted ~ .md-container & {\n      top: px2rem(96px);\n    }\n  }\n\n  // Secondary sidebar with table of contents\n  &--secondary {\n    display: none;\n    order: 2;\n\n    // [tablet landscape +]: Show table of contents as sidebar\n    @include break-from-device(tablet landscape) {\n      height: 0;\n\n      // [no-js]: Switch to native sticky behavior\n      .no-js & {\n        height: auto;\n      }\n\n      // Sidebar is visible\n      &:not([hidden]) {\n        display: block;\n      }\n\n      // Ensure smooth scrolling on iOS\n      .md-sidebar__scrollwrap {\n        touch-action: pan-y;\n      }\n    }\n  }\n\n  // Sidebar scroll wrapper\n  &__scrollwrap {\n    margin: 0 px2rem(4px);\n    overflow-y: auto;\n    scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n    scrollbar-gutter: stable;\n    // Hack: Chrome 81+ exhibits a strange bug, where it scrolls the container\n    // to the bottom if `scroll-snap-type` is set on the initial render. For\n    // this reason, we disable scroll snapping until this is resolved (#1667).\n    // scroll-snap-type: y mandatory;\n    scrollbar-width: thin;\n    // Hack: promote to own layer to reduce jitter\n    backface-visibility: hidden;\n\n    // Webkit scrollbar\n    &::-webkit-scrollbar {\n      width: px2rem(4px);\n      height: px2rem(4px);\n    }\n\n    // Sidebar scroll wrapper on focus/hover\n    &:is(:focus-within, :hover) {\n      scrollbar-color: var(--md-accent-fg-color) transparent;\n\n      // Webkit scrollbar thumb\n      &::-webkit-scrollbar-thumb {\n        background-color: var(--md-default-fg-color--lighter);\n\n        // Webkit scrollbar thumb on hover\n        &:hover {\n          background-color: var(--md-accent-fg-color);\n        }\n      }\n    }\n  }\n\n  // Hack: the scrollbar is only visible when the sidebar's contents overflow,\n  // which is nice, but leads to the problem where the chevrons of expandable\n  // sections will jump by `4px` when the sidebar is shown. We wanted to fix\n  // this problem for so long, but haven't found a clean way of doing it.\n  // Until now. The following declaration is only applied to Webkit browsers\n  // (e.g. Chrome and Safari), which support styling of scrollbars. The trick\n  // is to add conditional padding on the side of the scrollbar only if the\n  // sidebar's content doesn't overflow. This hack is inspired and adapted\n  // from Ayke van Laëthem's year old trick – see https://bit.ly/3Sb1qql\n  @supports selector(::-webkit-scrollbar) {\n\n    // Sidebar scroll wrapper\n    &__scrollwrap {\n      scrollbar-gutter: auto;\n    }\n\n    // Sidebar wrapper\n    &__inner {\n      padding-inline-end: calc(100% - #{px2rem(230px)});\n    }\n  }\n}\n\n// [tablet -]: Show overlay on active drawer\n@include break-to-device(tablet) {\n\n  // Drawer overlay\n  .md-overlay {\n    position: fixed;\n    top: 0;\n    z-index: 5;\n    width: 0;\n    height: 0;\n    background-color: hsla(0, 0%, 0%, 0.54);\n    opacity: 0;\n    transition:\n      width     0ms 250ms,\n      height    0ms 250ms,\n      opacity 250ms;\n\n    // Show overlay when drawer is active\n    [data-md-toggle=\"drawer\"]:checked ~ & {\n      width: 100%;\n      height: 100%;\n      opacity: 1;\n      transition:\n        width     0ms,\n        height    0ms,\n        opacity 250ms;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Keyframes\n// ----------------------------------------------------------------------------\n\n// Show repository facts\n@keyframes facts {\n  0% {\n    height: 0;\n  }\n\n  100% {\n    height: px2rem(13px);\n  }\n}\n\n// Show repository fact\n@keyframes fact {\n  0% {\n    opacity: 0;\n    transform: translateY(100%);\n  }\n\n  50% {\n    opacity: 0;\n  }\n\n  100% {\n    opacity: 1;\n    transform: translateY(0%);\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Repository information variables\n:root {\n  --md-source-forks-icon: svg-load(\"octicons/repo-forked-16.svg\");\n  --md-source-repositories-icon: svg-load(\"octicons/repo-16.svg\");\n  --md-source-stars-icon: svg-load(\"octicons/star-16.svg\");\n  --md-source-version-icon: svg-load(\"octicons/tag-16.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Repository information\n.md-source {\n  display: block;\n  font-size: px2rem(13px);\n  line-height: 1.2;\n  white-space: nowrap;\n  outline-color: var(--md-accent-fg-color);\n  // Hack: promote to own layer to reduce jitter\n  backface-visibility: hidden;\n  transition: opacity 250ms;\n\n  // Repository information on hover\n  &:hover {\n    opacity: 0.7;\n  }\n\n  // Repository icon\n  &__icon {\n    display: inline-block;\n    width: px2rem(40px);\n    height: px2rem(48px);\n    vertical-align: middle;\n\n    // Align with margin only (as opposed to normal button alignment)\n    svg {\n      margin-inline-start: px2rem(12px);\n      margin-top: px2rem(12px);\n    }\n\n    // Adjust spacing if icon is present\n    + .md-source__repository {\n      padding-inline-start: px2rem(40px);\n      margin-inline-start: px2rem(-40px);\n    }\n  }\n\n  // Repository name\n  &__repository {\n    display: inline-block;\n    max-width: calc(100% - #{px2rem(24px)});\n    margin-inline-start: px2rem(12px);\n    overflow: hidden;\n    text-overflow: ellipsis;\n    vertical-align: middle;\n  }\n\n  // Repository facts\n  &__facts {\n    display: flex;\n    gap: px2rem(8px);\n    width: 100%;\n    padding: 0;\n    margin: px2rem(2px) 0 0;\n    overflow: hidden;\n    font-size: px2rem(11px);\n    list-style-type: none;\n    opacity: 0.75;\n\n    // Show after the data was loaded\n    .md-source__repository--active & {\n      animation: facts 250ms ease-in;\n    }\n  }\n\n  // Repository fact\n  &__fact {\n    overflow: hidden;\n    text-overflow: ellipsis;\n\n    // Show after the data was loaded\n    .md-source__repository--active & {\n      animation: fact 400ms ease-out;\n    }\n\n    // Repository fact icon\n    &::before {\n      display: inline-block;\n      width: px2rem(12px);\n      height: px2rem(12px);\n      margin-inline-end: px2rem(2px);\n      vertical-align: text-top;\n      content: \"\";\n      background-color: currentcolor;\n      mask-repeat: no-repeat;\n      mask-position: center;\n      mask-size: contain;\n    }\n\n    // Adjust spacing for 2nd+ fact\n    &:nth-child(1n+2) {\n      flex-shrink: 0;\n    }\n\n    // Repository fact: version\n    &--version::before {\n      mask-image: var(--md-source-version-icon);\n    }\n\n    // Repository fact: stars\n    &--stars::before {\n      mask-image: var(--md-source-stars-icon);\n    }\n\n    // Repository fact: forks\n    &--forks::before {\n      mask-image: var(--md-source-forks-icon);\n    }\n\n    // Repository fact: repositories\n    &--repositories::before {\n      mask-image: var(--md-source-repositories-icon);\n    }\n  }\n}\n\n// Source file information\n.md-source-file {\n  margin: 1em 0;\n\n  // Source file information fact\n  &__fact {\n    display: inline-flex;\n    gap: px2rem(6px);\n    align-items: center;\n    margin-inline-end: px2rem(12px);\n    font-size: px2rem(13.6px);\n    color: var(--md-default-fg-color--light);\n\n    // Adjust vertical spacing\n    .md-icon {\n      flex-shrink: 0;\n      margin-bottom: px2rem(1px);\n    }\n\n    // Author\n    .md-author {\n      float: inline-start;\n      margin-right: px2rem(4px);\n    }\n\n    // Adjust size of icon\n    svg {\n      width: px2rem(18px);\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Status variables\n\n/* stylelint-disable scss/comment-no-loud */\n\n/* sphinx-immaterial: exclude default status icons,\n   they are defined by custom_admonitions.css instead\n\n:root {\n  --md-status: svg-load(\"material/information-outline.svg\");\n  --md-status--new: svg-load(\"material/alert-decagram.svg\");\n  --md-status--deprecated: svg-load(\"material/trash-can.svg\");\n  --md-status--encrypted: svg-load(\"material/shield-lock.svg\");\n}\n\nsphinx-immaterial: end exclude default status icons */\n\n// ----------------------------------------------------------------------------\n\n// Status\n.md-status {\n  // Status icon\n  &::after {\n    display: inline-block;\n    width: px2em(18px);\n    height: px2em(18px);\n    vertical-align: text-bottom;\n    content: \"\";\n    background-color: var(--md-default-fg-color--light);\n\n    /* sphinx-immaterial: exclude default status icon,\n       all status icons should be defined\n    mask-image: var(--md-status);\n       sphinx_immaterial: end exclude default status icon */\n    mask-repeat: no-repeat;\n    mask-position: center;\n    mask-size: contain;\n  }\n\n  // Status icon on hover\n  &:hover::after {\n    background-color: currentcolor;\n  }\n\n  /* sphinx-immaterial: exclude default status icons\n\n  // Status: new\n  &--new::after {\n    mask-image: var(--md-status--new);\n  }\n\n  // Status: deprecated\n  &--deprecated::after {\n    mask-image: var(--md-status--deprecated);\n  }\n\n  // Status: encrypted\n  &--encrypted::after {\n    mask-image: var(--md-status--encrypted);\n  }\n\n  sphinx-immaterial: end exclude default status icons */\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Navigation tabs\n.md-tabs {\n  // Must be higher than the z-index of the back-to-top button, or the button\n  // will overlay the navigation tabs bar when scrolling up fast.\n  z-index: 3;\n  display: block;\n  width: 100%;\n  overflow: auto;\n  line-height: 1.3;\n  color: var(--md-primary-bg-color);\n  background-color: var(--md-primary-fg-color);\n\n  // [print]: Hide tabs\n  @media print {\n    display: none;\n  }\n\n  // [tablet -]: Hide tabs\n  @include break-to-device(tablet) {\n    display: none;\n  }\n\n  // Navigation tabs are hidden\n  &[hidden] {\n    pointer-events: none;\n  }\n\n  // Navigation tabs list\n  &__list {\n    display: flex;\n    padding: 0;\n    margin: 0;\n    margin-inline-start: px2rem(4px);\n    overflow: auto;\n    white-space: nowrap;\n    list-style: none;\n    contain: content;\n    // Hack: don't show scrollbar when navigation tabs overflow, which should\n    // only happen in rare occasions, as adding too many top level sections is\n    // discouraged, since hiding content on horitontal axis doesn't lead to a\n    // good user experience. It's just harder to discover.\n    scrollbar-width: none;\n\n    // Hack: see above\n    &::-webkit-scrollbar {\n      display: none;\n    }\n  }\n\n  // Navigation tabs item\n  &__item {\n    height: px2rem(48px);\n    padding-inline: px2rem(12px);\n\n    // Navigation tabs link in active navigation\n    &--active .md-tabs__link {\n      color: inherit;\n      opacity: 1;\n    }\n  }\n\n  // Navigation tabs link - could be defined as block elements and aligned via\n  // line height, but this would imply more repaints when scrolling\n  &__link {\n    display: flex;\n    margin-top: px2rem(16px);\n    font-size: px2rem(14px);\n    outline-color: var(--md-accent-fg-color);\n    outline-offset: px2rem(4px);\n    // Hack: save a repaint when tabs are appearing on scrolling up\n    backface-visibility: hidden;\n    opacity: 0.7;\n    transition:\n      transform 400ms cubic-bezier(0.1, 0.7, 0.1, 1),\n      opacity   250ms;\n\n    // Navigation tabs link on focus/hover\n    &:is(:focus, :hover) {\n      color: inherit;\n      opacity: 1;\n    }\n\n    // Navigation tabs link icon\n    svg {\n      height: 1.3em;\n      margin-inline-end: px2rem(8px);\n      fill: currentcolor;\n    }\n\n    // Delay transitions by a small amount\n    @for $i from 2 through 16 {\n      .md-tabs__item:nth-child(#{$i}) & {\n        transition-delay: 20ms * ($i - 1);\n      }\n    }\n\n    // Hide tabs upon scrolling - disable transition to minimizes repaints\n    // while scrolling down, while scrolling up seems to be okay\n    .md-tabs[hidden] & {\n      opacity: 0;\n      transition:\n        transform 0ms 100ms,\n        opacity 100ms;\n      transform: translateY(50%);\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Tag variables\n:root {\n  --md-tag-icon: svg-load(\"material/pound.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Tag list (if not hidden)\n  .md-tags:not([hidden]) {\n    display: inline-flex;\n    flex-wrap: wrap;\n    gap: px2em(8px);\n    margin-top: px2em(-2px);\n    margin-bottom: px2em(12px);\n  }\n\n  // Tag\n  .md-tag {\n    display: inline-flex;\n    gap: px2em(8px);\n    align-items: center;\n    padding: px2em(4px, 12.8px) px2em(10px, 12.8px);\n    font-size: px2rem(12.8px); // Fallback\n    font-size: min(px2em(12.8px), px2rem(12.8px));\n    font-weight: 700;\n    line-height: 1.6;\n    letter-spacing: initial;\n    background: var(--md-default-fg-color--lightest);\n    border-radius: px2rem(48px);\n\n    // Linked tag\n    &[href] {\n      color: inherit;\n      outline: none;\n      -webkit-tap-highlight-color: transparent;\n      transition:\n        color            125ms,\n        background-color 125ms;\n\n      // Linked tag on focus/hover\n      &:is(:focus, :hover) {\n        color: var(--md-accent-bg-color);\n        background-color: var(--md-accent-fg-color);\n      }\n    }\n\n    // Tag inside headline\n    [id] > & {\n      vertical-align: text-top;\n    }\n  }\n\n  // Tag icon\n  .md-tag-icon {\n\n    // Tag icon content\n    &::before {\n      display: inline-block;\n      width: 1.2em;\n      height: 1.2em;\n      vertical-align: text-bottom;\n      content: \"\";\n      background-color: var(--md-default-fg-color--lighter);\n      mask-image: var(--md-tag-icon);\n      mask-repeat: no-repeat;\n      mask-position: center;\n      mask-size: contain;\n      transition: background-color 125ms;\n    }\n\n    // Linked tag on focus/hover\n    &[href]:is(:focus, :hover)::before {\n      background-color: var(--md-accent-bg-color);\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Keyframes\n// ----------------------------------------------------------------------------\n\n// Continuous pulse animation\n@keyframes pulse {\n  0% {\n    transform: scale(0.95);\n  }\n\n  75% {\n    transform: scale(1);\n  }\n\n  100% {\n    transform: scale(0.95);\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Tooltip variables\n:root {\n  --md-annotation-bg-icon: svg-load(\"material/circle.svg\");\n  --md-annotation-icon: svg-load(\"material/plus-circle.svg\");\n  --md-tooltip-width: #{px2rem(400px)};\n}\n\n// ----------------------------------------------------------------------------\n\n// Tooltip\n.md-tooltip {\n  position: absolute;\n  top: var(--md-tooltip-y);\n  left:\n    clamp(\n      var(--md-tooltip-0, #{px2rem(0px)}) + #{px2rem(16px)},\n      var(--md-tooltip-x),\n      100vw +\n      var(--md-tooltip-0, #{px2rem(0px)}) + #{px2rem(16px)} -\n      var(--md-tooltip-width) -\n      2 * #{px2rem(16px)}\n    );\n  // Hack: set an explicit `z-index` so we can transition it to ensure that any\n  // following elements are not overlaying the tooltip during the transition.\n  z-index: 0;\n  width: var(--md-tooltip-width);\n  max-width: calc(100vw - 2 * #{px2rem(16px)});\n  font-family: var(--md-text-font-family);\n  color: var(--md-default-fg-color);\n  background-color: var(--md-default-bg-color);\n  border-radius: px2rem(2px);\n  box-shadow: var(--md-shadow-z2);\n  opacity: 0;\n  transition:\n    transform 0ms 250ms,\n    opacity 250ms,\n    z-index 250ms;\n  transform: translateY(px2rem(-8px));\n  // Hack: promote to own layer to reduce jitter\n  backface-visibility: hidden;\n\n  // Active tooltip\n  &--active {\n    z-index: 2;\n    opacity: 1;\n    transition:\n      transform 250ms cubic-bezier(0.1, 0.7, 0.1, 1),\n      opacity   250ms,\n      z-index     0ms;\n    transform: translateY(0);\n  }\n\n  // Inline tooltip\n  &--inline {\n    width: auto;\n    font-weight: 700;\n    user-select: none;\n\n    // Tooltip is not active\n    &:not(.md-tooltip--active) {\n      transform: translateY(px2rem(4px)) scale(0.9);\n    }\n\n    // Tooltip wrapper\n    .md-tooltip__inner {\n      padding: px2rem(4px) px2rem(8px);\n      font-size: px2rem(10px);\n    }\n\n    // Hack: When the host element is hidden, the context for the tooltip is\n    // lost immediately, resulting in invalid and sometimes jumpy positioning.\n    [hidden] + & {\n      display: none;\n    }\n  }\n\n  // Show outline on target and for keyboard devices\n  :is(.focus-visible > &, &:target) {\n    outline: var(--md-accent-fg-color) auto;\n  }\n\n  // Tooltip wrapper\n  &__inner {\n    padding: px2rem(16px);\n    font-size: px2rem(12.8px);\n\n    // Adjust spacing on first child\n    &.md-typeset > :first-child {\n      margin-top: 0;\n    }\n\n    // Adjust spacing on last child\n    &.md-typeset > :last-child {\n      margin-bottom: 0;\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n\n// Annotation\n.md-annotation {\n  font-style: initial;\n  font-weight: 400;\n  text-align: initial;\n  white-space: normal;\n  vertical-align: text-bottom;\n  outline: none;\n\n  // Adjust for right-to-left languages\n  [dir=\"rtl\"] & {\n    direction: rtl;\n  }\n\n  // Annotation index in code block\n  code & {\n    font-family: var(--md-code-font-family);\n    font-size: inherit;\n  }\n\n  // Annotation is not hidden (e.g. when copying)\n  &:not([hidden]) {\n    display: inline-block;\n    // Hack: ensure that the line height doesn't exceed the line height of the\n    // hosting line, because it will lead to dancing pixels.\n    line-height: 1.25;\n  }\n\n  // Annotation index\n  &__index {\n    position: relative;\n    z-index: 0;\n    display: inline-block;\n    margin-inline: 0.4ch;\n    vertical-align: text-top;\n    cursor: pointer;\n    user-select: none;\n    outline: none;\n    // Hack: Work around Firefox bug that renders a subpixel outline when\n    // rotating a mask image element - see https://t.ly/qA1s4\n    overflow: hidden; // stylelint-disable-line order/properties-order\n    border-radius: 0.01px;\n\n    // Hack: increase specificity to override default for anchors in typesetted\n    // content, because transitions are defined on anchor elements\n    .md-annotation & {\n      transition: z-index 250ms;\n    }\n\n    // [screen]: Render annotation markers as icons\n    @media screen {\n      width: 2.2ch;\n\n      // Annotation is visible\n      [data-md-visible] > & {\n        animation: pulse 2000ms infinite;\n      }\n\n      // Annotation marker background\n      &::before {\n        position: absolute;\n        top: -0.1ch;\n        z-index: -1;\n        width: 2.2ch;\n        height: 2.2ch;\n        content: \"\";\n        background: var(--md-default-bg-color);\n        mask-image: var(--md-annotation-bg-icon);\n        mask-repeat: no-repeat;\n        mask-position: center;\n        mask-size: contain;\n      }\n\n      // Annotation marker – the marker must be positioned absolutely behind\n      // the index, because it shouldn't impact the rendering of a code block.\n      // Otherwise, small rounding differences in browsers can sometimes mess up\n      // alignment of text following an annotation.\n      &::after {\n        position: absolute;\n        top: -0.1ch;\n        z-index: -1;\n        width: 2.2ch;\n        height: 2.2ch;\n        content: \"\";\n        background-color: var(--md-default-fg-color--lighter);\n        mask-image: var(--md-annotation-icon);\n        mask-repeat: no-repeat;\n        mask-position: center;\n        mask-size: contain;\n        transition:\n          background-color 250ms,\n          transform        250ms;\n        // Hack: promote to own layer to reduce jitter\n        transform: scale(1.0001);\n\n        // Annotation marker for active tooltip\n        .md-tooltip--active + & {\n          transform: rotate(45deg);\n        }\n\n        // Annotation marker for active tooltip or on hover\n        :is(.md-tooltip--active + &, :hover > &) {\n          background-color: var(--md-accent-fg-color);\n        }\n      }\n    }\n\n    // Annotation index for active tooltip\n    .md-tooltip--active + & {\n      z-index: 2;\n      transition-duration: 0ms;\n      animation-play-state: paused;\n    }\n\n    // Annotation marker\n    [data-md-annotation-id] {\n      display: inline-block;\n\n      // [print]: Render annotation markers as numbers\n      @media print {\n        padding: 0 0.6ch;\n        font-weight: 700;\n        color: var(--md-default-bg-color);\n        white-space: nowrap;\n        background: var(--md-default-fg-color--lighter);\n        border-radius: 2ch;\n\n        // Annotation marker content\n        &::after {\n          content: attr(data-md-annotation-id);\n        }\n      }\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Annotation list\n  .md-annotation-list {\n    list-style: none;\n    counter-reset: xxx;\n\n    // Annotation list item\n    li {\n      position: relative;\n\n      // Annotation list marker\n      &::before {\n        position: absolute;\n        inset-inline-start: px2em(-34px);\n        top: px2em(4px);\n        min-width: 2ch;\n        height: 2ch;\n        padding: 0 0.6ch;\n        font-size: px2em(14.2px);\n        font-weight: 700;\n        line-height: 1.25;\n        color: var(--md-default-bg-color);\n        text-align: center;\n        content: counter(xxx);\n        counter-increment: xxx;\n        background: var(--md-default-fg-color--lighter);\n        border-radius: 2ch;\n      }\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Tooltip variables\n:root {\n  --md-tooltip-width: #{px2rem(400px)};\n  --md-tooltip-tail: #{px2rem(6px)};\n}\n\n// ----------------------------------------------------------------------------\n\n// Tooltip\n.md-tooltip2 {\n  position: absolute;\n  // Note that the top offset is computed from the host element offset plus the\n  // tooltip offset, which is always measured relative to the host element\n  top:\n    calc(\n      var(--md-tooltip-host-y) +\n      var(--md-tooltip-y)\n    );\n  // Hack: set an explicit `z-index` so we can transition it to ensure that any\n  // following elements are not overlaying the tooltip during the transition.\n  z-index: 0;\n  inline-size: 100%;\n  font-family: var(--md-text-font-family);\n  color: var(--md-default-fg-color);\n  pointer-events: none;\n  opacity: 0;\n  transition:\n    transform 0ms 250ms,\n    opacity 250ms,\n    z-index 250ms;\n  transform: translateY(px2rem(-8px));\n  // We explicitly set the origin to the tooltip tail, allowing the author to\n  // easily add further transforms to the tooltip, customizing the transition\n  transform-origin:\n    calc(\n      var(--md-tooltip-host-x) +\n      var(--md-tooltip-x)\n    )\n    0;\n  // Hack: promote to own layer to reduce jitter\n  backface-visibility: hidden;\n\n  // Tooltip tail\n  &::before {\n    position: absolute;\n    // The offset of the tooltip tail is computed from the host element offset,\n    // plus the tooltip offset, which equals the center of the host element,\n    // and minus the half width of the tooltip tail to center it. Then, on both\n    // sides, the tooltip tail is padded with 150% of the inset area.\n    left:\n      clamp(\n        1.5 * #{px2rem(16px)},\n        calc(\n          var(--md-tooltip-host-x) +\n          var(--md-tooltip-x) -\n          var(--md-tooltip-tail)\n        ),\n        calc(\n          100vw -\n          2 * var(--md-tooltip-tail) -\n          1.5 * #{px2rem(16px)}\n        )\n      );\n    z-index: 1;\n    display: block;\n    content: \"\";\n    border-inline: var(--md-tooltip-tail) solid transparent;\n  }\n\n  // Tooltip tail if rendered above target\n  &--top::before {\n    bottom: calc(-1 * var(--md-tooltip-tail) + px2rem(0.5px));\n    filter: drop-shadow(0 1px 0 hsla(0, 0%, 0%, 0.05));\n    border-top: var(--md-tooltip-tail) solid var(--md-default-bg-color);\n  }\n\n  // Tooltip tail if rendered below target\n  &--bottom::before {\n    top: calc(-1 * var(--md-tooltip-tail) + px2rem(0.5px));\n    filter: drop-shadow(0 -1px 0 hsla(0, 0%, 0%, 0.05));\n    border-bottom: var(--md-tooltip-tail) solid var(--md-default-bg-color);\n  }\n\n  // Tooltip is visible\n  &--active {\n    z-index: 2;\n    opacity: 1;\n    transition:\n      transform 400ms cubic-bezier(0, 1, 0.5, 1),\n      opacity   250ms,\n      z-index     0ms;\n    transform: translateY(0);\n  }\n\n  // Tooltip wrapper\n  &__inner {\n    position: relative;\n    // The tooltip is slightly moved to the left, so it nicely aligns with the\n    // content of the tooltip set by the padding of this element. On both sides,\n    // the tooltip is padded with the inset area, so it never touches the edge\n    // of the window for a better user experience.\n    left:\n      clamp(\n        #{px2rem(16px)},\n        calc(\n          var(--md-tooltip-host-x) -\n          #{px2rem(16px)}\n        ),\n        calc(\n          100vw -\n          var(--md-tooltip-width) -\n          #{px2rem(16px)}\n        )\n      );\n    max-width: calc(100vw - 2 * #{px2rem(16px)});\n    max-height: 40vh;\n    scrollbar-gutter: stable;\n    scrollbar-width: thin;\n    background-color: var(--md-default-bg-color);\n    border-radius: px2rem(2px);\n    box-shadow: var(--md-shadow-z2);\n\n    // Webkit scrollbar\n    &::-webkit-scrollbar {\n      width: px2rem(4px);\n      height: px2rem(4px);\n    }\n\n    // Webkit scrollbar thumb\n    &::-webkit-scrollbar-thumb {\n      background-color: var(--md-default-fg-color--lighter);\n\n      // Webkit scrollbar thumb on hover\n      &:hover {\n        background-color: var(--md-accent-fg-color);\n      }\n    }\n\n    // Tooltip is non-interactive - this role should be set if the tooltip has\n    // only informational and non-interactive content, e.g., an actual tooltip.\n    // It has no explicitl width set, uses a smaller font, and is centered,\n    // other than a tooltip with typesetted content.\n    [role=\"tooltip\"] > & {\n      left:\n        clamp(\n          #{px2rem(16px)},\n          calc(\n            var(--md-tooltip-host-x) +\n            var(--md-tooltip-x) -\n            var(--md-tooltip-width) / 2\n          ),\n          calc(\n            100vw -\n            var(--md-tooltip-width) -\n            #{px2rem(16px)}\n          )\n        );\n      width: fit-content;\n      // @todo refactor - this is currently a hack to fix overly long tooltips,\n      // but should be refactored in the future to be more flexible\n      max-width:\n        min(\n          calc(100vw - 2 * #{px2rem(16px)}),\n          400px\n        );\n      padding: px2rem(4px) px2rem(8px);\n      font-size: px2rem(10px);\n      font-weight: 700;\n      // If the author wishes to keep the tooltip visible upon hover and make\n      // the text selectable, this property can be set to `initial`\n      user-select: none;\n    }\n\n    // Adjust spacing on first child\n    &.md-typeset > :first-child {\n      margin-top: 0;\n    }\n\n    // Adjust spacing on last child\n    &.md-typeset > :last-child {\n      margin-bottom: 0;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Back-to-top button\n.md-top {\n  position: fixed;\n  top: px2rem(48px + 16px);\n  z-index: 2;\n  display: block;\n  padding: px2rem(8px) px2rem(16px);\n  margin-inline-start: 50%;\n  font-size: px2rem(14px);\n  color: var(--md-default-fg-color--light);\n  cursor: pointer;\n  background-color: var(--md-default-bg-color);\n  border-radius: px2rem(32px);\n  outline: none;\n  box-shadow: var(--md-shadow-z2);\n  transition:\n    color            125ms,\n    background-color 125ms,\n    transform        125ms cubic-bezier(0.4, 0, 0.2, 1),\n    opacity          125ms;\n  transform: translate(-50%, 0);\n\n  // [print]: Hide back-to-top button\n  @media print {\n    display: none;\n  }\n\n  // Adjust for right-to-left languages\n  [dir=\"rtl\"] & {\n    transform: translate(50%, 0);\n  }\n\n  // Back-to-top button is hidden\n  &[hidden] {\n    pointer-events: none;\n    opacity: 0;\n    transition-duration: 0ms;\n    transform: translate(-50%, px2rem(4px));\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      transform: translate(50%, px2rem(4px));\n    }\n  }\n\n  // Back-to-top button on focus/hover\n  &:is(:focus, :hover) {\n    color: var(--md-accent-bg-color);\n    background-color: var(--md-accent-fg-color);\n  }\n\n  // Inline icon\n  svg {\n    display: inline-block;\n    vertical-align: -0.5em;\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Keyframes\n// ----------------------------------------------------------------------------\n\n// See https://github.com/squidfunk/mkdocs-material/issues/2429\n@keyframes hoverfix {\n  0% {\n    pointer-events: none;\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Version selection variables\n:root {\n  --md-version-icon: svg-load(\"fontawesome/solid/caret-down.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Version selection\n.md-version {\n  flex-shrink: 0;\n  height: px2rem(48px);\n  font-size: px2rem(16px);\n\n  // Current selection\n  &__current {\n    position: relative;\n    // Hack: in general, we would use `vertical-align` to align the version at\n    // the bottom with the title, but since the list uses absolute positioning,\n    // this won't work consistently. Furthermore, we would need to use inline\n    // positioning to align the links, which looks jagged.\n    top: px2rem(1px);\n    margin-inline: px2rem(28px) px2rem(8px);\n    color: inherit;\n    cursor: pointer;\n    outline: none;\n\n    // Version selection icon\n    &::after {\n      display: inline-block;\n      width: px2rem(8px);\n      height: px2rem(12px);\n      margin-inline-start: px2rem(8px);\n      content: \"\";\n      background-color: currentcolor;\n      mask-image: var(--md-version-icon);\n      mask-repeat: no-repeat;\n      mask-position: center;\n      mask-size: contain;\n    }\n  }\n\n  // Version alias\n  &__alias {\n    margin-left: px2rem(6px);\n    opacity: 0.7;\n  }\n\n  // Version selection list\n  &__list {\n    position: absolute;\n    top: px2rem(3px);\n    z-index: 3;\n    max-height: 0;\n    padding: 0;\n    margin: px2rem(4px) px2rem(16px);\n    overflow: auto;\n    color: var(--md-default-fg-color);\n    list-style-type: none;\n    scroll-snap-type: y mandatory;\n    background-color: var(--md-default-bg-color);\n    border-radius: px2rem(2px);\n    box-shadow: var(--md-shadow-z2);\n    opacity: 0;\n    transition:\n      max-height 0ms 500ms,\n      opacity  250ms 250ms;\n\n    // Version selection list on parent focus/hover\n    .md-version:is(:focus-within, :hover) & {\n      max-height: px2rem(200px);\n      opacity: 1;\n      transition:\n        max-height 0ms,\n        opacity  250ms;\n    }\n\n    // Fix hover on touch devices\n    @media (pointer: coarse), (hover: none) {\n      // Switch off on hover\n      .md-version:hover & {\n        animation: hoverfix 250ms forwards;\n      }\n\n      // Enable on focus\n      .md-version:focus-within & {\n        animation: none;\n      }\n    }\n  }\n\n  // Version selection item\n  &__item {\n    line-height: px2rem(36px);\n  }\n\n  // Version selection link\n  &__link {\n    display: block;\n    width: 100%;\n    padding-inline: px2rem(12px) px2rem(24px);\n    white-space: nowrap;\n    cursor: pointer;\n    scroll-snap-align: start;\n    outline: none;\n    transition:\n      color            250ms,\n      background-color 250ms;\n\n    // Link on focus/hover\n    &:is(:focus, :hover) {\n      color: var(--md-accent-fg-color);\n    }\n\n    // Link on focus\n    &:focus {\n      background-color: var(--md-default-fg-color--lightest);\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n@use \"sass:color\";\n@use \"sass:list\";\n\n// ----------------------------------------------------------------------------\n// Variables\n// ----------------------------------------------------------------------------\n\n/// Admonition flavours\n$admonitions: (\n  \"note\":     pencil-circle         $clr-blue-a200,\n  \"abstract\": clipboard-text        $clr-light-blue-a400,\n  \"info\":     information           $clr-cyan-a700,\n  \"tip\":      fire                  $clr-teal-a700,\n  \"success\":  check                 $clr-green-a700,\n  \"question\": help-circle           $clr-light-green-a700,\n  \"warning\":  alert                 $clr-orange-a400,\n  \"failure\":  close                 $clr-red-a200,\n  \"danger\":   lightning-bolt-circle $clr-red-a400,\n  \"bug\":      shield-bug            $clr-pink-a400,\n  \"example\":  test-tube             $clr-deep-purple-a200,\n  \"quote\":    format-quote-close    $clr-grey\n) !default;\n\n// ----------------------------------------------------------------------------\n// Rules: layout\n// ----------------------------------------------------------------------------\n\n// Admonition variables\n:root {\n  @each $name, $props in $admonitions {\n    --md-admonition-icon--#{$name}:\n      svg-load(\"material/#{list.nth($props, 1)}.svg\");\n  }\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Admonition - note that all styles also apply to details tags, which are\n  // rendered as collapsible admonitions with summary elements as titles.\n  .admonition {\n    display: flow-root;\n    padding: 0 px2rem(12px);\n    margin: px2em(20px, 12.8px) 0;\n    font-size: px2rem(12.8px);\n    color: var(--md-admonition-fg-color);\n    background-color: var(--md-admonition-bg-color);\n    border: px2rem(1.5px) solid $clr-blue-a200;\n    border-radius: px2rem(4px);\n    box-shadow: var(--md-shadow-z1);\n    transition: box-shadow 125ms;\n    page-break-inside: avoid;\n\n    // [print]: Omit shadow as it may lead to rendering errors\n    @media print {\n      box-shadow: none;\n    }\n\n    // Admonition on focus\n    &:focus-within {\n      box-shadow: 0 0 0 px2rem(4px) color.adjust($clr-blue-a200, $alpha: -0.9);\n    }\n\n    // Hack: Chrome exhibits a weird issue where it will set nested elements to\n    // content-box. Doesn't happen in other browsers, so looks like a bug.\n    > * {\n      box-sizing: border-box;\n    }\n\n    // Adjust vertical spacing for nested admonitions\n    .admonition {\n      margin-top: 1em;\n      margin-bottom: 1em;\n    }\n\n    // Adjust spacing for contained table wrappers\n    .md-typeset__scrollwrap {\n      margin: 1em px2rem(-12px);\n    }\n\n    // Adjust spacing for contained tables\n    .md-typeset__table {\n      padding: 0 px2rem(12px);\n    }\n\n    // Adjust spacing for single-child tabbed block container\n    > .tabbed-set:only-child {\n      margin-top: 0;\n    }\n\n    // Adjust spacing on last child\n    html & > :last-child {\n      margin-bottom: px2rem(12px);\n    }\n  }\n\n  // Admonition title\n  .admonition-title {\n    position: relative;\n    padding-block: px2rem(8px);\n    padding-inline: px2rem(40px) px2rem(12px);\n    margin-block: 0;\n    margin-inline: px2rem(-12px);\n    font-weight: 700;\n    background-color: color.adjust($clr-blue-a200, $alpha: -0.9);\n    border: none;\n    border-inline-start-width: px2rem(4px);\n    border-start-start-radius: px2rem(2px);\n    border-start-end-radius: px2rem(2px);\n\n    // Adjust spacing for title-only admonitions\n    html &:last-child {\n      margin-bottom: 0;\n    }\n\n    // Admonition icon\n    &::before {\n      position: absolute;\n      inset-inline-start: px2rem(12px);\n      top: px2em(10px);\n      width: px2rem(20px);\n      height: px2rem(20px);\n      content: \"\";\n      background-color: $clr-blue-a200;\n      mask-image: var(--md-admonition-icon--note);\n      mask-repeat: no-repeat;\n      mask-position: center;\n      mask-size: contain;\n    }\n\n    // Inline code block\n    code {\n      box-shadow: 0 0 0 px2rem(1px) var(--md-default-fg-color--lightest);\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: flavours\n// ----------------------------------------------------------------------------\n\n// Define admonition flavors\n@each $name, $props in $admonitions {\n  $tint: list.nth($props, 2);\n\n  // Admonition flavour\n  .md-typeset .admonition.#{$name} {\n    border-color: $tint;\n\n    // Admonition on focus\n    &:focus-within {\n      box-shadow: 0 0 0 px2rem(4px) color.adjust($tint, $alpha: -0.9);\n    }\n  }\n\n  // Admonition flavour title\n  .md-typeset .#{$name} > .admonition-title {\n    background-color: color.adjust($tint, $alpha: -0.9);\n\n    // Admonition icon\n    &::before {\n      background-color: $tint;\n      mask-image: var(--md-admonition-icon--#{$name});\n    }\n\n    // Details marker\n    &::after {\n      color: $tint;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Footnotes variables\n:root {\n  --md-footnotes-icon: svg-load(\"material/keyboard-return.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Footnote container\n  .footnote {\n    font-size: px2rem(12.8px);\n    color: var(--md-default-fg-color--light);\n\n    // Footnote list - omit left indentation\n    > ol {\n      margin-inline-start: 0;\n\n      // Footnote item - footnote items can contain lists, so we need to scope\n      // the spacing adjustments to the top-level footnote item.\n      > li {\n        transition: color 125ms;\n\n        // Darken color on target\n        &:target {\n          color: var(--md-default-fg-color);\n        }\n\n        // Show backreferences on footnote focus without transition\n        &:focus-within .footnote-backref {\n          opacity: 1;\n          transition: none;\n          transform: translateX(0);\n        }\n\n        // Show backreferences on footnote hover/target\n        &:is(:hover, :target) .footnote-backref {\n          opacity: 1;\n          transform: translateX(0);\n        }\n\n        // Adjust spacing on first child\n        > :first-child {\n          margin-top: 0;\n        }\n      }\n    }\n  }\n\n  // Footnote reference\n  .footnote-ref {\n    font-size: px2em(12px, 16px);\n    font-weight: 700;\n\n    // Hack: increase specificity to override default\n    html & {\n      outline-offset: px2rem(2px);\n    }\n  }\n\n  // Show outline for all devices\n  [id^=\"fnref:\"]:target > .footnote-ref {\n    outline: auto;\n  }\n\n  // Footnote backreference\n  .footnote-backref {\n    display: inline-block;\n    // Hack: omit Unicode arrow for replacement with icon\n    font-size: 0;\n    color: var(--md-typeset-a-color);\n    vertical-align: text-bottom;\n    opacity: 0;\n    transition:\n      color     250ms,\n      transform 250ms 250ms,\n      opacity   125ms 250ms;\n    transform: translateX(px2rem(5px));\n\n    // [print]: Show footnote backreferences\n    @media print {\n      color: var(--md-typeset-a-color);\n      opacity: 1;\n      transform: translateX(0);\n    }\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      transform: translateX(px2rem(-5px));\n    }\n\n    // Adjust color on hover\n    &:hover {\n      color: var(--md-accent-fg-color);\n    }\n\n    // Footnote backreference icon\n    &::before {\n      display: inline-block;\n      width: px2rem(16px);\n      height: px2rem(16px);\n      content: \"\";\n      background-color: currentcolor;\n      mask-image: var(--md-footnotes-icon);\n      mask-repeat: no-repeat;\n      mask-position: center;\n      mask-size: contain;\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n\n        // Flip icon vertically\n        svg {\n          transform: scaleX(-1);\n        }\n      }\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Headerlink\n  .headerlink {\n    display: inline-block;\n    margin-inline-start: px2rem(10px);\n    color: var(--md-default-fg-color--lighter);\n    opacity: 0;\n    transition:\n      color   250ms,\n      opacity 125ms;\n\n    // [print]: Hide headerlinks\n    @media print {\n      display: none;\n    }\n  }\n\n  // Show headerlinks on parent hover\n  :is(:hover, :target) > .headerlink,\n  .headerlink:focus {\n    opacity: 1;\n    transition:\n      color   250ms,\n      opacity 125ms;\n  }\n\n  // Adjust color on parent target or focus/hover\n  :target > .headerlink,\n  .headerlink:is(:focus, :hover) {\n    color: var(--md-accent-fg-color);\n  }\n\n  // Adjust scroll margin for all elements with `id` attributes\n  :target {\n    --md-scroll-margin: #{px2rem(48px + 24px)};\n    --md-scroll-offset: #{px2rem(0px)};\n    // Scroll margin is finally ready for prime time - before, we used a hack\n    // for anchor correction based on pseudo elements but those times are gone.\n    scroll-margin-top:\n      calc(\n        var(--md-scroll-margin) -\n        var(--md-scroll-offset)\n      );\n\n    // [screen +]: Sticky navigation tabs\n    @include break-from-device(screen) {\n\n      // Adjust scroll margin for sticky navigation tabs\n      .md-header--lifted ~ .md-container & {\n        --md-scroll-margin: #{px2rem(96px + 24px)};\n      }\n    }\n  }\n\n  // Adjust scroll offset for headlines of level 1-3\n  :is(h1, h2, h3):target {\n    --md-scroll-offset: #{px2rem(4px)};\n  }\n\n  // Adjust scroll offset for headlines of level 4\n  h4:target {\n    --md-scroll-offset: #{px2rem(3px)};\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Arithmatex container\n  div.arithmatex {\n    overflow: auto;\n\n    // [mobile -]: Align with body copy\n    @include break-to-device(mobile) {\n      margin: 0 px2rem(-16px);\n\n      // Arithmatex content\n      > * {\n        width: min-content;\n      }\n    }\n\n    // Arithmatex content\n    > * {\n      padding: 0 px2rem(16px);\n      margin-inline: auto !important; // stylelint-disable-line\n      touch-action: auto;\n\n      // MathJax container - see https://bit.ly/3HR8YJ5\n      mjx-container {\n        margin: 0 !important; // stylelint-disable-line\n      }\n    }\n\n    // Prevent horizontal overflow, as this element is not visible but still has\n    // a height, which might be a bug in MathJax - see https://t.ly/ckPiA\n    mjx-assistive-mml {\n      height: 0;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Deletion\n  del.critic {\n    background-color: var(--md-typeset-del-color);\n    box-decoration-break: clone;\n  }\n\n  // Addition\n  ins.critic {\n    background-color: var(--md-typeset-ins-color);\n    box-decoration-break: clone;\n  }\n\n  // Comment\n  .critic.comment {\n    color: var(--md-code-hl-comment-color);\n    box-decoration-break: clone;\n\n    // Comment opening mark\n    &::before {\n      content: \"/* \";\n    }\n\n    // Comment closing mark\n    &::after {\n      content: \" */\";\n    }\n  }\n\n  // Critic block\n  .critic.block {\n    display: block;\n    padding-inline: px2rem(16px);\n    margin: 1em 0;\n    overflow: auto;\n    box-shadow: none;\n\n    // Adjust spacing on first child\n    > :first-child {\n      margin-top: 0.5em;\n    }\n\n    // Adjust spacing on last child\n    > :last-child {\n      margin-bottom: 0.5em;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Details variables\n:root {\n  --md-details-icon: svg-load(\"material/chevron-right.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Details\n  details {\n    @extend .admonition;\n\n    display: flow-root;\n    padding-top: 0;\n    overflow: visible;\n\n    // Details title icon - rotate icon on transition to open state\n    &[open] > summary::after {\n      transform: rotate(90deg);\n    }\n\n    // Adjust spacing for details in closed state\n    &:not([open]) {\n      padding-bottom: 0;\n      box-shadow: none;\n\n      // Hack: we cannot set `overflow: hidden` on the `details` element (which\n      // is why we set it to `overflow: visible`, as the outline would not be\n      // visible when focusing. Therefore, we must set the border radius on the\n      // summary explicitly.\n      > summary {\n        border-radius: px2rem(2px);\n      }\n    }\n  }\n\n  // Details title\n  summary {\n    @extend .admonition-title;\n\n    display: block;\n    min-height: px2rem(20px);\n    padding-inline-end: px2rem(36px);\n    // Hack: Work around Firefox bug that renders a subpixel outline when\n    // rotating a mask image element - see https://t.ly/qA1s4\n    overflow: hidden;\n    cursor: pointer;\n    border-start-start-radius: px2rem(2px);\n    border-start-end-radius: px2rem(2px);\n\n    // Show outline for keyboard devices\n    &.focus-visible {\n      outline-color: var(--md-accent-fg-color);\n      outline-offset: px2rem(4px);\n    }\n\n    // Hide outline for pointer devices\n    &:not(.focus-visible) {\n      outline: none;\n      -webkit-tap-highlight-color: transparent;\n    }\n\n    // Details marker\n    &::after {\n      position: absolute;\n      inset-inline-end: px2rem(8px);\n      top: px2em(10px);\n      width: px2rem(20px);\n      height: px2rem(20px);\n      content: \"\";\n      background-color: currentcolor;\n      mask-image: var(--md-details-icon);\n      mask-repeat: no-repeat;\n      mask-position: center;\n      mask-size: contain;\n      transition: transform 250ms;\n      transform: rotate(0deg);\n\n      // Adjust for right-to-left languages\n      [dir=\"rtl\"] & {\n        transform: rotate(180deg);\n      }\n    }\n\n    // Hide native details marker - modern\n    &::marker {\n      display: none;\n    }\n\n    // Hide native details marker - legacy, must be split into a separate rule,\n    // so older browsers don't consider the selector list as invalid\n    &::-webkit-details-marker {\n      display: none;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Emoji and icon container\n  :is(.emojione, .twemoji, .gemoji) {\n    --md-icon-size: #{px2em(18px)};\n\n    display: inline-flex;\n    height: var(--md-icon-size);\n    vertical-align: text-top;\n\n    // Icon - inlined via mkdocs-material-extensions\n    svg {\n      width: var(--md-icon-size);\n      max-height: 100%;\n      fill: currentcolor;\n    }\n  }\n\n  // Icon with size modifier\n  :is(.lg, .xl, .xxl, .xxxl) {\n    vertical-align: text-bottom;\n  }\n\n  // Adjust icon alignment\n  .middle {\n    vertical-align: middle;\n  }\n\n  // Adjust icon size to 1.5x\n  .lg {\n    --md-icon-size: #{px2em(24px)};\n  }\n\n  // Adjust icon size to 2x\n  .xl {\n    --md-icon-size: #{px2em(36px)};\n  }\n\n  // Adjust icon size to 3x\n  .xxl {\n    --md-icon-size: #{px2em(48px)};\n  }\n\n  // Adjust icon size to 4x\n  .xxxl {\n    --md-icon-size: #{px2em(64px)};\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules: syntax highlighting\n// ----------------------------------------------------------------------------\n\n// Code block\n.highlight {\n\n  // .o  = Operator\n  // .ow = Operator, word\n  :is(.o, .ow) {\n    color: var(--md-code-hl-operator-color);\n  }\n\n  .p {  // Punctuation\n    color: var(--md-code-hl-punctuation-color);\n  }\n\n  // .cpf = Comment, preprocessor file\n  // .l   = Literal\n  // .s   = Literal, string\n  // .sb  = Literal, string backticks\n  // .sc  = Literal, string char\n  // .s2  = Literal, string double\n  // .si  = Literal, string interpol\n  // .s1  = Literal, string single\n  // .ss  = Literal, string symbol\n  :is(.cpf, .l, .s, .sb, .sc, .s2, .si, .s1, .ss) {\n    color: var(--md-code-hl-string-color);\n  }\n\n  // .cp = Comment, pre-processor\n  // .se = Literal, string escape\n  // .sh = Literal, string heredoc\n  // .sr = Literal, string regex\n  // .sx = Literal, string other\n  :is(.cp, .se, .sh, .sr, .sx) {\n    color: var(--md-code-hl-special-color);\n  }\n\n  // .m  = Number\n  // .mb = Number, binary\n  // .mf = Number, float\n  // .mh = Number, hex\n  // .mi = Number, integer\n  // .il = Number, integer long\n  // .mo = Number, octal\n  :is(.m, .mb, .mf, .mh, .mi, .il, .mo) {\n    color: var(--md-code-hl-number-color);\n  }\n\n  // .k  = Keyword,\n  // .kd = Keyword, declaration\n  // .kn = Keyword, namespace\n  // .kp = Keyword, pseudo\n  // .kr = Keyword, reserved\n  // .kt = Keyword, type\n  :is(.k, .kd, .kn, .kp, .kr, .kt) {\n    color: var(--md-code-hl-keyword-color);\n  }\n\n  // .n  = Name\n  :is(.n) {\n    color: var(--md-code-hl-name-color);\n  }\n\n  // .kc = Keyword, constant\n  // .no = Name, constant\n  // .nb = Name, builtin\n  // .bp = Name, builtin pseudo\n  :is(.kc, .no, .nb, .bp) {\n    color: var(--md-code-hl-constant-color);\n  }\n\n  // .nc = Name, class\n  // .ne = Name, exception\n  // .nf = Name, function\n  // .nn = Name, namespace\n  :is(.nc, .ne, .nf, .nn) {\n    color: var(--md-code-hl-function-color);\n  }\n\n  // .nd = Name, decorator\n  // .ni = Name, entity\n  // .nl = Name, label\n  // .nt = Name, tag\n  :is(.nd, .ni, .nl, .nt) {\n    color: var(--md-code-hl-keyword-color);\n  }\n\n  // .c  = Comment\n  // .cm = Comment, multiline\n  // .c1 = Comment, single\n  // .ch = Comment, shebang\n  // .cs = Comment, special\n  // .sd = Literal, string doc\n  :is(.c, .cm, .c1, .ch, .cs, .sd) {\n    color: var(--md-code-hl-comment-color);\n  }\n\n  // .na = Name, attribute\n  // .nv = Variable,\n  // .vc = Variable, class\n  // .vg = Variable, global\n  // .vi = Variable, instance\n  :is(.na, .nv, .vc, .vg, .vi) {\n    color: var(--md-code-hl-variable-color);\n  }\n\n  // .ge = Generic, emph\n  // .gr = Generic, error\n  // .gh = Generic, heading\n  // .go = Generic, output\n  // .gp = Generic, prompt\n  // .gs = Generic, strong\n  // .gu = Generic, subheading\n  // .gt = Generic, traceback\n  :is(.ge, .gr, .gh, .go, .gp, .gs, .gu, .gt) {\n    color: var(--md-code-hl-generic-color);\n  }\n\n  // .gd = Diff, delete\n  // .gi = Diff, insert\n  :is(.gd, .gi) {\n    padding: 0 px2em(2px);\n    margin: 0 px2em(-2px);\n    border-radius: px2rem(2px);\n  }\n\n  .gd { // Diff, delete\n    background-color: var(--md-typeset-del-color);\n  }\n\n  .gi { // Diff, insert\n    background-color: var(--md-typeset-ins-color);\n  }\n\n  // Highlighted line\n  .hll {\n    display: block;\n    padding: 0 px2em(16px, 13.6px);\n    margin: 0 px2em(-16px, 13.6px);\n    background-color: var(--md-code-hl-color--light);\n    box-shadow: 2px 0 0 0 var(--md-code-hl-color) inset;\n  }\n\n  // Code block title\n  span.filename {\n    position: relative;\n    display: flow-root;\n    padding: px2em(9px, 13.6px) px2em(16px, 13.6px);\n    margin-top: 1em;\n    font-size: px2em(13.6px);\n    font-weight: 700;\n    background-color: var(--md-code-bg-color);\n    border-bottom: px2rem(1px) solid var(--md-default-fg-color--lightest);\n    border-top-left-radius: px2rem(2px);\n    border-top-right-radius: px2rem(2px);\n\n    // Adjust spacing for code block\n    + pre {\n      margin-top: 0;\n\n      // Remove rounded border on top side\n      > code {\n        border-top-left-radius: 0;\n        border-top-right-radius: 0;\n      }\n    }\n  }\n\n  // Code block line numbers (pymdownx-inline)\n  [data-linenos]::before {\n    position: sticky;\n    left: px2em(-16px, 13.6px);\n    // A `z-index` of 3 is necessary for ensuring that code block annotations\n    // don't overlay line numbers, as active annotations have a `z-index` of 2.\n    z-index: 3;\n    float: left;\n    padding-left: px2em(16px, 13.6px);\n    margin-right: px2em(16px, 13.6px);\n    margin-left: px2em(-16px, 13.6px);\n    color: var(--md-default-fg-color--light);\n    content: attr(data-linenos);\n    user-select: none;\n    background-color: var(--md-code-bg-color);\n    box-shadow: px2rem(-1px) 0 var(--md-default-fg-color--lightest) inset;\n  }\n\n  // Code block line anchors - Chrome and Safari seem to have a strange bug\n  // where scroll margin is not applied to anchors inside code blocks. Setting\n  // positioning to absolute seems to fix the problem. Interestingly, this does\n  // not happen in Firefox. Furthermore we must set `visibility: hidden` or\n  // the copy to clipboard functionality will include an empty line between\n  // each set of lines.\n  code a[id] {\n    position: absolute;\n    visibility: hidden;\n  }\n\n  // Copying in progress - this class is set before the content is copied and\n  // removed after copying is done to mitigate whitespace-related issues.\n  code[data-md-copying] {\n    // Hack: since we're using grid layout when line spans are enabled, we need\n    // to set the display property to `initial` to prevent the grid layout from\n    // being applied to the code block when copying, because it will add empty\n    // lines to the copied content - see https://t.ly/wt4ye\n    display: initial;\n\n    // Temporarily remove highlighted lines - see https://bit.ly/32iVGWh\n    .hll {\n      display: contents;\n    }\n\n    // Temporarily remove annotations\n    .md-annotation {\n      display: none;\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: layout\n// ----------------------------------------------------------------------------\n\n// Code block with line numbers\n.highlighttable {\n  display: flow-root;\n\n  // Set table elements to block layout, because otherwise the whole flexbox\n  // hacking won't work correctly\n  :is(tbody, td) {\n    display: block;\n    padding: 0;\n  }\n\n  // We need to use flexbox layout, because otherwise it's not possible to\n  // make the code container scroll while keeping the line numbers static\n  tr {\n    display: flex;\n  }\n\n  // The pre tags are nested inside a table, so we need to omit the margin\n  // because it collapses below all the overflows\n  pre {\n    margin: 0;\n  }\n\n  // Code block title container\n  th.filename {\n    flex-grow: 1;\n    padding: 0;\n    text-align: left;\n\n    // Adjust spacing\n    span.filename {\n      margin-top: 0;\n    }\n  }\n\n  // Code block line numbers - disable user selection, so code can be easily\n  // copied without accidentally also copying the line numbers\n  .linenos {\n    padding: px2em(10.5px, 13.6px) px2em(16px, 13.6px);\n    padding-right: 0;\n    font-size: px2em(13.6px);\n    user-select: none;\n    background-color: var(--md-code-bg-color);\n    border-top-left-radius: px2rem(2px);\n    border-bottom-left-radius: px2rem(2px);\n  }\n\n  // Code block line numbers container\n  .linenodiv {\n    padding-right: px2em(8px, 13.6px);\n    box-shadow: px2rem(-1px) 0 var(--md-default-fg-color--lightest) inset;\n\n    // Adjust colors and alignment\n    pre {\n      color: var(--md-default-fg-color--light);\n      text-align: right;\n    }\n  }\n\n  // Code block container - stretch to remaining space\n  .code {\n    flex: 1;\n    min-width: 0;\n  }\n}\n\n// Code block line numbers container\n.linenodiv a {\n  color: inherit;\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Code block with line numbers - unfortunately, these selectors need to be\n  // overly specific so they don't bleed into code blocks in annotations.\n  .highlighttable {\n    margin: 1em 0;\n    direction: ltr;\n\n    // Remove rounded borders on code blocks\n    > tbody > tr > .code > div > pre > code {\n      border-top-left-radius: 0;\n      border-bottom-left-radius: 0;\n    }\n  }\n\n  // Code block result container\n  // sphinx-immaterial: altered to support\n  // 1. docutils classes (.literal-block-wrapper, div[class^=\"highlight-\"])\n  // 2. rst-result directive's output-prefix (.results-prefix)\n  :is(.highlight, .highlighttable, .literal-block-wrapper, div[class^=\"highlight-\"], .results-prefix) + .result {\n    padding: 0 px2em(16px);\n    margin-top: calc(-1em + #{px2em(-2px)});\n    overflow: visible;\n    border: px2rem(1px) solid var(--md-code-bg-color);\n    border-top-width: px2rem(2px);\n    border-bottom-right-radius: px2rem(2px);\n    border-bottom-left-radius: px2rem(2px);\n\n    // Clearfix, because we can't use overflow: auto\n    &::after {\n      display: block;\n      clear: both;\n      content: \"\";\n    }\n  }\n\n  // sphinx-immaterial: reset margin for rst-result directive's output-prefix\n  .results .results-prefix + .result {\n    margin-top: 0;\n  }\n\n  // sphinx-immaterial: use modified style from span.filename (see above)\n  .results .results-prefix {\n    padding: px2em(9px, 13.6px) px2em(16px, 13.6px);\n    margin-top: -1em;\n    font-size: px2em(13.6px);\n    font-weight: 700;\n    background-color: var(--md-code-bg-color);\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: top-level\n// ----------------------------------------------------------------------------\n\n// [mobile -]: Align with body copy\n@include break-to-device(mobile) {\n\n  // Top-level code block\n  .md-content__inner > .highlight {\n    margin: 1em px2rem(-16px);\n\n    // Remove rounded borders\n    > .filename,\n    > pre > code {\n      border-radius: 0;\n    }\n\n    // Code block with line numbers - unfortunately, these selectors need to be\n    // overly specific so they don't bleed into code blocks in annotations.\n    > .highlighttable > tbody > tr > .filename span.filename,\n    > .highlighttable > tbody > tr > .linenos,\n    > .highlighttable > tbody > tr > .code > div > pre > code {\n      border-radius: 0;\n    }\n\n    // Code block result container\n    + .result {\n      margin-inline: px2rem(-16px);\n      border-inline-width: 0;\n      border-radius: 0;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Keyboard key\n  .keys {\n\n    // Keyboard key icon\n    kbd:is(::before, ::after) {\n      position: relative;\n      margin: 0;\n      color: inherit;\n      -moz-osx-font-smoothing: initial;\n      -webkit-font-smoothing: initial;\n    }\n\n    // Surrounding text\n    span {\n      padding: 0 px2em(3.2px);\n      color: var(--md-default-fg-color--light);\n    }\n\n    // Define keyboard keys with left icon\n    @each $name, $code in (\n\n      // Modifiers\n      \"alt\":           \"\\2387\",\n      \"left-alt\":      \"\\2387\",\n      \"right-alt\":     \"\\2387\",\n      \"command\":       \"\\2318\",\n      \"left-command\":  \"\\2318\",\n      \"right-command\": \"\\2318\",\n      \"control\":       \"\\2303\",\n      \"left-control\":  \"\\2303\",\n      \"right-control\": \"\\2303\",\n      \"meta\":          \"\\25C6\",\n      \"left-meta\":     \"\\25C6\",\n      \"right-meta\":    \"\\25C6\",\n      \"option\":        \"\\2325\",\n      \"left-option\":   \"\\2325\",\n      \"right-option\":  \"\\2325\",\n      \"shift\":         \"\\21E7\",\n      \"left-shift\":    \"\\21E7\",\n      \"right-shift\":   \"\\21E7\",\n      \"super\":         \"\\2756\",\n      \"left-super\":    \"\\2756\",\n      \"right-super\":   \"\\2756\",\n      \"windows\":       \"\\229E\",\n      \"left-windows\":  \"\\229E\",\n      \"right-windows\": \"\\229E\",\n\n      // Other keys\n      \"arrow-down\":    \"\\2193\",\n      \"arrow-left\":    \"\\2190\",\n      \"arrow-right\":   \"\\2192\",\n      \"arrow-up\":      \"\\2191\",\n      \"backspace\":     \"\\232B\",\n      \"backtab\":       \"\\21E4\",\n      \"caps-lock\":     \"\\21EA\",\n      \"clear\":         \"\\2327\",\n      \"context-menu\":  \"\\2630\",\n      \"delete\":        \"\\2326\",\n      \"eject\":         \"\\23CF\",\n      \"end\":           \"\\2913\",\n      \"escape\":        \"\\238B\",\n      \"home\":          \"\\2912\",\n      \"insert\":        \"\\2380\",\n      \"page-down\":     \"\\21DF\",\n      \"page-up\":       \"\\21DE\",\n      \"print-screen\":  \"\\2399\"\n    ) {\n      .key-#{$name}::before {\n        padding-right: px2em(6.4px);\n        content: $code;\n      }\n    }\n\n    // Define keyboard keys with right icon\n    @each $name, $code in (\n      \"tab\":           \"\\21E5\",\n      \"num-enter\":     \"\\2324\",\n      \"enter\":         \"\\23CE\"\n    ) {\n      .key-#{$name}::after {\n        padding-left: px2em(6.4px);\n        content: $code;\n      }\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Tabbed variables\n:root {\n  --md-tabbed-icon--prev: svg-load(\"material/chevron-left.svg\");\n  --md-tabbed-icon--next: svg-load(\"material/chevron-right.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Tabbed container\n  .tabbed-set {\n    position: relative;\n    display: flex;\n    flex-flow: column wrap;\n    margin: 1em 0;\n    border-radius: px2rem(2px);\n\n    // Tab radio button - the Tabbed extension will generate radio buttons with\n    // labels, so tabs can be triggered without the necessity for JavaScript.\n    // This is pretty cool, as it has great accessibility out-of-the box, so\n    // we just hide the radio button and toggle the label color for indication.\n    > input {\n      position: absolute;\n      width: 0;\n      height: 0;\n      opacity: 0;\n\n      // Adjust scroll margin\n      &:target {\n        --md-scroll-offset: #{px2em(10px, 16px)};\n      }\n\n      // Tab label states\n      @for $i from 20 through 1 {\n        &:nth-child(#{$i}) {\n\n          // Tab is active\n          &:checked {\n\n            // Tab label\n            ~ .tabbed-labels > :nth-child(#{$i}) {\n              @extend %tabbed-label;\n            }\n\n            // Tab content\n            ~ .tabbed-content > :nth-child(#{$i}) {\n              @extend %tabbed-content;\n            }\n          }\n\n          // Tab label on keyboard focus\n          &.focus-visible ~ .tabbed-labels > :nth-child(#{$i}) {\n            @extend %tabbed-label-focus-visible;\n          }\n        }\n      }\n\n      // Tab indicator on keyboard focus\n      &.focus-visible ~ .tabbed-labels::before {\n        background-color: var(--md-accent-fg-color);\n      }\n    }\n  }\n\n  // Tabbed labels\n  .tabbed-labels {\n    display: flex;\n    max-width: 100%;\n    overflow: auto;\n    scrollbar-width: none;             // Firefox\n    box-shadow: 0 px2rem(-1px) var(--md-default-fg-color--lightest) inset;\n    -ms-overflow-style: none;          // IE, Edge\n\n    // [print]: Move one layer up for ordering\n    @media print {\n      display: contents;\n    }\n\n    // [screen and no reduced motion]: Disable animation\n    @media screen {\n\n      // [js]: Show animated tab indicator\n      .js & {\n        position: relative;\n\n        // Tab indicator\n        &::before {\n          position: absolute;\n          bottom: 0;\n          left: 0;\n          display: block;\n          width: var(--md-indicator-width);\n          height: 2px;\n          content: \"\";\n          background: var(--md-default-fg-color);\n          transition:\n            width            225ms,\n            background-color 250ms,\n            transform        250ms;\n          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n          transform: translateX(var(--md-indicator-x));\n        }\n      }\n    }\n\n    // Webkit scrollbar\n    &::-webkit-scrollbar {\n      display: none;                   // Chrome, Safari\n    }\n\n    // Tab label\n    > label {\n      flex-shrink: 0;\n      width: auto;\n      padding: px2em(10px, 12.8px) 1.25em px2em(8px, 12.8px);\n      font-size: px2rem(12.8px);\n      font-weight: 700;\n      color: var(--md-default-fg-color--light);\n      white-space: nowrap;\n      cursor: pointer;\n      scroll-margin-inline-start: px2rem(20px);\n      border-bottom: px2rem(2px) solid transparent;\n      border-radius: px2rem(2px) px2rem(2px) 0 0;\n      transition:\n        background-color 250ms,\n        color            250ms;\n\n      // [print]: Intersperse labels with containers\n      @media print {\n\n        // Ensure correct order of labels\n        @for $i from 1 through 20 {\n          &:nth-child(#{$i}) {\n            order: $i;\n          }\n        }\n      }\n\n      // Tab label on hover\n      &:hover {\n        color: var(--md-default-fg-color);\n      }\n\n      // Tab label anchor link\n      > [href]:first-child {\n        color: inherit;\n      }\n    }\n\n    // Tab label with anchor link\n    &--linked > label {\n      padding: 0;\n\n      // Move padding one level down to anchor link, so the whole tab area\n      // becomes clickable, not only the text.\n      > a {\n        display: block;\n        padding: px2em(10px, 12.8px) 1.25em px2em(8px, 12.8px);\n      }\n    }\n  }\n\n  // Tabbed content\n  .tabbed-content {\n    width: 100%;\n\n    // [print]: Move one layer up for ordering\n    @media print {\n      display: contents;\n    }\n  }\n\n  // Tabbed block\n  .tabbed-block {\n    display: none;\n\n    // [print]: Intersperse labels with containers\n    @media print {\n      display: block;\n\n      // Ensure correct order of containers\n      @for $i from 1 through 20 {\n        &:nth-child(#{$i}) {\n          order: $i;\n        }\n      }\n    }\n\n    // Code block is the first child of a tab - remove margin and mirror\n    // previous (now deprecated) SuperFences code block grouping behavior\n    > pre:first-child,\n    > .highlight:first-child > pre {\n      margin: 0;\n\n      // Remove rounded borders on code block\n      > code {\n        border-top-left-radius: 0;\n        border-top-right-radius: 0;\n      }\n    }\n\n    // Code block is the first child of a tab - remove margin and mirror\n    // previous (now deprecated) SuperFences code block grouping behavior\n    > .highlight:first-child {\n\n      // Code block title - remove spacing and rounded borders\n      > .filename {\n        margin: 0;\n        border-top-left-radius: 0;\n        border-top-right-radius: 0;\n      }\n\n      // Code block with line numbers - unfortunately, these selectors need to\n      // be overly specific so they don't bleed into code blocks in annotations.\n      > .highlighttable {\n        margin: 0;\n\n        // Remove rounded borders on line numbers and titles\n        > tbody > tr > .filename span.filename,\n        > tbody > tr > .linenos {\n          margin: 0;\n          border-top-left-radius: 0;\n          border-top-right-radius: 0;\n        }\n\n        // Remove rounded borders on code blocks\n        > tbody > tr > .code > div > pre > code {\n          border-top-left-radius: 0;\n          border-top-right-radius: 0;\n        }\n      }\n\n      // Code block result container - adjust spacing\n      + .result {\n        margin-top: px2em(-2px);\n      }\n    }\n\n    // Adjust spacing for nested tabbed container\n    > .tabbed-set {\n      margin: 0;\n    }\n  }\n\n  // Tabbed button\n  .tabbed-button {\n    display: block;\n    align-self: center;\n    width: px2rem(18px);\n    height: px2rem(18px);\n    margin-top: px2rem(2px);\n    color: var(--md-default-fg-color--light);\n    pointer-events: initial;\n    cursor: pointer;\n    border-radius: 100%;\n    transition: background-color 250ms;\n\n    // Tabbed button on hover\n    &:hover {\n      color: var(--md-accent-fg-color);\n      background-color: var(--md-accent-fg-color--transparent);\n    }\n\n    // Tabbed button icon\n    &::after {\n      display: block;\n      width: 100%;\n      height: 100%;\n      content: \"\";\n      background-color: currentcolor;\n      mask-image: var(--md-tabbed-icon--prev);\n      mask-repeat: no-repeat;\n      mask-position: center;\n      mask-size: contain;\n      transition:\n        background-color 250ms,\n        transform        250ms;\n    }\n  }\n\n  // Tabbed control\n  .tabbed-control {\n    position: absolute;\n    display: flex;\n    justify-content: start;\n    width: px2rem(24px);\n    height: px2rem(38px);\n    pointer-events: none;\n    background:\n      linear-gradient(\n        to right,\n        var(--md-default-bg-color) 60%,\n        transparent\n      );\n    transition: opacity 125ms;\n\n    // Adjust for right-to-left languages\n    [dir=\"rtl\"] & {\n      transform: rotate(180deg);\n    }\n\n    // Tabbed control is hidden\n    &[hidden] {\n      opacity: 0;\n    }\n\n    // Tabbed control next\n    &--next {\n      right: 0;\n      justify-content: end;\n      background:\n        linear-gradient(\n          to left,\n          var(--md-default-bg-color) 60%,\n          transparent\n        );\n\n      // Tabbed button icon content\n      .tabbed-button::after {\n        mask-image: var(--md-tabbed-icon--next);\n      }\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: top-level\n// ----------------------------------------------------------------------------\n\n// [mobile -]: Align with body copy\n@include break-to-device(mobile) {\n\n  // Top-level tabbed labels\n  .md-content__inner > .tabbed-set .tabbed-labels {\n    max-width: 100vw;\n    padding-inline-start: px2rem(16px);\n    margin: 0 px2rem(-16px);\n    scroll-padding-inline-start: px2rem(16px);\n\n    // Hack: some browsers ignore the right padding on flex containers,\n    // see https://bit.ly/3lsPS3S\n    &::after {\n      padding-inline-end: px2rem(16px);\n      content: \"\";\n    }\n\n    // Tabbed control previous\n    ~ .tabbed-control--prev {\n      width: px2rem(40px);\n      padding-inline-start: px2rem(16px);\n      margin-inline-start: px2rem(-16px);\n    }\n\n    // Tabbed control next\n    ~ .tabbed-control--next {\n      width: px2rem(40px);\n      padding-inline-end: px2rem(16px);\n      margin-inline-end: px2rem(-16px);\n    }\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Placeholders: improve colocation for better compression\n// ----------------------------------------------------------------------------\n\n// Tab label placeholder\n%tabbed-label {\n\n  // [screen]: Show active state\n  @media screen {\n    color: var(--md-default-fg-color);\n\n    // [no-js]: Show border (indicator is animated with JavaScript)\n    .no-js & {\n      border-color: var(--md-default-fg-color);\n    }\n  }\n}\n\n// Tab label on keyboard focus placeholder\n%tabbed-label-focus-visible {\n  color: var(--md-accent-fg-color);\n}\n\n// Tab content placeholder\n%tabbed-content {\n  display: block;\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Tasklist variables\n:root {\n  --md-tasklist-icon: svg-load(\"octicons/check-circle-fill-24.svg\");\n  --md-tasklist-icon--checked: svg-load(\"octicons/check-circle-fill-24.svg\");\n}\n\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Tasklist item\n  .task-list-item {\n    position: relative;\n    list-style-type: none;\n\n    // Make checkbox items align with normal list items, but position\n    // everything in ems for correct layout at smaller font sizes\n    [type=\"checkbox\"] {\n      position: absolute;\n      inset-inline-start: -2em;\n      top: 0.45em;\n    }\n  }\n\n  // Hide native checkbox, when custom classes are enabled\n  .task-list-control [type=\"checkbox\"] {\n    z-index: -1;\n    opacity: 0;\n  }\n\n  // Tasklist indicator in unchecked state\n  .task-list-indicator::before {\n    position: absolute;\n    inset-inline-start: px2em(-24px);\n    top: 0.15em;\n    width: px2em(20px);\n    height: px2em(20px);\n    content: \"\";\n    background-color: var(--md-default-fg-color--lightest);\n    mask-image: var(--md-tasklist-icon);\n    mask-repeat: no-repeat;\n    mask-position: center;\n    mask-size: contain;\n  }\n\n  // Tasklist indicator in checked state\n  [type=\"checkbox\"]:checked + .task-list-indicator::before {\n    background-color: $clr-green-a400;\n    mask-image: var(--md-tasklist-icon--checked);\n  }\n}\n", "// css overrides for the readthedocs-sphinx-ext that\n// is automatically appended to projects built/hosted on RTD\n\n.rst-versions {\n  font-family: var(--md-text-font-family);\n\n  &.rst-badge {\n    top: 50px;\n    // stylelint-disable-next-line declaration-no-important\n    bottom: inherit !important;\n    height: auto;\n    font-size: 0.85rem;\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// [print]: Hide comment section\n@media print {\n\n  // Comments headline\n  [id=\"__comments\"] {\n    display: none;\n  }\n\n  // Comments integration\n  .giscus {\n    display: none;\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Mermaid variables\n:root > * {\n  --md-mermaid-font-family:            var(--md-text-font-family), sans-serif;\n\n  // General colors\n  --md-mermaid-edge-color:             var(--md-code-fg-color);\n  --md-mermaid-node-bg-color:          var(--md-accent-fg-color--transparent);\n  --md-mermaid-node-fg-color:          var(--md-accent-fg-color);\n  --md-mermaid-label-bg-color:         var(--md-default-bg-color);\n  --md-mermaid-label-fg-color:         var(--md-code-fg-color);\n\n  // Sequence diagram colors\n  --md-mermaid-sequence-actor-bg-color:      var(--md-mermaid-label-bg-color);\n  --md-mermaid-sequence-actor-fg-color:      var(--md-mermaid-label-fg-color);\n  --md-mermaid-sequence-actor-border-color:  var(--md-mermaid-node-fg-color);\n  --md-mermaid-sequence-actor-line-color:    var(--md-default-fg-color--lighter);\n  --md-mermaid-sequence-actorman-bg-color:   var(--md-mermaid-label-bg-color);\n  --md-mermaid-sequence-actorman-line-color: var(--md-mermaid-node-fg-color);\n  --md-mermaid-sequence-box-bg-color:        var(--md-mermaid-node-bg-color);\n  --md-mermaid-sequence-box-fg-color:        var(--md-mermaid-edge-color);\n  --md-mermaid-sequence-label-bg-color:      var(--md-mermaid-node-bg-color);\n  --md-mermaid-sequence-label-fg-color:      var(--md-mermaid-node-fg-color);\n  --md-mermaid-sequence-loop-bg-color:       var(--md-mermaid-node-bg-color);\n  --md-mermaid-sequence-loop-fg-color:       var(--md-mermaid-edge-color);\n  --md-mermaid-sequence-loop-border-color:   var(--md-mermaid-node-fg-color);\n  --md-mermaid-sequence-message-fg-color:    var(--md-mermaid-edge-color);\n  --md-mermaid-sequence-message-line-color:  var(--md-mermaid-edge-color);\n  --md-mermaid-sequence-note-bg-color:       var(--md-mermaid-label-bg-color);\n  --md-mermaid-sequence-note-fg-color:       var(--md-mermaid-edge-color);\n  --md-mermaid-sequence-note-border-color:   var(--md-mermaid-label-fg-color);\n  --md-mermaid-sequence-number-bg-color:     var(--md-mermaid-node-fg-color);\n  --md-mermaid-sequence-number-fg-color:     var(--md-accent-bg-color);\n}\n\n// ----------------------------------------------------------------------------\n\n// Mermaid container\n.mermaid {\n  margin: 1em 0;\n  line-height: normal;\n}\n", "// All definitions\n:root > * {\n  // Colors\n  --md-graphviz-edge-color:             var(--md-default-fg-color);\n  --md-graphviz-node-bg-color:          var(--md-accent-fg-color--transparent);\n  --md-graphviz-node-fg-color:          var(--md-accent-fg-color);\n  --md-graphviz-label-bg-color:         var(--md-default-bg-color);\n  --md-graphviz-label-fg-color:         var(--md-code-fg-color);\n  --md-graphviz-a-hover-color:          var(--md-primary-fg-color);\n}\n\n// Mermaid container\n.graphviz {\n  margin: 1em 0;\n}\n\n.graphviz a:hover > text {\n  fill: var(--md-graphviz-hover-color) !important; // stylelint-disable-line declaration-no-important\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // Grid container\n  .grid {\n    display: grid;\n    grid-template-columns:\n      repeat(\n        auto-fit,\n        minmax(\n          min(100%, #{px2rem(320px)}),\n          1fr\n        )\n      );\n    grid-gap: px2rem(8px);\n    margin: 1em 0;\n\n    // Grid card container - if all grid items should render as cards, the\n    // `.cards` class can be added, which moves list items up one level.\n    &.cards > :is(ul, ol) {\n      display: contents;\n    }\n\n    // Grid card - a card is either a list item of a grid container with the\n    // `.cards` class or a single element with the `.card` class, which allows\n    // to align cards with other components (admonitions, tabs, ...) in grids.\n    &.cards > :is(ul, ol) > li,\n    > .card {\n      display: block;\n      padding: px2rem(16px);\n      margin: 0;\n      border: px2rem(1px) solid var(--md-default-fg-color--lightest);\n      border-radius: px2rem(2px);\n      transition:\n        border     250ms,\n        box-shadow 250ms;\n\n      // Grid list item on focus/hover\n      &:is(:focus-within, :hover) {\n        border-color: transparent;\n        box-shadow: var(--md-shadow-z2);\n      }\n\n      // Adjust spacing for horizontal separators\n      > hr {\n        margin-block: 1em;\n      }\n\n      // Adjust spacing on first child\n      > :first-child {\n        margin-top: 0;\n      }\n\n      // Adjust spacing on last child\n      > :last-child {\n        margin-bottom: 0;\n      }\n    }\n\n    // Grid item\n    > * {\n      margin-block: 0;\n    }\n\n    // Grid item: admonition\n    > :is(.admonition, details) {\n      margin-block: 0;\n    }\n\n    // Grid item: code block\n    > pre,\n    > .highlight > *,\n    > .highlighttable {\n      margin-block: 0;\n    }\n\n    // Grid item: code block without line numbers - stretch to match height\n    // of containing grid item, which must be done explicitly.\n    > .highlight > pre:only-child,\n    > .highlight > pre > code {\n      height: 100%;\n    }\n\n    // Grid item: code block with line numbers - stretch to match height of\n    // containing grid item, which is even uglier than the rule before. However,\n    // it's not possible to achieve this behavior without explicitly setting the\n    // height on each and every element as we do here.\n    > .highlighttable,\n    > .highlighttable > tbody,\n    > .highlighttable > tbody > tr,\n    > .highlighttable > tbody > tr > .code,\n    > .highlighttable > tbody > tr > .code > .highlight,\n    > .highlighttable > tbody > tr > .code > .highlight > pre,\n    > .highlighttable > tbody > tr > .code > .highlight > pre > code {\n      height: 100%;\n    }\n\n    // Grid item: tabbed container\n    > .tabbed-set {\n      margin-block: 0;\n    }\n  }\n}\n", "////\n/// Copyright (c) 2016-2025 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Rules\n// ----------------------------------------------------------------------------\n\n// Scoped in typesetted content to match specificity of regular content\n.md-typeset {\n\n  // [tablet +]: Allow for rendering content as sidebars\n  @include break-from-device(tablet) {\n\n    // Modifier to float block elements\n    .inline {\n      float: inline-start;\n      width: px2rem(234px);\n      margin-inline-end: px2rem(16px);\n      margin-top: 0;\n      margin-bottom: px2rem(16px);\n\n      // Modifier to move to end (ltr: right, rtl: left)\n      &.end {\n        float: inline-end;\n        margin-inline: px2rem(16px) 0;\n      }\n    }\n  }\n}\n", "// Additional styles expected by sphinx.\n\n.md-typeset {\n\n  // alignment of text and inline objects inside block objects\n  .align-left {\n    text-align: left;\n  }\n\n  .align-right {\n    text-align: right;\n  }\n\n  .align-center {\n    clear: both;\n    text-align: center;\n  }\n\n  .align-top {\n    vertical-align: top;\n  }\n\n  .align-middle {\n    vertical-align: middle;\n  }\n\n  .align-bottom {\n    vertical-align: bottom;\n  }\n\n  // Figures, Images, and Tables\n  .figure.align-left,\n  figure.align-left,\n  img.align-left,\n  object.align-left,\n  table.align-left {\n    margin-right: auto;\n  }\n\n  .figure.align-center,\n  figure.align-center,\n  img.align-center,\n  object.align-center,\n  table.align-center {\n    margin-right: auto;\n    margin-left: auto;\n  }\n\n  .figure.align-right,\n  figure.align-right,\n  img.align-right,\n  object.align-right,\n  table.align-right {\n    margin-left: auto;\n  }\n\n  .figure.align-center,\n  .figure.align-right,\n  figure.align-center,\n  figure.align-right,\n  img.align-center,\n  img.align-right,\n  object.align-center,\n  object.align-right {\n    display: block;\n  }\n\n  // reset inner alignment in figures and tables\n  .figure.align-left,\n  .figure.align-right,\n  figure.align-left,\n  figure.align-right,\n  table.align-left,\n  table.align-center,\n  table.align-right {\n    text-align: inherit;\n  }\n\n  .rubric {\n    font-weight: 700;\n  }\n\n  .viewcode-block .viewcode-back {\n    float: right;\n  }\n\n  .versionmodified {\n    font-style: italic;\n  }\n\n  // Indentation of line blocks\n  div.line-block {\n    display: block;\n  }\n\n  div.line-block div.line-block {\n    margin-left: 1.5em;\n  }\n\n  // display of footnotes and citations\n  aside.footnote,\n  div.citation {\n    display: grid;\n    grid-auto-columns: minmax(auto, max-content);\n\n    > span {\n      &.label {\n        grid-column: 1;\n      }\n\n      &.backrefs {\n        grid-column: 2;\n      }\n\n      &:last-of-type {\n        padding-right: 0.5em;\n      }\n    }\n\n    > :not(span.backrefs, span.label) {\n      grid-column: 3;\n\n      &:first-of-type {\n        margin-top: 0;\n      }\n\n      &:last-child {\n        margin-bottom: 0;\n\n        &::after {\n          clear: both;\n          content: \"\";\n        }\n      }\n    }\n  }\n}\n", "$api-color-header-bg: var(--md-code-bg-color);\n$api-color-keyword: var(--md-code-hl-keyword-color);\n$api-color-name: var(--md-code-hl-name-color);\n$api-color-pre-name: var(--md-code-hl-name-color);\n$api-color-type: var(--md-code-hl-special-color);\n$api-color-param-name: var(--md-default-fg-color--light);\n$objinfo-icon-size: 16px;\n\n// Wrap in .md-typeset to ensure rules below have higher selectivity\n// than those in _typeset.scss.\n.md-typeset {\n  :is(dl.objdesc, dl.api-field) > dt {\n    font-family: var(--md-code-font-family);\n    background: $api-color-header-bg;\n\n    // Eliminate the normal padding for inline code blocks\n    code {\n      padding: 0;\n      border-radius: 0;\n    }\n\n    .sig-name:not(.sig-name-nonprimary) {\n      padding: 0;\n      font-weight: 700;\n      color: $api-color-name;\n    }\n\n    .sig-param {\n      font-style: normal;\n    }\n\n    .sig-param .n:not(.desctype) {\n      color: $api-color-param-name;\n    }\n\n    // These elements are added by Sphinx when the\n    // `maximum_signature_line_length` is specified. Override the margins\n    // specified in `_typeset.scss`.\n    dl,\n    dd {\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n\n    .sig-param a.reference .n:not(.desctype):hover {\n      color: var(--md-accent-fg-color);\n    }\n\n    // Ensure each parameter starts on a new line and is indented.\n    &.sig-wrap {\n      .sig-param-decl {\n        &::before {\n          white-space: pre;\n          // 5 spaces below result in 4 spaces of indent.\n          // For some reason one space is lost.\n          content: \"\\A     \";\n        }\n      }\n\n      // Note: It would be nice to have something like\n      // `:last-of-class` here, but that doesn't exist.\n      .sig-paren ~ .sig-paren {\n        // Ensure the final paren starts on a new line\n        &::before {\n          white-space: pre;\n          content: \"\\A\";\n        }\n      }\n    }\n  }\n\n  // Hide the colon added in docutils 0.18\n  dl.objdesc > dd > dl.field-list > dt > .colon {\n    display: none;\n  }\n\n  // Rules for coloring cross-references in signatures.\n  :is(dl.objdesc, dl.api-field) > dt,\n  .sig-inline {\n    a.reference > .sig-name,\n    a.reference.sig-name,\n    a.reference:not(.desctype) > .n {\n      color: var(--md-typeset-a-color);\n\n      &:hover {\n        color: var(--md-accent-fg-color);\n      }\n    }\n\n    .desctype {\n      &,\n      > a.reference {\n        color: $api-color-type;\n        // Prevent pygments highlight rules from taking precedence.\n        .n {\n          color: inherit;\n        }\n      }\n\n      &:is(a.reference):hover,\n      > a.reference:hover {\n        color: var(--md-accent-fg-color);\n      }\n    }\n  }\n\n  dl.objdesc {\n    > dt {\n      padding-top: 0.5em;\n      padding-right: 0.5em;\n      padding-left: 0.5em;\n      font-family: var(--md-code-font-family);\n      background: $api-color-header-bg;\n\n      &,\n      code {\n        font-size: 0.75rem;\n      }\n\n      // Prefix like \"class\", \"staticmethod\".\n      .property {\n        font-style: normal;\n        font-weight: 700;\n        color: $api-color-keyword;\n      }\n\n      .sig-prename {\n        padding: 0;\n        color: $api-color-pre-name;\n      }\n\n      .viewcode-link,\n      .viewcode-back {\n        float: right;\n        text-align: right;\n      }\n\n      &.api-include-path {\n        &,\n        code {\n          font-size: 0.65rem;\n        }\n      }\n    }\n\n    > dt:first-child {\n      padding-top: 0.5em;\n    }\n\n    > dt:last-of-type {\n      padding-bottom: 0.5em;\n    }\n\n    > dd {\n      // Heading like \"Parameters\" or \"Returns\"\n      dl.field-list {\n        > dt {\n          margin-bottom: 1em;\n          font-size: 1em;\n          font-weight: 700;\n        }\n      }\n      // JSON domain uses noindent class to avoid an extra level of\n      // nesting.\n      dd.noindent {\n        margin-left: 0;\n      }\n    }\n  }\n\n  dl.api-field {\n    > dt {\n      // Display as table so that background is just the width of the\n      // content.\n      display: table;\n\n      // Prevent permalink symbol from having a background color of $api-color-header-bg.\n      a.headerlink {\n        position: relative;\n        left: 0.5em;\n        width: 0;\n        margin-left: 0;\n      }\n\n      &,\n      code {\n        font-size: 0.65rem;\n      }\n\n      &.api-parameter-kind {\n        float: right;\n        font-family: var(--md-text-font-family);\n\n        &::before {\n          content: \"[\";\n        }\n\n        &::after {\n          content: \"]\";\n        }\n      }\n    }\n  }\n\n  // Make object summaries more compact\n  dl.objdesc.summary > dd {\n    &,\n    > p:first-child {\n      margin-top: 0;\n    }\n  }\n\n  // C/C++ inline \"text\" expressions: should have regular, rather than\n  // \"code\", font face.\n  .sig-inline:is(.c-texpr, .cpp-texpr) {\n    font-family: unset;\n    background-color: unset;\n  }\n}\n\n.md-nav__link {\n  white-space: nowrap;\n}\n\n:root {\n  > * {\n    --objinfo-icon-fg-alias: #{$clr-orange-900};\n    --objinfo-icon-fg-default: #{$clr-grey-800};\n    --objinfo-icon-fg-data: #{$clr-blue-800};\n    --objinfo-icon-fg-procedure: #{$clr-purple-800};\n    --objinfo-icon-fg-sub-data: #{$clr-green-800};\n    --objinfo-icon-bg-default: var(--md-default-bg-color);\n  }\n}\n\n// Only use dark mode on screens\n@media screen {\n  // Slate theme, i.e. dark mode\n  [data-md-color-scheme=\"slate\"] {\n    --objinfo-icon-fg-alias: #{$clr-orange-300};\n    --objinfo-icon-fg-default: #{$clr-grey-300};\n    --objinfo-icon-fg-data: #{$clr-blue-300};\n    --objinfo-icon-fg-procedure: #{$clr-purple-200};\n    --objinfo-icon-fg-sub-data: #{$clr-green-300};\n  }\n}\n\n.objinfo-icon {\n  display: inline-table;\n  flex-shrink: 0;\n  width: $objinfo-icon-size;\n  height: $objinfo-icon-size;\n  margin-right: 8px;\n  font-family: var(--md-text-font-family);\n  font-weight: 500;\n  line-height: $objinfo-icon-size;\n  color: var(--objinfo-icon-fg-default);\n  text-align: center;\n  vertical-align: middle;\n  background-color: var(--objinfo-icon-bg-default);\n  border: 1px solid var(--objinfo-icon-fg-default);\n  border-radius: 2px;\n\n  @each $objclass in (alias, procedure, data, sub-data) {\n    &__#{$objclass} {\n      color: var(--objinfo-icon-bg-default);\n      background-color: var(--objinfo-icon-fg-#{$objclass});\n      border: 1px solid var(--objinfo-icon-fg-#{$objclass});\n    }\n  }\n}\n\n.search-result-objlabel {\n  float: right;\n  padding: 2px;\n  border: 1px solid var(--md-default-fg-color--light);\n  border-radius: 2px;\n}\n\n// a rule specifically designed for autosummary tables\n// this overrides `.md-typeset code { word-break: break-word; }`\ntable.longtable.docutils.data.align-default {\n  tbody > tr > td > p > a.reference.internal {\n    > code.xref.py.py-obj.docutils.literal.notranslate {\n      > span.pre {\n        word-break: normal;\n      }\n    }\n  }\n}\n"]}