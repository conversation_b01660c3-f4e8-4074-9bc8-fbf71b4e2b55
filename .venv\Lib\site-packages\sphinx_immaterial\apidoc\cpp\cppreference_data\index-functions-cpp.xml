<?xml version="1.0" encoding="utf-8" ?>
<!--
    Copyright (C) 2011  <PERSON><PERSON><PERSON> <<EMAIL>>

    This file is part of cppreference-doc

    This work is licensed under the Creative Commons Attribution-ShareAlike 3.0
    Unported License. To view a copy of this license, visit
    http://creativecommons.org/licenses/by-sa/3.0/ or send a letter to Creative
    Commons, 444 Castro Street, Suite 900, Mountain View, California, 94041, USA.

    Permission is granted to copy, distribute and/or modify this document
    under the terms of the GNU Free Documentation License, Version 1.3 or
    any later version published by the Free Software Foundation; with no
    Invariant Sections, no Front-Cover Texts, and no Back-Cover Texts.
-->

<!--
    For the documentation of the schema, see the accompanying
    index-function.README file
-->
<index>

    <!--=======================================================================-->
    <!-- cpp/types -->

        <!-- generic types -->

    <typedef name="std::size_t" link="cpp/types/size_t"/>
    <typedef name="std::ptrdiff_t" link="cpp/types/ptrdiff_t"/>
    <typedef name="std::nullptr_t" link="cpp/types/nullptr_t" since="c++11"/>
    <const name="NULL" link="cpp/types/NULL"/>
    <typedef name="std::max_align_t" link="cpp/types/max_align_t" since="c++11"/>
    <function name="offsetof" link="cpp/types/offsetof"/>
    <class name="std::byte" link="cpp/types/byte" since="c++17">
        <overload name="std::to_integer" link="."/>
        <overload name="operator&lt;&lt;=" link="."/>
        <overload name="operator&gt;&gt;=" link="."/>
        <overload name="operator&lt;&lt;" link="."/>
        <overload name="operator&gt;&gt;" link="."/>
        <overload name="operator|=" link="."/>
        <overload name="operator&amp;=" link="."/>
        <overload name="operator^=" link="."/>
        <overload name="operator|" link="."/>
        <overload name="operator&amp;" link="."/>
        <overload name="operator^" link="."/>
        <overload name="operator~" link="."/>
    </class>

        <!-- fixed width integer types -->

    <typedef name="std::int8_t" link="cpp/types/integer"/>
    <typedef name="std::int16_t" link="cpp/types/integer"/>
    <typedef name="std::int32_t" link="cpp/types/integer"/>
    <typedef name="std::int64_t" link="cpp/types/integer"/>
    <typedef name="std::int_fast8_t" link="cpp/types/integer"/>
    <typedef name="std::int_fast16_t" link="cpp/types/integer"/>
    <typedef name="std::int_fast32_t" link="cpp/types/integer"/>
    <typedef name="std::int_fast64_t" link="cpp/types/integer"/>
    <typedef name="std::int_least8_t" link="cpp/types/integer"/>
    <typedef name="std::int_least16_t" link="cpp/types/integer"/>
    <typedef name="std::int_least32_t" link="cpp/types/integer"/>
    <typedef name="std::int_least64_t" link="cpp/types/integer"/>
    <typedef name="std::intmax_t" link="cpp/types/integer"/>
    <typedef name="std::intptr_t" link="cpp/types/integer"/>

    <typedef name="std::uint8_t" link="cpp/types/integer"/>
    <typedef name="std::uint16_t" link="cpp/types/integer"/>
    <typedef name="std::uint32_t" link="cpp/types/integer"/>
    <typedef name="std::uint64_t" link="cpp/types/integer"/>
    <typedef name="std::uint_fast8_t" link="cpp/types/integer"/>
    <typedef name="std::uint_fast16_t" link="cpp/types/integer"/>
    <typedef name="std::uint_fast32_t" link="cpp/types/integer"/>
    <typedef name="std::uint_fast64_t" link="cpp/types/integer"/>
    <typedef name="std::uint_least8_t" link="cpp/types/integer"/>
    <typedef name="std::uint_least16_t" link="cpp/types/integer"/>
    <typedef name="std::uint_least32_t" link="cpp/types/integer"/>
    <typedef name="std::uint_least64_t" link="cpp/types/integer"/>
    <typedef name="std::uintmax_t" link="cpp/types/integer"/>
    <typedef name="std::uintptr_t" link="cpp/types/integer"/>

    <const name="INT8_MIN" link="cpp/types/integer"/>
    <const name="INT16_MIN" link="cpp/types/integer"/>
    <const name="INT32_MIN" link="cpp/types/integer"/>
    <const name="INT64_MIN" link="cpp/types/integer"/>
    <const name="INT_FAST8_MIN" link="cpp/types/integer"/>
    <const name="INT_FAST16_MIN" link="cpp/types/integer"/>
    <const name="INT_FAST32_MIN" link="cpp/types/integer"/>
    <const name="INT_FAST64_MIN" link="cpp/types/integer"/>
    <const name="INT_LEAST8_MIN" link="cpp/types/integer"/>
    <const name="INT_LEAST16_MIN" link="cpp/types/integer"/>
    <const name="INT_LEAST32_MIN" link="cpp/types/integer"/>
    <const name="INT_LEAST64_MIN" link="cpp/types/integer"/>
    <const name="INTPTR_MIN" link="cpp/types/integer"/>
    <const name="INTMAX_MIN" link="cpp/types/integer"/>

    <const name="INT8_MAX" link="cpp/types/integer"/>
    <const name="INT16_MAX" link="cpp/types/integer"/>
    <const name="INT32_MAX" link="cpp/types/integer"/>
    <const name="INT64_MAX" link="cpp/types/integer"/>
    <const name="INT_FAST8_MAX" link="cpp/types/integer"/>
    <const name="INT_FAST16_MAX" link="cpp/types/integer"/>
    <const name="INT_FAST32_MAX" link="cpp/types/integer"/>
    <const name="INT_FAST64_MAX" link="cpp/types/integer"/>
    <const name="INT_LEAST8_MAX" link="cpp/types/integer"/>
    <const name="INT_LEAST16_MAX" link="cpp/types/integer"/>
    <const name="INT_LEAST32_MAX" link="cpp/types/integer"/>
    <const name="INT_LEAST64_MAX" link="cpp/types/integer"/>
    <const name="INTPTR_MAX" link="cpp/types/integer"/>
    <const name="INTMAX_MAX" link="cpp/types/integer"/>

    <const name="UINT8_MAX" link="cpp/types/integer"/>
    <const name="UINT16_MAX" link="cpp/types/integer"/>
    <const name="UINT32_MAX" link="cpp/types/integer"/>
    <const name="UINT64_MAX" link="cpp/types/integer"/>
    <const name="UINT_FAST8_MAX" link="cpp/types/integer"/>
    <const name="UINT_FAST16_MAX" link="cpp/types/integer"/>
    <const name="UINT_FAST32_MAX" link="cpp/types/integer"/>
    <const name="UINT_FAST64_MAX" link="cpp/types/integer"/>
    <const name="UINT_LEAST8_MAX" link="cpp/types/integer"/>
    <const name="UINT_LEAST16_MAX" link="cpp/types/integer"/>
    <const name="UINT_LEAST32_MAX" link="cpp/types/integer"/>
    <const name="UINT_LEAST64_MAX" link="cpp/types/integer"/>
    <const name="UINTPTR_MAX" link="cpp/types/integer"/>
    <const name="UINTMAX_MAX" link="cpp/types/integer"/>

    <const name="PRId8" link="cpp/types/integer"/>
    <const name="PRId16" link="cpp/types/integer"/>
    <const name="PRId32" link="cpp/types/integer"/>
    <const name="PRId64" link="cpp/types/integer"/>
    <const name="PRIdLEAST8" link="cpp/types/integer"/>
    <const name="PRIdLEAST16" link="cpp/types/integer"/>
    <const name="PRIdLEAST32" link="cpp/types/integer"/>
    <const name="PRIdLEAST64" link="cpp/types/integer"/>
    <const name="PRIdFAST8" link="cpp/types/integer"/>
    <const name="PRIdFAST16" link="cpp/types/integer"/>
    <const name="PRIdFAST32" link="cpp/types/integer"/>
    <const name="PRIdFAST64" link="cpp/types/integer"/>
    <const name="PRIdMAX" link="cpp/types/integer"/>
    <const name="PRIdPTR" link="cpp/types/integer"/>

    <const name="PRIi8" link="cpp/types/integer"/>
    <const name="PRIi16" link="cpp/types/integer"/>
    <const name="PRIi32" link="cpp/types/integer"/>
    <const name="PRIi64" link="cpp/types/integer"/>
    <const name="PRIiLEAST8" link="cpp/types/integer"/>
    <const name="PRIiLEAST16" link="cpp/types/integer"/>
    <const name="PRIiLEAST32" link="cpp/types/integer"/>
    <const name="PRIiLEAST64" link="cpp/types/integer"/>
    <const name="PRIiFAST8" link="cpp/types/integer"/>
    <const name="PRIiFAST16" link="cpp/types/integer"/>
    <const name="PRIiFAST32" link="cpp/types/integer"/>
    <const name="PRIiFAST64" link="cpp/types/integer"/>
    <const name="PRIiMAX" link="cpp/types/integer"/>
    <const name="PRIiPTR" link="cpp/types/integer"/>

    <const name="PRIu8" link="cpp/types/integer"/>
    <const name="PRIu16" link="cpp/types/integer"/>
    <const name="PRIu32" link="cpp/types/integer"/>
    <const name="PRIu64" link="cpp/types/integer"/>
    <const name="PRIuLEAST8" link="cpp/types/integer"/>
    <const name="PRIuLEAST16" link="cpp/types/integer"/>
    <const name="PRIuLEAST32" link="cpp/types/integer"/>
    <const name="PRIuLEAST64" link="cpp/types/integer"/>
    <const name="PRIuFAST8" link="cpp/types/integer"/>
    <const name="PRIuFAST16" link="cpp/types/integer"/>
    <const name="PRIuFAST32" link="cpp/types/integer"/>
    <const name="PRIuFAST64" link="cpp/types/integer"/>
    <const name="PRIuMAX" link="cpp/types/integer"/>
    <const name="PRIuPTR" link="cpp/types/integer"/>

    <const name="PRIo8" link="cpp/types/integer"/>
    <const name="PRIo16" link="cpp/types/integer"/>
    <const name="PRIo32" link="cpp/types/integer"/>
    <const name="PRIo64" link="cpp/types/integer"/>
    <const name="PRIoLEAST8" link="cpp/types/integer"/>
    <const name="PRIoLEAST16" link="cpp/types/integer"/>
    <const name="PRIoLEAST32" link="cpp/types/integer"/>
    <const name="PRIoLEAST64" link="cpp/types/integer"/>
    <const name="PRIoFAST8" link="cpp/types/integer"/>
    <const name="PRIoFAST16" link="cpp/types/integer"/>
    <const name="PRIoFAST32" link="cpp/types/integer"/>
    <const name="PRIoFAST64" link="cpp/types/integer"/>
    <const name="PRIoMAX" link="cpp/types/integer"/>
    <const name="PRIoPTR" link="cpp/types/integer"/>

    <const name="PRIx8" link="cpp/types/integer"/>
    <const name="PRIx16" link="cpp/types/integer"/>
    <const name="PRIx32" link="cpp/types/integer"/>
    <const name="PRIx64" link="cpp/types/integer"/>
    <const name="PRIxLEAST8" link="cpp/types/integer"/>
    <const name="PRIxLEAST16" link="cpp/types/integer"/>
    <const name="PRIxLEAST32" link="cpp/types/integer"/>
    <const name="PRIxLEAST64" link="cpp/types/integer"/>
    <const name="PRIxFAST8" link="cpp/types/integer"/>
    <const name="PRIxFAST16" link="cpp/types/integer"/>
    <const name="PRIxFAST32" link="cpp/types/integer"/>
    <const name="PRIxFAST64" link="cpp/types/integer"/>
    <const name="PRIxMAX" link="cpp/types/integer"/>
    <const name="PRIxPTR" link="cpp/types/integer"/>

    <const name="PRIX8" link="cpp/types/integer"/>
    <const name="PRIX16" link="cpp/types/integer"/>
    <const name="PRIX32" link="cpp/types/integer"/>
    <const name="PRIX64" link="cpp/types/integer"/>
    <const name="PRIXLEAST8" link="cpp/types/integer"/>
    <const name="PRIXLEAST16" link="cpp/types/integer"/>
    <const name="PRIXLEAST32" link="cpp/types/integer"/>
    <const name="PRIXLEAST64" link="cpp/types/integer"/>
    <const name="PRIXFAST8" link="cpp/types/integer"/>
    <const name="PRIXFAST16" link="cpp/types/integer"/>
    <const name="PRIXFAST32" link="cpp/types/integer"/>
    <const name="PRIXFAST64" link="cpp/types/integer"/>
    <const name="PRIXMAX" link="cpp/types/integer"/>
    <const name="PRIXPTR" link="cpp/types/integer"/>

    <const name="SCNd8" link="cpp/types/integer"/>
    <const name="SCNd16" link="cpp/types/integer"/>
    <const name="SCNd32" link="cpp/types/integer"/>
    <const name="SCNd64" link="cpp/types/integer"/>
    <const name="SCNdLEAST8" link="cpp/types/integer"/>
    <const name="SCNdLEAST16" link="cpp/types/integer"/>
    <const name="SCNdLEAST32" link="cpp/types/integer"/>
    <const name="SCNdLEAST64" link="cpp/types/integer"/>
    <const name="SCNdFAST8" link="cpp/types/integer"/>
    <const name="SCNdFAST16" link="cpp/types/integer"/>
    <const name="SCNdFAST32" link="cpp/types/integer"/>
    <const name="SCNdFAST64" link="cpp/types/integer"/>
    <const name="SCNdMAX" link="cpp/types/integer"/>
    <const name="SCNdPTR" link="cpp/types/integer"/>

    <const name="SCNi8" link="cpp/types/integer"/>
    <const name="SCNi16" link="cpp/types/integer"/>
    <const name="SCNi32" link="cpp/types/integer"/>
    <const name="SCNi64" link="cpp/types/integer"/>
    <const name="SCNiLEAST8" link="cpp/types/integer"/>
    <const name="SCNiLEAST16" link="cpp/types/integer"/>
    <const name="SCNiLEAST32" link="cpp/types/integer"/>
    <const name="SCNiLEAST64" link="cpp/types/integer"/>
    <const name="SCNiFAST8" link="cpp/types/integer"/>
    <const name="SCNiFAST16" link="cpp/types/integer"/>
    <const name="SCNiFAST32" link="cpp/types/integer"/>
    <const name="SCNiFAST64" link="cpp/types/integer"/>
    <const name="SCNiMAX" link="cpp/types/integer"/>
    <const name="SCNiPTR" link="cpp/types/integer"/>

    <const name="SCNu8" link="cpp/types/integer"/>
    <const name="SCNu16" link="cpp/types/integer"/>
    <const name="SCNu32" link="cpp/types/integer"/>
    <const name="SCNu64" link="cpp/types/integer"/>
    <const name="SCNuLEAST8" link="cpp/types/integer"/>
    <const name="SCNuLEAST16" link="cpp/types/integer"/>
    <const name="SCNuLEAST32" link="cpp/types/integer"/>
    <const name="SCNuLEAST64" link="cpp/types/integer"/>
    <const name="SCNuFAST8" link="cpp/types/integer"/>
    <const name="SCNuFAST16" link="cpp/types/integer"/>
    <const name="SCNuFAST32" link="cpp/types/integer"/>
    <const name="SCNuFAST64" link="cpp/types/integer"/>
    <const name="SCNuMAX" link="cpp/types/integer"/>
    <const name="SCNuPTR" link="cpp/types/integer"/>

    <const name="SCNo8" link="cpp/types/integer"/>
    <const name="SCNo16" link="cpp/types/integer"/>
    <const name="SCNo32" link="cpp/types/integer"/>
    <const name="SCNo64" link="cpp/types/integer"/>
    <const name="SCNoLEAST8" link="cpp/types/integer"/>
    <const name="SCNoLEAST16" link="cpp/types/integer"/>
    <const name="SCNoLEAST32" link="cpp/types/integer"/>
    <const name="SCNoLEAST64" link="cpp/types/integer"/>
    <const name="SCNoFAST8" link="cpp/types/integer"/>
    <const name="SCNoFAST16" link="cpp/types/integer"/>
    <const name="SCNoFAST32" link="cpp/types/integer"/>
    <const name="SCNoFAST64" link="cpp/types/integer"/>
    <const name="SCNoMAX" link="cpp/types/integer"/>
    <const name="SCNoPTR" link="cpp/types/integer"/>

    <const name="SCNx8" link="cpp/types/integer"/>
    <const name="SCNx16" link="cpp/types/integer"/>
    <const name="SCNx32" link="cpp/types/integer"/>
    <const name="SCNx64" link="cpp/types/integer"/>
    <const name="SCNxLEAST8" link="cpp/types/integer"/>
    <const name="SCNxLEAST16" link="cpp/types/integer"/>
    <const name="SCNxLEAST32" link="cpp/types/integer"/>
    <const name="SCNxLEAST64" link="cpp/types/integer"/>
    <const name="SCNxFAST8" link="cpp/types/integer"/>
    <const name="SCNxFAST16" link="cpp/types/integer"/>
    <const name="SCNxFAST32" link="cpp/types/integer"/>
    <const name="SCNxFAST64" link="cpp/types/integer"/>
    <const name="SCNxMAX" link="cpp/types/integer"/>
    <const name="SCNxPTR" link="cpp/types/integer"/>

        <!-- cpp/types/numeric_limits -->

    <class name="std::numeric_limits" link="cpp/types/numeric_limits" type="template">
        <const name="is_specialized"/>
        <const name="is_signed"/>
        <const name="is_integer"/>
        <const name="is_exact"/>

        <const name="has_infinity"/>
        <const name="has_quiet_NaN"/>
        <const name="has_signaling_NaN"/>
        <const name="has_denorm"/>
        <const name="has_denorm_loss"/>

        <const name="round_style"/>

        <const name="is_iec559"/>
        <const name="is_bounded"/>
        <const name="is_modulo"/>

        <const name="digits"/>
        <const name="digits10"/>
        <const name="max_digits10"/>

        <const name="radix"/>
        <const name="min_exponent"/>
        <const name="min_exponent10"/>
        <const name="max_exponent"/>
        <const name="max_exponent10"/>
        <const name="traps"/>
        <const name="tinyness_before"/>

        <function name="min"/>
        <function name="max"/>
        <function name="lowest"/>
        <function name="epsilon"/>
        <function name="round_error"/>
        <function name="infinity"/>
        <function name="quiet_NaN"/>
        <function name="signaling_NaN"/>
        <function name="denorm_min"/>
    </class>

    <enum name="std::float_round_style" link="cpp/types/numeric_limits/float_round_style"/>
    <const name="std::round_indeterminate" link="cpp/types/numeric_limits/float_round_style"/>
    <const name="std::round_toward_zero" link="cpp/types/numeric_limits/float_round_style"/>
    <const name="std::round_to_nearest" link="cpp/types/numeric_limits/float_round_style"/>
    <const name="std::round_toward_infinity" link="cpp/types/numeric_limits/float_round_style"/>
    <const name="std::round_toward_neg_infinity" link="cpp/types/numeric_limits/float_round_style"/>

    <enum name="std::float_denorm_style" link="cpp/types/numeric_limits/float_denorm_style"/>
    <const name="std::denorm_indeterminate" link="cpp/types/numeric_limits/float_denorm_style"/>
    <const name="std::denorm_absent" link="cpp/types/numeric_limits/float_denorm_style"/>
    <const name="std::denorm_present" link="cpp/types/numeric_limits/float_denorm_style"/>

        <!-- cpp/types/climits -->
    <const name="PTRDIFF_MIN" link="cpp/types/climits"/>
    <const name="PTRDIFF_MAX" link="cpp/types/climits"/>
    <const name="SIZE_MAX" link="cpp/types/climits"/>
    <const name="SIG_ATOMIC_MIN" link="cpp/types/climits"/>
    <const name="SIG_ATOMIC_MAX" link="cpp/types/climits"/>
    <const name="WCHAR_MIN" link="cpp/types/climits"/>
    <const name="WCHAR_MAX" link="cpp/types/climits"/>
    <const name="WINT_MIN" link="cpp/types/climits"/>
    <const name="WINT_MAX" link="cpp/types/climits"/>

    <const name="CHAR_BIT" link="cpp/types/climits"/>
    <const name="MB_LEN_MAX" link="cpp/types/climits"/>
    <const name="CHAR_MIN" link="cpp/types/climits"/>
    <const name="CHAR_MAX" link="cpp/types/climits"/>

    <const name="SCHAR_MIN" link="cpp/types/climits"/>
    <const name="SHRT_MIN" link="cpp/types/climits"/>
    <const name="INT_MIN" link="cpp/types/climits"/>
    <const name="LONG_MIN" link="cpp/types/climits"/>
    <const name="LLONG_MIN" link="cpp/types/climits"/>

    <const name="SCHAR_MAX" link="cpp/types/climits"/>
    <const name="SHRT_MAX" link="cpp/types/climits"/>
    <const name="INT_MAX" link="cpp/types/climits"/>
    <const name="LONG_MAX" link="cpp/types/climits"/>
    <const name="LLONG_MAX" link="cpp/types/climits"/>

    <const name="UCHAR_MAX" link="cpp/types/climits"/>
    <const name="USHRT_MAX" link="cpp/types/climits"/>
    <const name="UINT_MAX" link="cpp/types/climits"/>
    <const name="ULONG_MAX" link="cpp/types/climits"/>
    <const name="ULLONG_MAX" link="cpp/types/climits"/>

    <const name="FLT_RADIX" link="cpp/types/climits"/>
    <const name="DECIMAL_DIG" link="cpp/types/climits" since="c++11"/>

    <const name="FLT_DECIMAL_DIG" link="cpp/types/climits" since="c++17"/>
    <const name="DBL_DECIMAL_DIG" link="cpp/types/climits" since="c++17"/>
    <const name="LDBL_DECIMAL_DIG" link="cpp/types/climits" since="c++17"/>

    <const name="FLT_MIN" link="cpp/types/climits"/>
    <const name="DBL_MIN" link="cpp/types/climits"/>
    <const name="LDBL_MIN" link="cpp/types/climits"/>

    <const name="FLT_TRUE_MIN" link="cpp/types/climits" since="c++17"/>
    <const name="DBL_TRUE_MIN" link="cpp/types/climits" since="c++17"/>
    <const name="LDBL_TRUE_MIN" link="cpp/types/climits" since="c++17"/>

    <const name="FLT_MAX" link="cpp/types/climits"/>
    <const name="DBL_MAX" link="cpp/types/climits"/>
    <const name="LDBL_MAX" link="cpp/types/climits"/>

    <const name="FLT_EPSILON" link="cpp/types/climits"/>
    <const name="DBL_EPSILON" link="cpp/types/climits"/>
    <const name="LDBL_EPSILON" link="cpp/types/climits"/>

    <const name="FLT_DIG" link="cpp/types/climits"/>
    <const name="DBL_DIG" link="cpp/types/climits"/>
    <const name="LDBL_DIG" link="cpp/types/climits"/>

    <const name="FLT_MANT_DIG" link="cpp/types/climits"/>
    <const name="DBL_MANT_DIG" link="cpp/types/climits"/>
    <const name="LDBL_MANT_DIG" link="cpp/types/climits"/>

    <const name="FLT_MIN_EXP" link="cpp/types/climits"/>
    <const name="DBL_MIN_EXP" link="cpp/types/climits"/>
    <const name="LDBL_MIN_EXP" link="cpp/types/climits"/>

    <const name="FLT_MIN_10_EXP" link="cpp/types/climits"/>
    <const name="DBL_MIN_10_EXP" link="cpp/types/climits"/>
    <const name="LDBL_MIN_10_EXP" link="cpp/types/climits"/>

    <const name="FLT_MAX_EXP" link="cpp/types/climits"/>
    <const name="DBL_MAX_EXP" link="cpp/types/climits"/>
    <const name="LDBL_MAX_EXP" link="cpp/types/climits"/>

    <const name="FLT_MAX_10_EXP" link="cpp/types/climits"/>
    <const name="DBL_MAX_10_EXP" link="cpp/types/climits"/>
    <const name="LDBL_MAX_10_EXP" link="cpp/types/climits"/>

    <const name="FLT_ROUNDS" link="cpp/types/climits/FLT_ROUNDS"/>
    <const name="FLT_EVAL_METHOD" link="cpp/types/climits/FLT_EVAL_METHOD"/>

    <const name="FLT_HAS_SUBNORM" link="cpp/types/climits" since="c++17"/>
    <const name="DBL_HAS_SUBNORM" link="cpp/types/climits" since="c++17"/>
    <const name="LDBL_HAS_SUBNORM" link="cpp/types/climits" since="c++17"/>

        <!-- RTTI -->

    <class name="std::type_info" link="cpp/types/type_info">
        <function name="operator==" link="operator_cmp"/>
        <function name="operator!=" link="operator_cmp"/>
        <function name="before"/>
        <function name="hash_code"/>
        <function name="name"/>
    </class>

    <class name="std::type_index" link="cpp/types/type_index" since="c++11">
        <constructor/>
        <function name="operator==" link="operator_cmp"/>
        <function name="operator!=" link="operator_cmp"/>
        <function name="operator&lt;=" link="operator_cmp"/>
        <function name="operator&lt;" link="operator_cmp"/>
        <function name="operator&gt;=" link="operator_cmp"/>
        <function name="operator&gt;" link="operator_cmp"/>
        <function name="hash_code"/>
        <function name="name"/>

        <specialization name="std::hash" link="hash"/>
    </class>

    <class name="std::bad_typeid" link="cpp/types/bad_typeid">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <class name="std::bad_cast" link="cpp/types/bad_cast">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <!-- type traits -->
    <class name="std::is_void" link="cpp/types/is_void" since="c++11"/>
    <variable name="std::is_void_v" link="cpp/types/is_void" since="c++17"/>
    <class name="std::is_null_pointer" link="cpp/types/is_null_pointer" since="c++14"/>
    <variable name="std::is_null_pointer_v" link="cpp/types/is_null_pointer" since="c++17"/>
    <class name="std::is_integral" link="cpp/types/is_integral" since="c++11"/>
    <variable name="std::is_integral_v" link="cpp/types/is_integral" since="c++17"/>
    <class name="std::is_floating_point" link="cpp/types/is_floating_point" since="c++11"/>
    <variable name="std::is_floating_point_v" link="cpp/types/is_floating_point" since="c++17"/>
    <class name="std::is_array" link="cpp/types/is_array" since="c++11"/>
    <variable name="std::is_array_v" link="cpp/types/is_array" since="c++17"/>
    <class name="std::is_enum" link="cpp/types/is_enum" since="c++11"/>
    <variable name="std::is_enum_v" link="cpp/types/is_enum" since="c++17"/>
    <class name="std::is_union" link="cpp/types/is_union" since="c++11"/>
    <variable name="std::is_union_v" link="cpp/types/is_union" since="c++17"/>
    <class name="std::is_class" link="cpp/types/is_class" since="c++11"/>
    <variable name="std::is_class_v" link="cpp/types/is_class" since="c++17"/>
    <class name="std::is_function" link="cpp/types/is_function" since="c++11"/>
    <variable name="std::is_function_v" link="cpp/types/is_function" since="c++17"/>
    <class name="std::is_pointer" link="cpp/types/is_pointer" since="c++11"/>
    <variable name="std::is_pointer_v" link="cpp/types/is_pointer" since="c++17"/>
    <class name="std::is_lvalue_reference" link="cpp/types/is_lvalue_reference" since="c++11"/>
    <variable name="std::is_lvalue_reference_v" link="cpp/types/is_lvalue_reference" since="c++17"/>
    <class name="std::is_rvalue_reference" link="cpp/types/is_rvalue_reference" since="c++11"/>
    <variable name="std::is_rvalue_reference_v" link="cpp/types/is_rvalue_reference" since="c++17"/>
    <class name="std::is_member_object_pointer" link="cpp/types/is_member_object_pointer" since="c++11"/>
    <variable name="std::is_member_object_pointer_v" link="cpp/types/is_member_object_pointer" since="c++17"/>
    <class name="std::is_member_function_pointer" link="cpp/types/is_member_function_pointer" since="c++11"/>
    <variable name="std::is_member_function_pointer_v" link="cpp/types/is_member_function_pointer" since="c++17"/>

    <class name="std::is_fundamental" link="cpp/types/is_fundamental" since="c++11"/>
    <variable name="std::is_fundamental_v" link="cpp/types/is_fundamental" since="c++17"/>
    <class name="std::is_arithmetic" link="cpp/types/is_arithmetic" since="c++11"/>
    <variable name="std::is_arithmetic_v" link="cpp/types/is_arithmetic" since="c++17"/>
    <class name="std::is_scalar" link="cpp/types/is_scalar" since="c++11"/>
    <variable name="std::is_scalar_v" link="cpp/types/is_scalar" since="c++17"/>
    <class name="std::is_object" link="cpp/types/is_object" since="c++11"/>
    <variable name="std::is_object_v" link="cpp/types/is_object" since="c++17"/>
    <class name="std::is_compound" link="cpp/types/is_compound" since="c++11"/>
    <variable name="std::is_compound_v" link="cpp/types/is_compound" since="c++17"/>
    <class name="std::is_reference" link="cpp/types/is_reference" since="c++11"/>
    <variable name="std::is_reference_v" link="cpp/types/is_reference" since="c++17"/>
    <class name="std::is_member_pointer" link="cpp/types/is_member_pointer" since="c++11"/>
    <variable name="std::is_member_pointer_v" link="cpp/types/is_member_pointer" since="c++17"/>

    <class name="std::is_const" link="cpp/types/is_const" since="c++11"/>
    <variable name="std::is_const_v" link="cpp/types/is_const" since="c++17"/>
    <class name="std::is_volatile" link="cpp/types/is_volatile" since="c++11"/>
    <variable name="std::is_volatile_v" link="cpp/types/is_volatile" since="c++17"/>
    <class name="std::is_trivial" link="cpp/types/is_trivial" since="c++11"/>
    <variable name="std::is_trivial_v" link="cpp/types/is_trivial" since="c++17"/>
    <class name="std::is_trivially_copyable" link="cpp/types/is_trivially_copyable" since="c++11"/>
    <variable name="std::is_trivially_copyable_v" link="cpp/types/is_trivially_copyable" since="c++17"/>
    <class name="std::is_standard_layout" link="cpp/types/is_standard_layout" since="c++11"/>
    <variable name="std::is_standard_layout_v" link="cpp/types/is_standard_layout" since="c++17"/>
    <class name="std::is_pod" link="cpp/types/is_pod" since="c++11"/>
    <variable name="std::is_pod_v" link="cpp/types/is_pod" since="c++17"/>
    <class name="std::is_literal_type" link="cpp/types/is_literal_type" since="c++11"/>
    <variable name="std::is_literal_type_v" link="cpp/types/is_literal_type" since="c++17"/>
    <class name="std::has_unique_object_representations" link="cpp/types/has_unique_object_representations" since="c++17"/>
    <variable name="std::has_unique_object_representations_v" link="cpp/types/has_unique_object_representations" since="c++17"/>
    <class name="std::is_empty" link="cpp/types/is_empty" since="c++11"/>
    <variable name="std::is_empty_v" link="cpp/types/is_empty" since="c++17"/>
    <class name="std::is_polymorphic" link="cpp/types/is_polymorphic" since="c++11"/>
    <variable name="std::is_polymorphic_v" link="cpp/types/is_polymorphic" since="c++17"/>
    <class name="std::is_abstract" link="cpp/types/is_abstract" since="c++11"/>
    <variable name="std::is_abstract_v" link="cpp/types/is_abstract" since="c++17"/>
    <class name="std::is_final" link="cpp/types/is_final" since="c++14"/>
    <variable name="std::is_final_v" link="cpp/types/is_final" since="c++17"/>
    <class name="std::is_aggregate" link="cpp/types/is_aggregate" since="c++14"/>
    <variable name="std::is_aggregate_v" link="cpp/types/is_aggregate" since="c++17"/>
    <class name="std::is_signed" link="cpp/types/is_signed" since="c++11"/>
    <variable name="std::is_signed_v" link="cpp/types/is_signed" since="c++17"/>
    <class name="std::is_unsigned" link="cpp/types/is_unsigned" since="c++11"/>
    <variable name="std::is_unsigned_v" link="cpp/types/is_unsigned" since="c++17"/>
    <class name="std::is_bounded_array" link="cpp/types/is_bounded_array" since="c++20"/>
    <variable name="std::is_bounded_array_v" link="cpp/types/is_bounded_array" since="c++20"/>
    <class name="std::is_unbounded_array" link="cpp/types/is_unbounded_array" since="c++20"/>
    <variable name="std::is_unbounded_array_v" link="cpp/types/is_unbounded_array" since="c++20"/>

    <class name="std::is_constructible" link="cpp/types/is_constructible" since="c++11"/>
    <variable name="std::is_constructible_v" link="cpp/types/is_constructible" since="c++17"/>
    <class name="std::is_trivially_constructible" link="cpp/types/is_constructible" since="c++11"/>
    <variable name="std::is_trivially_constructible_v" link="cpp/types/is_constructible" since="c++17"/>
    <class name="std::is_nothrow_constructible" link="cpp/types/is_constructible" since="c++11"/>
    <variable name="std::is_nothrow_constructible_v" link="cpp/types/is_constructible" since="c++17"/>
    <class name="std::is_default_constructible" link="cpp/types/is_default_constructible" since="c++11"/>
    <variable name="std::is_default_constructible_v" link="cpp/types/is_default_constructible" since="c++17"/>
    <class name="std::is_trivially_default_constructible" link="cpp/types/is_default_constructible" since="c++11"/>
    <variable name="std::is_trivially_default_constructible_v" link="cpp/types/is_default_constructible" since="c++17"/>
    <class name="std::is_nothrow_default_constructible" link="cpp/types/is_default_constructible" since="c++11"/>
    <variable name="std::is_nothrow_default_constructible_v" link="cpp/types/is_default_constructible" since="c++17"/>
    <class name="std::is_copy_constructible" link="cpp/types/is_copy_constructible" since="c++11"/>
    <variable name="std::is_copy_constructible_v" link="cpp/types/is_copy_constructible" since="c++17"/>
    <class name="std::is_trivially_copy_constructible" link="cpp/types/is_copy_constructible" since="c++11"/>
    <variable name="std::is_trivially_copy_constructible_v" link="cpp/types/is_copy_constructible" since="c++17"/>
    <class name="std::is_nothrow_copy_constructible" link="cpp/types/is_copy_constructible" since="c++11"/>
    <variable name="std::is_nothrow_copy_constructible_v" link="cpp/types/is_copy_constructible" since="c++17"/>
    <class name="std::is_move_constructible" link="cpp/types/is_move_constructible" since="c++11"/>
    <variable name="std::is_move_constructible_v" link="cpp/types/is_move_constructible" since="c++17"/>
    <class name="std::is_trivially_move_constructible" link="cpp/types/is_move_constructible" since="c++11"/>
    <variable name="std::is_trivially_move_constructible_v" link="cpp/types/is_move_constructible" since="c++17"/>
    <class name="std::is_nothrow_move_constructible" link="cpp/types/is_move_constructible" since="c++11"/>
    <variable name="std::is_nothrow_move_constructible_v" link="cpp/types/is_move_constructible" since="c++17"/>

    <class name="std::is_assignable" link="cpp/types/is_assignable" since="c++11"/>
    <variable name="std::is_assignable_v" link="cpp/types/is_assignable" since="c++17"/>
    <class name="std::is_trivially_assignable" link="cpp/types/is_assignable" since="c++11"/>
    <variable name="std::is_trivially_assignable_v" link="cpp/types/is_assignable" since="c++17"/>
    <class name="std::is_nothrow_assignable" link="cpp/types/is_assignable" since="c++11"/>
    <variable name="std::is_nothrow_assignable_v" link="cpp/types/is_assignable" since="c++17"/>
    <class name="std::is_copy_assignable" link="cpp/types/is_copy_assignable" since="c++11"/>
    <variable name="std::is_copy_assignable_v" link="cpp/types/is_copy_assignable" since="c++17"/>
    <class name="std::is_trivially_copy_assignable" link="cpp/types/is_copy_assignable" since="c++11"/>
    <variable name="std::is_trivially_copy_assignable_v" link="cpp/types/is_copy_assignable" since="c++17"/>
    <class name="std::is_nothrow_copy_assignable" link="cpp/types/is_copy_assignable" since="c++11"/>
    <variable name="std::is_nothrow_copy_assignable_v" link="cpp/types/is_copy_assignable" since="c++17"/>
    <class name="std::is_move_assignable" link="cpp/types/is_move_assignable" since="c++11"/>
    <variable name="std::is_move_assignable_v" link="cpp/types/is_move_assignable" since="c++17"/>
    <class name="std::is_trivially_move_assignable" link="cpp/types/is_move_assignable" since="c++11"/>
    <variable name="std::is_trivially_move_assignable_v" link="cpp/types/is_move_assignable" since="c++17"/>
    <class name="std::is_nothrow_move_assignable" link="cpp/types/is_move_assignable" since="c++11"/>
    <variable name="std::is_nothrow_move_assignable_v" link="cpp/types/is_move_assignable" since="c++17"/>
    <class name="std::is_destructible" link="cpp/types/is_destructible" since="c++11"/>
    <variable name="std::is_destructible_v" link="cpp/types/is_destructible" since="c++17"/>
    <class name="std::is_trivially_destructible" link="cpp/types/is_destructible" since="c++11"/>
    <variable name="std::is_trivially_destructible_v" link="cpp/types/is_destructible" since="c++17"/>
    <class name="std::is_nothrow_destructible" link="cpp/types/is_destructible" since="c++11"/>
    <variable name="std::is_nothrow_destructible_v" link="cpp/types/is_destructible" since="c++17"/>

    <class name="std::is_swappable" link="cpp/types/is_swappable" since="c++17"/>
    <variable name="std::is_swappable_v" link="cpp/types/is_swappable" since="c++17"/>
    <class name="std::is_swappable_with" link="cpp/types/is_swappable" since="c++17"/>
    <variable name="std::is_swappable_with_v" link="cpp/types/is_swappable" since="c++17"/>
    <class name="std::is_nothrow_swappable" link="cpp/types/is_swappable" since="c++17"/>
    <variable name="std::is_nothrow_swappable_v" link="cpp/types/is_swappable" since="c++17"/>
    <class name="std::is_nothrow_swappable_with" link="cpp/types/is_swappable" since="c++17"/>
    <variable name="std::is_nothrow_swappable_with_v" link="cpp/types/is_swappable" since="c++17"/>

    <class name="std::has_virtual_destructor" link="cpp/types/has_virtual_destructor" since="c++11"/>
    <variable name="std::has_virtual_destructor_v" link="cpp/types/has_virtual_destructor" since="c++17"/>

    <class name="std::alignment_of" link="cpp/types/alignment_of" since="c++11"/>
    <variable name="std::alignment_of_v" link="cpp/types/alignment_of" since="c++17"/>
    <class name="std::rank" link="cpp/types/rank" since="c++11"/>
    <variable name="std::rank_v" link="cpp/types/rank" since="c++17"/>
    <class name="std::extent" link="cpp/types/extent" since="c++11"/>
    <variable name="std::extent_v" link="cpp/types/extent" since="c++17"/>

    <class name="std::is_same" link="cpp/types/is_same" since="c++11"/>
    <variable name="std::is_same_v" link="cpp/types/is_same" since="c++17"/>
    <class name="std::is_base_of" link="cpp/types/is_base_of" since="c++11"/>
    <variable name="std::is_base_of_v" link="cpp/types/is_base_of" since="c++17"/>
    <class name="std::is_convertible" link="cpp/types/is_convertible" since="c++11"/>
    <variable name="std::is_convertible_v" link="cpp/types/is_convertible" since="c++17"/>
    <class name="std::is_nothrow_convertible" link="cpp/types/is_convertible" since="c++20"/>
    <variable name="std::is_nothrow_convertible_v" link="cpp/types/is_convertible" since="c++20"/>
    <class name="std::is_invocable" link="cpp/types/is_invocable" since="c++17"/>
    <variable name="std::is_invocable_v" link="cpp/types/is_invocable" since="c++17"/>
    <class name="std::is_invocable_r" link="cpp/types/is_invocable" since="c++17"/>
    <variable name="std::is_invocable_r_v" link="cpp/types/is_invocable" since="c++17"/>
    <class name="std::is_nothrow_invocable" link="cpp/types/is_invocable" since="c++17"/>
    <variable name="std::is_nothrow_invocable_v" link="cpp/types/is_invocable" since="c++17"/>
    <class name="std::is_nothrow_invocable_r" link="cpp/types/is_invocable" since="c++17"/>
    <variable name="std::is_nothrow_invocable_r_v" link="cpp/types/is_invocable" since="c++17"/>

    <class name="std::remove_cv" link="cpp/types/remove_cv" since="c++11"/>
    <class name="std::remove_const" link="cpp/types/remove_cv" since="c++11"/>
    <class name="std::remove_volatile" link="cpp/types/remove_cv" since="c++11"/>
    <typedef name="std::remove_cv_t" alias="std::remove_cv" since="c++14"/>
    <typedef name="std::remove_const_t" alias="std::remove_const" since="c++14"/>
    <typedef name="std::remove_volatile_t" alias="std::remove_volatile" since="c++14"/>

    <class name="std::add_cv" link="cpp/types/add_cv" since="c++11"/>
    <class name="std::add_const" link="cpp/types/add_cv" since="c++11"/>
    <class name="std::add_volatile" link="cpp/types/add_cv" since="c++11"/>
    <typedef name="std::add_cv_t" alias="std::add_cv" since="c++14"/>
    <typedef name="std::add_const_t" alias="std::add_const" since="c++14"/>
    <typedef name="std::add_volatile_t" alias="std::add_volatile" since="c++14"/>

    <class name="std::remove_reference" link="cpp/types/remove_reference" since="c++11"/>
    <typedef name="std::remove_reference_t" alias="std::remove_reference" since="c++14"/>
    <class name="std::add_lvalue_reference" link="cpp/types/add_reference" since="c++11"/>
    <typedef name="std::add_lvalue_reference_t" alias="std::add_lvalue_reference" since="c++14"/>
    <class name="std::add_rvalue_reference" link="cpp/types/add_reference" since="c++11"/>
    <typedef name="std::add_rvalue_reference_t" alias="std::add_rvalue_reference" since="c++14"/>

    <class name="std::remove_pointer" link="cpp/types/remove_pointer" since="c++11"/>
    <typedef name="std::remove_pointer_t" alias="std::remove_pointer" since="c++14"/>
    <class name="std::add_pointer" link="cpp/types/add_pointer" since="c++11"/>
    <typedef name="std::add_pointer_t" alias="std::add_pointer" since="c++14"/>

    <class name="std::make_signed" link="cpp/types/make_signed" since="c++11"/>
    <typedef name="std::make_signed_t" alias="std::make_signed" since="c++14"/>
    <class name="std::make_unsigned" link="cpp/types/make_unsigned" since="c++11"/>
    <typedef name="std::make_unsigned_t" alias="std::make_unsigned" since="c++14"/>

    <class name="std::remove_extent" link="cpp/types/remove_extent" since="c++11"/>
    <typedef name="std::remove_extent_t" alias="std::remove_extent" since="c++14"/>
    <class name="std::remove_all_extents" link="cpp/types/remove_all_extents" since="c++11"/>
    <typedef name="std::remove_all_extents_t" alias="std::remove_all_extents" since="c++14"/>

    <class name="std::aligned_storage" link="cpp/types/aligned_storage" since="c++11"/>
    <typedef name="std::aligned_storage_t" alias="std::aligned_storage" since="c++14"/>
    <class name="std::aligned_union" link="cpp/types/aligned_union" since="c++11"/>
    <typedef name="std::aligned_union_t" alias="std::aligned_union" since="c++14"/>
    <class name="std::decay" link="cpp/types/decay" since="c++11"/>
    <typedef name="std::decay_t" alias="std::decay" since="c++14"/>
    <class name="std::remove_cvref" link="cpp/types/remove_cvref" since="c++20"/>
    <typedef name="std::remove_cvref_t" alias="std::remove_cvref" since="c++20"/>
    <class name="std::enable_if" link="cpp/types/enable_if" since="c++11"/>
    <typedef name="std::enable_if_t" alias="std::enable_if" since="c++14"/>
    <class name="std::conditional" link="cpp/types/conditional" since="c++11"/>
    <typedef name="std::conditional_t" alias="std::conditional" since="c++14"/>
    <class name="std::common_type" link="cpp/types/common_type" since="c++11"/>
    <typedef name="std::common_type_t" alias="std::common_type" since="c++14"/>
    <class name="std::common_reference" link="cpp/types/common_reference" since="c++20"/>
    <typedef name="std::common_reference_t" alias="std::common_reference" since="c++20"/>
    <class name="std::basic_common_reference" link="cpp/types/common_reference" since="c++20"/>
    <class name="std::underlying_type" link="cpp/types/underlying_type" since="c++11"/>
    <typedef name="std::underlying_type_t" alias="std::underlying_type" since="c++14"/>
    <class name="std::result_of" link="cpp/types/result_of" since="c++11"/>
    <typedef name="std::result_of_t" alias="std::result_of" since="c++14"/>
    <class name="std::invoke_result" link="cpp/types/result_of" since="c++17"/>
    <typedef name="std::invoke_result_t" alias="std::invoke_result" since="c++17"/>
    <typedef name="std::void_t" link="cpp/types/void_t" since="c++17"/>
    <class name="std::type_identity" link="cpp/types/type_identity" since="c++20"/>
    <typedef name="std::type_identity_t" alias="std::type_identity" since="c++20"/>

    <class name="std::conjunction" link="cpp/types/conjunction" since="c++17"/>
    <variable name="std::conjunction_v" link="cpp/types/conjunction" since="c++17"/>
    <class name="std::disjunction" link="cpp/types/disjunction" since="c++17"/>
    <variable name="std::disjunction_v" link="cpp/types/disjunction" since="c++17"/>
    <class name="std::negation" link="cpp/types/negation" since="c++17"/>
    <variable name="std::negation_v" link="cpp/types/negation" since="c++17"/>

    <class name="std::integral_constant" link="cpp/types/integral_constant" since="c++11"/>
    <typedef name="std::bool_constant" alias="std::integral_constant" since="c++17"/>
    <typedef name="std::true_type" alias="std::integral_constant" since="c++11"/>
    <typedef name="std::false_type" alias="std::integral_constant" since="c++11"/>

    <enum name="std::endian" link="cpp/types/endian" since="c++20">
        <const name="little" link="."/>
        <const name="big" link="."/>
        <const name="native" link="."/>
    </enum>

    <function name="std::is_constant_evaluated" link="cpp/types/is_constant_evaluated" since="c++20"/>

    <!--=======================================================================-->
    <!-- cpp/iterator -->

    <class name="std::iterator_traits" link="cpp/iterator/iterator_traits"/>
    <class name="std::input_iterator_tag" link="cpp/iterator/iterator_tags"/>
    <class name="std::output_iterator_tag" link="cpp/iterator/iterator_tags"/>
    <class name="std::forward_iterator_tag" link="cpp/iterator/iterator_tags"/>
    <class name="std::bidirectional_iterator_tag" link="cpp/iterator/iterator_tags"/>
    <class name="std::random_access_iterator_tag" link="cpp/iterator/iterator_tags"/>
    <class name="std::iterator" link="cpp/iterator/iterator"/>

    <class name="std::incrementable_traits" link="cpp/iterator/incrementable_traits" since="c++20"/>
    <class name="std::readable_traits" link="cpp/iterator/readable_traits" since="c++20"/>
    <typedef name="std::iter_value_t" link="cpp/iterator/iter_t" since="c++20"/>
    <typedef name="std::iter_reference_t" link="cpp/iterator/iter_t" since="c++20"/>
    <typedef name="std::iter_difference_t" link="cpp/iterator/iter_t" since="c++20"/>
    <typedef name="std::iter_rvalue_reference_t" link="cpp/iterator/iter_t" since="c++20"/>
    <typedef name="std::iter_common_reference_t" link="cpp/iterator/iter_t" since="c++20"/>

    <class name="std::reverse_iterator" link="cpp/iterator/reverse_iterator">
        <constructor/>
        <function name="operator="/>
        <function name="base"/>

        <function name="operator*"/>
        <function name="operator-&gt;" link="operator*"/>
        <function name="operator[]" link="operator_at"/>

        <function name="operator++" link="operator_arith"/>
        <function name="operator++(int)" link="operator_arith"/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator+" link="operator_arith"/>
        <function name="operator--" link="operator_arith"/>
        <function name="operator--(int)" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="operator-" link="operator_arith"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
        <overload name="operator+" link="operator+"/>
        <overload name="operator-" link="operator-"/>
    </class>

    <class name="std::move_iterator" link="cpp/iterator/move_iterator" since="c++11">
        <constructor/>
        <function name="operator="/>
        <function name="base"/>

        <function name="operator*"/>
        <function name="operator-&gt;" link="operator*"/>
        <function name="operator[]" link="operator_at"/>

        <function name="operator++" link="operator_arith"/>
        <function name="operator++(int)" link="operator_arith"/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator+" link="operator_arith"/>
        <function name="operator--" link="operator_arith"/>
        <function name="operator--(int)" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="operator-" link="operator_arith"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
        <overload name="operator+" link="operator+"/>
        <overload name="operator-" link="operator-"/>
    </class>

    <class name="std::back_insert_iterator" link="cpp/iterator/back_insert_iterator">
        <constructor/>
        <function name="operator="/>

        <function name="operator*"/>
        <function name="operator++"/>
        <function name="operator++(int)" link="operator++"/>

        <!-- protected -->
        <variable name="container" link="."/>
    </class>

    <class name="std::front_insert_iterator" link="cpp/iterator/front_insert_iterator">
        <constructor/>
        <function name="operator="/>

        <function name="operator*"/>
        <function name="operator++"/>
        <function name="operator++(int)" link="operator++"/>

        <!-- protected -->
        <variable name="container" link="."/>
    </class>

    <class name="std::insert_iterator" link="cpp/iterator/insert_iterator">
        <constructor/>
        <function name="operator="/>

        <function name="operator*"/>
        <function name="operator++" link="operator++"/>
        <function name="operator++(int)" link="operator++"/>

        <!-- protected -->
        <variable name="container" link="."/>
        <variable name="iter" link="."/>
    </class>

    <function name="std::make_reverse_iterator" link="cpp/iterator/make_reverse_iterator" since="c++14"/>
    <function name="std::make_move_iterator" link="cpp/iterator/make_move_iterator" since="c++11"/>
    <function name="std::back_inserter" link="cpp/iterator/back_inserter"/>
    <function name="std::front_inserter" link="cpp/iterator/front_inserter"/>
    <function name="std::inserter" link="cpp/iterator/inserter"/>

    <class name="std::istream_iterator" link="cpp/iterator/istream_iterator">
        <constructor/>
        <destructor/>

        <function name="operator*"/>
        <function name="operator-&gt;" link="operator*"/>
        <function name="operator++" link="operator_arith"/>
        <function name="operator++(int)" link="operator_arith"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
    </class>

    <class name="std::istreambuf_iterator" link="cpp/iterator/istreambuf_iterator">
        <constructor/>
        <destructor link="."/>

        <function name="operator*"/>
        <function name="operator-&gt;" link="operator*"/>
        <function name="operator++" link="operator_arith"/>
        <function name="operator++(int)" link="operator_arith"/>
        <function name="equal"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
    </class>

    <class name="std::ostream_iterator" link="cpp/iterator/ostream_iterator">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="operator*"/>
        <function name="operator++" link="operator_arith"/>
        <function name="operator++(int)" link="operator_arith"/>
    </class>

    <class name="std::ostreambuf_iterator" link="cpp/iterator/ostreambuf_iterator">
        <constructor/>
        <destructor link="."/>
        <function name="operator="/>

        <function name="operator*"/>
        <function name="operator++" link="operator_arith"/>
        <function name="operator++(int)" link="operator_arith"/>
        <function name="failed"/>
    </class>

    <function name="std::advance" link="cpp/iterator/advance"/>
    <function name="std::distance" link="cpp/iterator/distance"/>
    <function name="std::next" link="cpp/iterator/next" since="c++11"/>
    <function name="std::prev" link="cpp/iterator/prev" since="c++11"/>
    <function name="std::begin" link="cpp/iterator/begin" since="c++11"/>
    <function name="std::cbegin" link="cpp/iterator/begin" since="c++14"/>
    <function name="std::rbegin" link="cpp/iterator/rbegin" since="c++14"/>
    <function name="std::crbegin" link="cpp/iterator/rbegin" since="c++14"/>
    <function name="std::end" link="cpp/iterator/end" since="c++11"/>
    <function name="std::cend" link="cpp/iterator/end" since="c++14"/>
    <function name="std::rend" link="cpp/iterator/rend" since="c++14"/>
    <function name="std::crend" link="cpp/iterator/rend" since="c++14"/>

    <function name="std::size" link="cpp/iterator/size" since="c++17"/>
    <function name="std::ssize" link="cpp/iterator/size" since="c++20"/>
    <function name="std::empty" link="cpp/iterator/empty" since="c++17"/>
    <function name="std::data" link="cpp/iterator/data" since="c++17"/>

    <function name="std::ranges::begin" link="cpp/ranges/begin" since="c++20"/>
    <function name="std::ranges::cbegin" link="cpp/ranges/begin" since="c++20"/>

    <!--=======================================================================-->
    <!-- cpp/utility/memory -->

        <!-- low level stuff -->

    <function name="operator new" link="cpp/memory/new/operator_new"/>
    <function name="operator new[]" link="cpp/memory/new/operator_new"/>
    <function name="operator delete" link="cpp/memory/new/operator_delete"/>
    <function name="operator delete[]" link="cpp/memory/new/operator_delete"/>

    <function name="std::get_new_handler" link="cpp/memory/new/get_new_handler" since="c++11"/>
    <function name="std::set_new_handler" link="cpp/memory/new/set_new_handler"/>

    <class name="std::bad_alloc" link="cpp/memory/new/bad_alloc">
        <inherits name="std::exception"/>
        <constructor link="."/>
        <function name="operator=" link="."/>
        <function name="what" link="."/>
    </class>

    <class name="std::bad_array_new_length" link="cpp/memory/new/bad_array_new_length" since="c++11">
        <inherits name="std::bad_alloc"/>
        <constructor link="."/>
    </class>

    <class name="std::nothrow_t" link="cpp/memory/new/nothrow_t"/>
    <class name="std::align_val_t" link="cpp/memory/new/align_val_t" since="c++11"/>
    <typedef name="std::new_handler" link="cpp/memory/new/new_handler"/>

    <const name="std::nothrow" link="cpp/memory/new/nothrow"/>

        <!-- high level stuff -->

    <class name="std::allocator" link="cpp/memory/allocator">
        <constructor/>
        <destructor/>
        <function name="address"/>
        <function name="allocate"/>
        <function name="deallocate"/>
        <function name="max_size"/>
        <function name="construct"/>
        <function name="destroy"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
    </class>

    <class name="std::allocator_traits" link="cpp/memory/allocator_traits" since="c++11">
        <typedef name="rebind_alloc" link="."/>
        <typedef name="rebind_traits" link="."/>
        <function name="allocate"/>
        <function name="deallocate"/>
        <function name="max_size"/>
        <function name="construct"/>
        <function name="destroy"/>
        <function name="select_on_container_copy_construction"/>
    </class>

    <class name="std::allocator_arg_t" link="cpp/memory/allocator_arg_t" since="c++11"/>
    <const name="std::allocator_arg" link="cpp/memory/allocator_arg" since="c++11"/>

    <class name="std::uses_allocator" link="cpp/memory/uses_allocator" since="c++11"/>
    <variable name="std::uses_allocator_v" link="cpp/memory/uses_allocator" since="c++17"/>
    <function name="std::uses_allocator_construction_args" link="cpp/memory/uses_allocator_construction_args" since="c++20"/>
    <function name="std::make_obj_using_allocator" link="cpp/memory/make_obj_using_allocator" since="c++20"/>
    <function name="std::uninitialized_construct_using_allocator" link="cpp/memory/uninitialized_construct_using_allocator" since="c++20"/>

    <class name="std::scoped_allocator_adaptor" link="cpp/memory/scoped_allocator_adaptor" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="inner_allocator"/>
        <function name="outer_allocator"/>
        <function name="allocate"/>
        <function name="deallocate"/>
        <function name="max_size"/>
        <function name="construct"/>
        <function name="destroy"/>
        <function name="select_on_container_copy_construction"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
    </class>

    <class name="std::pmr::polymorphic_allocator" link="cpp/memory/polymorphic_allocator" since="c++17">
        <constructor/>
        <destructor link="."/>
        <function name="allocate"/>
        <function name="deallocate"/>
        <function name="construct"/>
        <function name="destroy"/>
        <function name="select_on_container_copy_construction"/>
        <function name="resource"/>

        <overload name="operator==" link="operator_eq"/>
        <overload name="operator!=" link="operator_eq"/>
    </class>

    <class name="std::pmr::memory_resource" link="cpp/memory/memory_resource" since="c++17">
        <constructor/>
        <function name="allocate"/>
        <function name="deallocate"/>
        <function name="is_equal"/>
        <function name="do_allocate"/>
        <function name="do_deallocate"/>
        <function name="do_is_equal"/>

        <overload name="operator==" link="operator_eq"/>
        <overload name="operator!=" link="operator_eq"/>
    </class>

    <class name="std::pmr::pool_options" link="cpp/memory/pool_options" since="c++17">
        <variable name="max_blocks_per_chunk" link="."/>
        <variable name="largest_required_pool_block" link="."/>
    </class>

    <class name="std::pmr::synchronized_pool_resource" link="cpp/memory/synchronized_pool_resource" since="c++17">
        <inherits name="std::pmr::memory_resource"/>
        <constructor/>
        <destructor/>
        <function name="release"/>
        <function name="upstream_resource"/>
        <function name="options"/>
        <function name="do_allocate"/>
        <function name="do_deallocate"/>
        <function name="do_is_equal"/>
    </class>

    <class name="std::pmr::unsynchronized_pool_resource" link="cpp/memory/unsynchronized_pool_resource" since="c++17">
        <inherits name="std::pmr::memory_resource"/>
        <constructor/>
        <destructor/>
        <function name="release"/>
        <function name="upstream_resource"/>
        <function name="options"/>
        <function name="do_allocate"/>
        <function name="do_deallocate"/>
        <function name="do_is_equal"/>
    </class>

    <class name="std::pmr::monotonic_buffer_resource" link="cpp/memory/monotonic_buffer_resource" since="c++17">
        <inherits name="std::pmr::memory_resource"/>
        <constructor/>
        <destructor/>
        <function name="release"/>
        <function name="upstream_resource"/>
        <function name="do_allocate"/>
        <function name="do_deallocate"/>
        <function name="do_is_equal"/>
    </class>

    <function name="std::pmr::new_delete_resource" link="cpp/memory/new_delete_resource" since="c++17"/>
    <function name="std::pmr::null_memory_resource" link="cpp/memory/null_memory_resource" since="c++17"/>
    <function name="std::pmr::get_default_resource" link="cpp/memory/get_default_resource" since="c++17"/>
    <function name="std::pmr::set_default_resource" link="cpp/memory/set_default_resource" since="c++17"/>

    <function name="std::uninitialized_copy" link="cpp/memory/uninitialized_copy"/>
    <function name="std::uninitialized_copy_n" link="cpp/memory/uninitialized_copy_n"/>
    <function name="std::uninitialized_fill" link="cpp/memory/uninitialized_fill"/>
    <function name="std::uninitialized_fill_n" link="cpp/memory/uninitialized_fill_n"/>

    <function name="std::uninitialized_move" link="cpp/memory/uninitialized_move" since="c++17"/>
    <function name="std::uninitialized_move_n" link="cpp/memory/uninitialized_move_n" since="c++17"/>
    <function name="std::uninitialized_default_construct" link="cpp/memory/uninitialized_default_construct" since="c++17"/>
    <function name="std::uninitialized_default_construct_n" link="cpp/memory/uninitialized_default_construct_n" since="c++17"/>
    <function name="std::uninitialized_value_construct" link="cpp/memory/uninitialized_value_construct" since="c++17"/>
    <function name="std::uninitialized_value_construct_n" link="cpp/memory/uninitialized_value_construct_n" since="c++17"/>

    <function name="std::destroy" link="cpp/memory/destroy" since="c++17"/>
    <function name="std::destroy_at" link="cpp/memory/destroy_at" since="c++17"/>
    <function name="std::destroy_n" link="cpp/memory/destroy_n" since="c++17"/>
    <function name="std::construct_at" link="cpp/memory/construct_at" since="c++20"/>

    <function name="std::ranges::uninitialized_copy" link="cpp/memory/ranges/uninitialized_copy" since="c++20"/>
    <function name="std::ranges::uninitialized_copy_n" link="cpp/memory/ranges/uninitialized_copy_n" since="c++20"/>
    <function name="std::ranges::uninitialized_fill" link="cpp/memory/ranges/uninitialized_fill" since="c++20"/>
    <function name="std::ranges::uninitialized_fill_n" link="cpp/memory/ranges/uninitialized_fill_n" since="c++20"/>

    <function name="std::ranges::uninitialized_move" link="cpp/memory/ranges/uninitialized_move" since="c++20"/>
    <function name="std::ranges::uninitialized_move_n" link="cpp/memory/ranges/uninitialized_move_n" since="c++20"/>
    <function name="std::ranges::uninitialized_default_construct" link="cpp/memory/ranges/uninitialized_default_construct" since="c++20"/>
    <function name="std::ranges::uninitialized_default_construct_n" link="cpp/memory/ranges/uninitialized_default_construct_n" since="c++20"/>
    <function name="std::ranges::uninitialized_value_construct" link="cpp/memory/ranges/uninitialized_value_construct" since="c++20"/>
    <function name="std::ranges::uninitialized_value_construct_n" link="cpp/memory/ranges/uninitialized_value_construct_n" since="c++20"/>

    <function name="std::ranges::destroy" link="cpp/memory/ranges/destroy" since="c++20"/>
    <function name="std::ranges::destroy_at" link="cpp/memory/ranges/destroy_at" since="c++20"/>
    <function name="std::ranges::destroy_n" link="cpp/memory/ranges/destroy_n" since="c++20"/>
    <function name="std::ranges::construct_at" link="cpp/memory/ranges/construct_at" since="c++20"/>

    <class name="std::raw_storage_iterator" link="cpp/memory/raw_storage_iterator">
        <constructor/>
        <function name="operator*"/>
        <function name="operator="/>
        <function name="operator++" link="operator_arith"/>
    </class>

    <function name="std::get_temporary_buffer" link="cpp/memory/get_temporary_buffer"/>
    <function name="std::return_temporary_buffer" link="cpp/memory/return_temporary_buffer"/>

    <class name="std::unique_ptr" link="cpp/memory/unique_ptr" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="release"/>
        <function name="reset"/>
        <function name="swap"/>
        <function name="get"/>
        <function name="get_deleter"/>
        <function name="operator*"/>
        <function name="operator->" link="operator*"/>
        <function name="operator[]" link="operator_at"/>
        <function name="operator bool" link="operator_bool"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt" since="c++20"/>

        <overload name="std::swap" link="swap2"/>
        <specialization name="std::hash" link="hash"/>
    </class>

    <class name="std::shared_ptr" link="cpp/memory/shared_ptr" since="c++11">
        <typedef name="weak_type" link="." since="c++17"/>
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="reset"/>
        <function name="swap"/>
        <function name="get"/>
        <function name="operator*"/>
        <function name="operator->" link="operator*"/>
        <function name="operator[]" link="operator_at"/>
        <function name="unique"/>
        <function name="use_count"/>
        <function name="operator bool" link="operator_bool"/>
        <function name="owner_before"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::swap" link="swap2"/>
        <overload name="std::get_deleter" link="get_deleter"/>

        <overload name="std::atomic_is_lock_free" link="atomic"/>
        <overload name="std::atomic_load" link="atomic"/>
        <overload name="std::atomic_load_explicit" link="atomic"/>
        <overload name="std::atomic_store" link="atomic"/>
        <overload name="std::atomic_store_explicit" link="atomic"/>
        <overload name="std::atomic_exchange" link="atomic"/>
        <overload name="std::atomic_exchange_explicit" link="atomic"/>
        <overload name="std::atomic_compare_exchange_weak" link="atomic"/>
        <overload name="std::atomic_compare_exchange_strong" link="atomic"/>
        <overload name="std::atomic_compare_exchange_weak_explicit" link="atomic"/>
        <overload name="std::atomic_compare_exchange_strong_explicit" link="atomic"/>
        <specialization name="std::hash" link="hash"/>
    </class>

    <function name="std::make_unique" link="cpp/memory/unique_ptr/make_unique" since="c++14"/>
    <function name="std::make_unique_for_overwrite" link="cpp/memory/unique_ptr/make_unique" since="c++20"/>
    <function name="std::make_shared" link="cpp/memory/shared_ptr/make_shared" since="c++11"/>
    <function name="std::make_shared_for_overwrite" link="cpp/memory/shared_ptr/make_shared" since="c++20"/>
    <function name="std::allocate_shared" link="cpp/memory/shared_ptr/allocate_shared" since="c++11"/>
    <function name="std::allocate_shared_for_overwrite" link="cpp/memory/shared_ptr/allocate_shared" since="c++20"/>
    <function name="std::static_pointer_cast" link="cpp/memory/shared_ptr/pointer_cast" since="c++11"/>
    <function name="std::dynamic_pointer_cast" link="cpp/memory/shared_ptr/pointer_cast" since="c++11"/>
    <function name="std::const_pointer_cast" link="cpp/memory/shared_ptr/pointer_cast" since="c++11"/>
    <function name="std::reinterpret_pointer_cast" link="cpp/memory/shared_ptr/pointer_cast" since="c++17"/>

    <class name="std::weak_ptr" link="cpp/memory/weak_ptr" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="reset"/>
        <function name="swap"/>
        <function name="use_count"/>
        <function name="expired"/>
        <function name="lock"/>
        <function name="owner_before"/>

        <overload name="std::swap" link="swap"/>
    </class>

    <class name="std::auto_ptr" link="cpp/memory/auto_ptr">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="reset"/>
        <function name="release"/>
        <function name="get"/>
        <function name="operator*"/>
        <function name="operator->" link="operator*"/>

        <function name="operator auto_ptr&lt;Y&gt;" link="operator_auto_ptr"/>
    </class>

    <class name="std::owner_less" link="cpp/memory/owner_less" since="c++11">
        <function name="operator()" link="."/>
    </class>

    <class name="std::enable_shared_from_this" link="cpp/memory/enable_shared_from_this" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="shared_from_this"/>
        <function name="weak_from_this" since="c++17"/>
    </class>

    <class name="std::bad_weak_ptr" link="cpp/memory/bad_weak_ptr" since="c++11">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <class name="std::default_delete" link="cpp/memory/default_delete" since="c++11">
        <constructor link="."/>
        <function name="operator()" link="."/>
    </class>

    <function name="std::declare_reachable" link="cpp/memory/gc/declare_reachable" since="c++11"/>
    <function name="std::undeclare_reachable" link="cpp/memory/gc/undeclare_reachable" since="c++11"/>
    <function name="std::declare_no_pointers" link="cpp/memory/gc/declare_no_pointers" since="c++11"/>
    <function name="std::undeclare_no_pointers" link="cpp/memory/gc/undeclare_no_pointers" since="c++11"/>

    <class name="std::pointer_safety" link="cpp/memory/gc/pointer_safety" since="c++11">
        <const name="relaxed" link="."/>
        <const name="preferred" link="."/>
        <const name="strict" link="."/>
    </class>
    <function name="std::get_pointer_safety" link="cpp/memory/gc/get_pointer_safety" since="c++11"/>

    <class name="std::pointer_traits" link="cpp/memory/pointer_traits" since="c++11">
        <typedef name="rebind" link="."/>
        <function name="pointer_to"/>
        <function name="to_address"/>
    </class>

    <function name="std::to_address" link="cpp/memory/to_address" since="c++20"/>
    <function name="std::addressof" link="cpp/memory/addressof" since="c++11"/>
    <function name="std::align" link="cpp/memory/align" since="c++11"/>
    <function name="std::assume_aligned" link="cpp/memory/assume_aligned" since="c++20"/>

    <function name="std::malloc" link="cpp/memory/c/malloc"/>
    <function name="std::aligned_alloc" link="cpp/memory/c/aligned_alloc" since="c++17"/>
    <function name="std::calloc" link="cpp/memory/c/calloc"/>
    <function name="std::realloc" link="cpp/memory/c/realloc"/>
    <function name="std::free" link="cpp/memory/c/free"/>

    <!--=======================================================================-->
    <!-- cpp/utility/error -->

    <class name="std::exception" link="cpp/error/exception">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="what"/>
    </class>

    <class name="std::logic_error" link="cpp/error/logic_error">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <class name="std::domain_error" link="cpp/error/domain_error">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <class name="std::length_error" link="cpp/error/length_error">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <class name="std::out_of_range" link="cpp/error/out_of_range">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <class name="std::invalid_argument" link="cpp/error/invalid_argument">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <class name="std::runtime_error" link="cpp/error/runtime_error">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <class name="std::range_error" link="cpp/error/range_error">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <class name="std::overflow_error" link="cpp/error/overflow_error">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <class name="std::underflow_error" link="cpp/error/underflow_error">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <function name="assert" link="cpp/error/assert"/>

    <function name="std::uncaught_exception" link="cpp/error/uncaught_exception"/>
    <function name="std::uncaught_exceptions" link="cpp/error/uncaught_exception" since="c++17"/>
    <typedef name="std::exception_ptr" link="cpp/error/exception_ptr" since="c++11"/>
    <function name="std::make_exception_ptr" link="cpp/error/make_exception_ptr" since="c++11"/>
    <function name="std::current_exception" link="cpp/error/current_exception" since="c++11"/>
    <function name="std::rethrow_exception" link="cpp/error/rethrow_exception" since="c++11"/>

    <class name="std::nested_exception" link="cpp/error/nested_exception" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="rethrow_nested"/>
        <function name="nested_ptr"/>
    </class>
    <function name="std::throw_with_nested" link="cpp/error/throw_with_nested" since="c++11"/>
    <function name="std::rethrow_if_nested" link="cpp/error/rethrow_if_nested" since="c++11"/>

    <function name="std::terminate" link="cpp/error/terminate"/>
    <typedef name="std::terminate_handler" link="cpp/error/terminate_handler"/>
    <function name="std::get_terminate" link="cpp/error/get_terminate" since="c++11"/>
    <function name="std::set_terminate" link="cpp/error/set_terminate"/>

    <function name="std::unexpected" link="cpp/error/unexpected"/>
    <class name="std::bad_exception" link="cpp/error/bad_exception"/>
    <typedef name="std::unexpected_handler" link="cpp/error/unexpected_handler"/>
    <function name="std::get_unexpected" link="cpp/error/get_unexpected" since="c++11"/>
    <function name="std::set_unexpected" link="cpp/error/set_unexpected"/>

    <const name="errno" link="cpp/error/errno"/>
    <const name="E2BIG" link="cpp/error/errno_macros"/>
    <const name="EACCESS" link="cpp/error/errno_macros"/>
    <const name="EADDRINUSE" link="cpp/error/errno_macros"/>
    <const name="EADDRNOTAVAIL" link="cpp/error/errno_macros"/>
    <const name="EAFNOSUPPORT" link="cpp/error/errno_macros"/>
    <const name="EAGAIN" link="cpp/error/errno_macros"/>
    <const name="EALREADY" link="cpp/error/errno_macros"/>
    <const name="EBADF" link="cpp/error/errno_macros"/>
    <const name="EBADMSG" link="cpp/error/errno_macros"/>
    <const name="EBUSY" link="cpp/error/errno_macros"/>
    <const name="ECANCELED" link="cpp/error/errno_macros"/>
    <const name="ECHILD" link="cpp/error/errno_macros"/>
    <const name="ECONNABORTED" link="cpp/error/errno_macros"/>
    <const name="ECONNREFUSED" link="cpp/error/errno_macros"/>
    <const name="ECONNRESET" link="cpp/error/errno_macros"/>
    <const name="EDEADLK" link="cpp/error/errno_macros"/>
    <const name="EDESTADDRREQ" link="cpp/error/errno_macros"/>
    <const name="EDOM" link="cpp/error/errno_macros"/>
    <const name="EEXIST" link="cpp/error/errno_macros"/>
    <const name="EFAULT" link="cpp/error/errno_macros"/>
    <const name="EFBIG" link="cpp/error/errno_macros"/>
    <const name="EHOSTUNREACH" link="cpp/error/errno_macros"/>
    <const name="EIDRM" link="cpp/error/errno_macros"/>
    <const name="EILSEQ" link="cpp/error/errno_macros"/>
    <const name="EINPROGRESS" link="cpp/error/errno_macros"/>
    <const name="EINTR" link="cpp/error/errno_macros"/>
    <const name="EINVAL" link="cpp/error/errno_macros"/>
    <const name="EIO" link="cpp/error/errno_macros"/>
    <const name="EISCONN" link="cpp/error/errno_macros"/>
    <const name="EISDIR" link="cpp/error/errno_macros"/>
    <const name="ELOOP" link="cpp/error/errno_macros"/>
    <const name="EMFILE" link="cpp/error/errno_macros"/>
    <const name="EMLINK" link="cpp/error/errno_macros"/>
    <const name="EMSGSIZE" link="cpp/error/errno_macros"/>
    <const name="ENAMETOOLONG" link="cpp/error/errno_macros"/>
    <const name="ENETDOWN" link="cpp/error/errno_macros"/>
    <const name="ENETRESET" link="cpp/error/errno_macros"/>
    <const name="ENETUNREACH" link="cpp/error/errno_macros"/>
    <const name="ENFILE" link="cpp/error/errno_macros"/>
    <const name="ENOBUFS" link="cpp/error/errno_macros"/>
    <const name="ENODATA" link="cpp/error/errno_macros"/>
    <const name="ENODEV" link="cpp/error/errno_macros"/>
    <const name="ENOENT" link="cpp/error/errno_macros"/>
    <const name="ENOEXEC" link="cpp/error/errno_macros"/>
    <const name="ENOLCK" link="cpp/error/errno_macros"/>
    <const name="ENOLINK" link="cpp/error/errno_macros"/>
    <const name="ENOMEM" link="cpp/error/errno_macros"/>
    <const name="ENOMSG" link="cpp/error/errno_macros"/>
    <const name="ENOPROTOOPT" link="cpp/error/errno_macros"/>
    <const name="ENOSPC" link="cpp/error/errno_macros"/>
    <const name="ENOSR" link="cpp/error/errno_macros"/>
    <const name="ENOSTR" link="cpp/error/errno_macros"/>
    <const name="ENOSYS" link="cpp/error/errno_macros"/>
    <const name="ENOTCONN" link="cpp/error/errno_macros"/>
    <const name="ENOTDIR" link="cpp/error/errno_macros"/>
    <const name="ENOTEMPTY" link="cpp/error/errno_macros"/>
    <const name="ENOTRECOVERABLE" link="cpp/error/errno_macros"/>
    <const name="ENOTSOCK" link="cpp/error/errno_macros"/>
    <const name="ENOTSUP" link="cpp/error/errno_macros"/>
    <const name="ENOTTY" link="cpp/error/errno_macros"/>
    <const name="ENXIO" link="cpp/error/errno_macros"/>
    <const name="EOPNOTSUPP" link="cpp/error/errno_macros"/>
    <const name="EOVERFLOW" link="cpp/error/errno_macros"/>
    <const name="EOWNERDEAD" link="cpp/error/errno_macros"/>
    <const name="EPERM" link="cpp/error/errno_macros"/>
    <const name="EPIPE" link="cpp/error/errno_macros"/>
    <const name="EPROTO" link="cpp/error/errno_macros"/>
    <const name="EPROTONOSUPPORT" link="cpp/error/errno_macros"/>
    <const name="EPROTOTYPE" link="cpp/error/errno_macros"/>
    <const name="ERANGE" link="cpp/error/errno_macros"/>
    <const name="EROFS" link="cpp/error/errno_macros"/>
    <const name="ESPIPE" link="cpp/error/errno_macros"/>
    <const name="ESRCH" link="cpp/error/errno_macros"/>
    <const name="ETIME" link="cpp/error/errno_macros"/>
    <const name="ETIMEDOUT" link="cpp/error/errno_macros"/>
    <const name="ETXTBSY" link="cpp/error/errno_macros"/>
    <const name="EWOULDBLOCK" link="cpp/error/errno_macros"/>
    <const name="EXDEV" link="cpp/error/errno_macros"/>

    <class name="std::error_category" link="cpp/error/error_category" since="c++11">
        <constructor/>
        <destructor/>
        <function name="name"/>
        <function name="default_error_condition"/>
        <function name="equivalent"/>
        <function name="message"/>
        <function name="operator==" link="operator_cmp"/>
        <function name="operator!=" link="operator_cmp"/>
        <function name="operator&lt;" link="operator_cmp"/>
    </class>

    <function name="std::generic_category" link="cpp/error/generic_category" since="c++11"/>
    <function name="std::system_category" link="cpp/error/system_category" since="c++11"/>

    <class name="std::error_condition" link="cpp/error/error_condition" since="c++11">
        <constructor/>
        <function name="operator="/>
        <function name="assign"/>
        <function name="clear"/>
        <function name="value"/>
        <function name="category"/>
        <function name="message"/>
        <function name="operator bool" link="operator_bool"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>

        <specialization name="std::hash" link="hash" since="c++17"/>
    </class>

    <class name="std::is_error_condition_enum" link="cpp/error/error_condition/is_error_condition_enum" since="c++11"/>
    <variable name="std::is_error_condition_enum_v" link="cpp/error/error_condition/is_error_condition_enum" since="c++17"/>

    <class name="std::error_code" link="cpp/error/error_code" since="c++11">
        <constructor/>
        <function name="operator="/>
        <function name="assign"/>
        <function name="clear"/>
        <function name="value"/>
        <function name="category"/>
        <function name="default_error_condition"/>
        <function name="message"/>
        <function name="operator bool" link="operator_bool"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>

        <specialization name="std::hash" link="hash"/>
    </class>

    <class name="std::is_error_code_enum" link="cpp/error/error_code/is_error_code_enum" since="c++11"/>
    <class name="std::is_error_code_enum_v" link="cpp/error/error_code/is_error_code_enum" since="c++17"/>

    <class name="std::errc" link="cpp/error/errc" since="c++11">
        <const name="address_family_not_supported" link="."/>
        <const name="address_in_use" link="."/>
        <const name="address_not_available" link="."/>
        <const name="already_connected" link="."/>
        <const name="argument_list_too_long" link="."/>
        <const name="argument_out_of_domain" link="."/>
        <const name="bad_address" link="."/>
        <const name="bad_file_descriptor" link="."/>
        <const name="bad_message" link="."/>
        <const name="broken_pipe" link="."/>
        <const name="connection_aborted" link="."/>
        <const name="connection_already_in_progress" link="."/>
        <const name="connection_refused" link="."/>
        <const name="connection_reset" link="."/>
        <const name="cross_device_link" link="."/>
        <const name="destination_address_required" link="."/>
        <const name="device_or_resource_busy" link="."/>
        <const name="directory_not_empty" link="."/>
        <const name="executable_format_error" link="."/>
        <const name="file_exists" link="."/>
        <const name="file_too_large" link="."/>
        <const name="filename_too_long" link="."/>
        <const name="function_not_supported" link="."/>
        <const name="host_unreachable" link="."/>
        <const name="identifier_removed" link="."/>
        <const name="illegal_byte_sequence" link="."/>
        <const name="inappropriate_io_control_operation" link="."/>
        <const name="interrupted" link="."/>
        <const name="invalid_argument" link="."/>
        <const name="invalid_seek" link="."/>
        <const name="io_error" link="."/>
        <const name="is_a_directory" link="."/>
        <const name="message_size" link="."/>
        <const name="network_down" link="."/>
        <const name="network_reset" link="."/>
        <const name="network_unreachable" link="."/>
        <const name="no_buffer_space" link="."/>
        <const name="no_child_process" link="."/>
        <const name="no_link" link="."/>
        <const name="no_lock_available" link="."/>
        <const name="no_message_available" link="."/>
        <const name="no_message" link="."/>
        <const name="no_protocol_option" link="."/>
        <const name="no_space_on_device" link="."/>
        <const name="no_stream_resources" link="."/>
        <const name="no_such_device_or_address" link="."/>
        <const name="no_such_device" link="."/>
        <const name="no_such_file_or_directory" link="."/>
        <const name="no_such_process" link="."/>
        <const name="not_a_directory" link="."/>
        <const name="not_a_socket" link="."/>
        <const name="not_a_stream" link="."/>
        <const name="not_connected" link="."/>
        <const name="not_enough_memory" link="."/>
        <const name="not_supported" link="."/>
        <const name="operation_canceled" link="."/>
        <const name="operation_in_progress" link="."/>
        <const name="operation_not_permitted" link="."/>
        <const name="operation_not_supported" link="."/>
        <const name="operation_would_block" link="."/>
        <const name="owner_dead" link="."/>
        <const name="permission_denied" link="."/>
        <const name="protocol_error" link="."/>
        <const name="protocol_not_supported" link="."/>
        <const name="read_only_file_system" link="."/>
        <const name="resource_deadlock_would_occur" link="."/>
        <const name="resource_unavailable_try_again" link="."/>
        <const name="result_out_of_range" link="."/>
        <const name="state_not_recoverable" link="."/>
        <const name="stream_timeout" link="."/>
        <const name="text_file_busy" link="."/>
        <const name="timed_out" link="."/>
        <const name="too_many_files_open_in_system" link="."/>
        <const name="too_many_files_open" link="."/>
        <const name="too_many_links" link="."/>
        <const name="too_many_symbolic_link_levels" link="."/>
        <const name="value_too_large" link="."/>
        <const name="wrong_protocol_type" link="."/>

        <specialization name="std::is_error_condition_enum" link="is_error_condition_enum"/>
        <overload name="std::make_error_code" link="make_error_code"/>
        <overload name="std::make_error_condition" link="make_error_condition"/>
    </class>

    <class name="std::system_error" link="cpp/error/system_error">
        <inherits name="std::runtime_error"/>
        <constructor/>
        <function name="code"/>
        <function name="what"/>
    </class>

    <!--=======================================================================-->
    <!-- cpp/utility/source_location -->

    <class name="std::source_location" link="cpp/utility/source_location" since="c++20">
        <constructor/>
        <function name="current"/>

        <function name="line"/>
        <function name="column"/>
        <function name="file_name"/>
        <function name="function_name"/>
    </class>

    <!--=======================================================================-->
    <!-- cpp/utility/initializer_list -->

    <class name="std::initializer_list" link="cpp/utility/initializer_list" since="c++11">
        <constructor/>
        <function name="size"/>
        <function name="begin"/>
        <function name="end"/>

        <overload name="std::begin" link="begin2"/>
        <overload name="std::end" link="end2"/>
        <overload name="std::rbegin" link="rbegin2" since="c++14"/>
        <overload name="std::rend" link="rend2" since="c++14"/>
    </class>

    <!--=======================================================================-->
    <!-- cpp/coroutine -->

    <class name="std::coroutine_traits" link="cpp/coroutine/coroutine_traits" since="c++20">
        <typedef name="promise_type" link="."/>
    </class>

    <class name="std::coroutine_handle" link="cpp/coroutine/coroutine_handle" since="c++20">
        <constructor/>
        <function name="operator="/>
        <function name="from_promise"/>

        <function name="operator coroutine_handle<>" link="operator_coroutine_handle_void"/>

        <function name="done"/>
        <function name="operator bool" link="operator_bool"/>

        <function name="operator()" link="resume"/>
        <function name="resume"/>
        <function name="destroy"/>

        <function name="promise"/>

        <function name="address"/>
        <function name="from_address"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp"/>

        <specialization name="std::hash" link="hash"/>
    </class>

    <class name="std::noop_coroutine_promise" link="cpp/coroutine/noop_coroutine_promise" since="c++20"/>
    <typedef name="std::noop_coroutine_handle" link="cpp/coroutine/coroutine_handle" since="c++20"/>
    <function name="std::noop_coroutine" link="cpp/coroutine/noop_coroutine" since="c++20"/>

    <class name="std::suspend_never" link="cpp/coroutine/suspend_never" since="c++20">
        <function name="await_ready" link="."/>
        <function name="await_suspend" link="."/>
        <function name="await_resume" link="."/>
    </class>

    <class name="std::suspend_always" link="cpp/coroutine/suspend_always" since="c++20">
        <function name="await_ready" link="."/>
        <function name="await_suspend" link="."/>
        <function name="await_resume" link="."/>
    </class>

    <!--=======================================================================-->
    <!-- cpp/utility/variadic -->

    <function name="va_start" link="cpp/utility/variadic/va_start"/>
    <function name="va_copy" link="cpp/utility/variadic/va_copy"/>
    <function name="va_arg" link="cpp/utility/variadic/va_arg"/>
    <function name="va_end" link="cpp/utility/variadic/va_end"/>
    <class name="va_list" link="cpp/utility/variadic/va_list"/>

    <!--=======================================================================-->
    <!-- cpp/utility/program -->

    <function name="std::abort" link="cpp/utility/program/abort"/>
    <function name="std::exit" link="cpp/utility/program/exit"/>
    <function name="std::quick_exit" link="cpp/utility/program/quick_exit" since="c++11"/>
    <function name="std::_Exit" link="cpp/utility/program/_Exit" since="c++11"/>
    <function name="std::atexit" link="cpp/utility/program/atexit"/>
    <function name="std::at_quick_exit" link="cpp/utility/program/at_quick_exit" since="c++11"/>

    <const name="EXIT_SUCCESS" link="cpp/utility/program/EXIT_status"/>
    <const name="EXIT_FAILURE" link="cpp/utility/program/EXIT_status"/>

    <function name="std::system" link="cpp/utility/program/system"/>
    <function name="std::getenv" link="cpp/utility/program/getenv"/>

    <function name="std::signal" link="cpp/utility/program/signal"/>
    <function name="std::raise" link="cpp/utility/program/raise"/>
    <typedef name="std::sig_atomic_t" link="cpp/utility/program/sig_atomic_t"/>

    <const name="SIG_DFL" link="cpp/utility/program/SIG_strategies"/>
    <const name="SIG_IGN" link="cpp/utility/program/SIG_strategies"/>
    <const name="SIG_ERR" link="cpp/utility/program/SIG_ERR"/>

    <const name="SIGABRT" link="cpp/utility/program/SIG_types"/>
    <const name="SIGFPE" link="cpp/utility/program/SIG_types"/>
    <const name="SIGILL" link="cpp/utility/program/SIG_types"/>
    <const name="SIGINT" link="cpp/utility/program/SIG_types"/>
    <const name="SIGSEGV" link="cpp/utility/program/SIG_types"/>
    <const name="SIGTERM" link="cpp/utility/program/SIG_types"/>

    <function name="std::longjmp" link="cpp/utility/program/longjmp"/>
    <function name="setjmp" link="cpp/utility/program/setjmp"/>
    <typedef name="std::jmp_buf" link="cpp/utility/program/jmp_buf"/>

    <!--=======================================================================-->
    <!-- cpp/chrono -->

    <class name="std::chrono::time_point" link="cpp/chrono/time_point" since="c++11">
        <constructor/>
        <function name="time_since_epoch"/>
        <function name="min"/>
        <function name="max"/>

        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>

        <function name="operator++" link="operator_inc_dec" since="c++20"/>
        <function name="operator--" link="operator_inc_dec" since="c++20"/>

        <overload name="operator+" link="operator_arith2"/>
        <overload name="operator-" link="operator_arith2"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::chrono::floor" link="floor" since="c++17"/>
        <overload name="std::chrono::ceil" link="ceil" since="c++17"/>
        <overload name="std::chrono::round" link="round" since="c++17"/>

        <specialization name="std::common_type" link="common_type"/>
    </class>

    <function name="std::chrono::time_point_cast" link="cpp/chrono/time_point/time_point_cast" since="c++11"/>

    <class name="std::chrono::duration" link="cpp/chrono/duration" since="c++11">
        <constructor/>
        <function name="operator="/>
        <function name="count"/>
        <function name="zero"/>
        <function name="min"/>
        <function name="max"/>

        <function name="operator+" link="operator_arith"/>
        <function name="operator-" link="operator_arith"/>

        <function name="operator++" link="operator_arith2"/>
        <function name="operator++(int)" link="operator_arith2"/>
        <function name="operator--" link="operator_arith2"/>
        <function name="operator--(int)" link="operator_arith2"/>

        <function name="operator+=" link="operator_arith3"/>
        <function name="operator-=" link="operator_arith3"/>
        <function name="operator*=" link="operator_arith3"/>
        <function name="operator/=" link="operator_arith3"/>
        <function name="operator%=" link="operator_arith3"/>

        <overload name="operator+" link="operator_arith4"/>
        <overload name="operator-" link="operator_arith4"/>
        <overload name="operator*" link="operator_arith4"/>
        <overload name="operator/" link="operator_arith4"/>
        <overload name="operator%" link="operator_arith4"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp" since="c++20"/>

        <overload name="std::chrono::floor" link="floor" since="c++17"/>
        <overload name="std::chrono::ceil" link="ceil" since="c++17"/>
        <overload name="std::chrono::round" link="round" since="c++17"/>
        <overload name="std::chrono::abs" link="abs" since="c++17"/>

        <overload name="operator&lt;&lt;" link="operator_ltlt" since="c++20"/>
        <overload name="std::chrono::from_stream" link="from_stream" since="c++20"/>

        <specialization name="std::common_type" link="common_type"/>

        <specialization name="std::formatter" link="formatter"/>
    </class>

    <typedef name="std::chrono::nanoseconds" alias="std::chrono::duration" since="c++11"/>
    <typedef name="std::chrono::microseconds" alias="std::chrono::duration" since="c++11"/>
    <typedef name="std::chrono::milliseconds" alias="std::chrono::duration" since="c++11"/>
    <typedef name="std::chrono::seconds" alias="std::chrono::duration" since="c++11"/>
    <typedef name="std::chrono::minutes" alias="std::chrono::duration" since="c++11"/>
    <typedef name="std::chrono::hours" alias="std::chrono::duration" since="c++11"/>
    <typedef name="std::chrono::days" alias="std::chrono::duration" since="c++20"/>
    <typedef name="std::chrono::weeks" alias="std::chrono::duration" since="c++20"/>
    <typedef name="std::chrono::months" alias="std::chrono::duration" since="c++20"/>
    <typedef name="std::chrono::years" alias="std::chrono::duration" since="c++20"/>

    <function name="std::chrono::duration_cast" link="cpp/chrono/duration/duration_cast" since="c++11"/>

    <class name="std::chrono::treat_as_floating_point" link="cpp/chrono/treat_as_floating_point" since="c++11">
    </class>
    <variable name="std::chrono::treat_as_floating_point_v" link="cpp/chrono/treat_as_floating_point" since="c++11"/>

    <class name="std::chrono::duration_values" link="cpp/chrono/duration_values" since="c++11">
        <function name="zero"/>
        <function name="min"/>
        <function name="max"/>
    </class>

    <function name="std::literals::chrono_literals::operator&quot;&quot;h" link="cpp/chrono/operator&quot;&quot;h" since="c++14"/>
    <function name="std::literals::chrono_literals::operator&quot;&quot;min" link="cpp/chrono/operator&quot;&quot;min" since="c++14"/>
    <function name="std::literals::chrono_literals::operator&quot;&quot;s" link="cpp/chrono/operator&quot;&quot;s" since="c++14"/>
    <function name="std::literals::chrono_literals::operator&quot;&quot;ms" link="cpp/chrono/operator&quot;&quot;ms" since="c++14"/>
    <function name="std::literals::chrono_literals::operator&quot;&quot;us" link="cpp/chrono/operator&quot;&quot;us" since="c++14"/>
    <function name="std::literals::chrono_literals::operator&quot;&quot;ns" link="cpp/chrono/operator&quot;&quot;ns" since="c++14"/>

    <class name="std::chrono::system_clock" link="cpp/chrono/system_clock" since="c++11">
        <const name="is_steady" link="."/>
        <function name="now"/>
        <function name="to_time_t"/>
        <function name="from_time_t"/>
    </class>

    <class name="std::chrono::sys_time" link="cpp/chrono/system_clock" since="c++20">
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>
    <typedef name="std::chrono::sys_seconds" alias="std::chrono::sys_time" since="c++20"/>
    <typedef name="std::chrono::sys_days" alias="std::chrono::sys_time" since="c++20"/>

    <class name="std::chrono::steady_clock" link="cpp/chrono/steady_clock" since="c++11">
        <const name="is_steady" link="."/>
        <function name="now"/>
    </class>

    <class name="std::chrono::high_resolution_clock" link="cpp/chrono/high_resolution_clock" since="c++11">
        <const name="is_steady" link="."/>
        <function name="now"/>
    </class>

    <class name="std::chrono::utc_clock" link="cpp/chrono/utc_clock" since="c++20">
        <const name="is_steady" link="."/>
        <function name="now"/>
        <function name="to_sys"/>
        <function name="from_sys"/>
    </class>

    <class name="std::chrono::utc_time" link="cpp/chrono/utc_clock" since="c++20">
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>
    <typedef name="std::chrono::utc_seconds" alias="std::chrono::utc_time" since="c++20"/>

    <class name="std::chrono::tai_clock" link="cpp/chrono/tai_clock" since="c++20">
        <const name="is_steady" link="."/>
        <function name="now"/>
        <function name="to_utc"/>
        <function name="from_utc"/>
    </class>

    <class name="std::chrono::tai_time" link="cpp/chrono/tai_clock" since="c++20">
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>
    <typedef name="std::chrono::tai_seconds" alias="std::chrono::tai_time" since="c++20"/>

    <class name="std::chrono::gps_clock" link="cpp/chrono/gps_clock" since="c++20">
        <const name="is_steady" link="."/>
        <function name="now"/>
        <function name="to_utc"/>
        <function name="from_utc"/>
    </class>

    <class name="std::chrono::gps_time" link="cpp/chrono/gps_clock" since="c++20">
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>
    <typedef name="std::chrono::gps_seconds" alias="std::chrono::gps_time" since="c++20"/>

    <class name="std::chrono::file_clock" link="cpp/chrono/file_clock" since="c++20">
        <const name="is_steady" link="."/>
        <function name="now"/>
        <function name="to_utc" link="to_from_utc"/>
        <function name="from_utc" link="to_from_utc"/>
        <function name="to_sys" link="to_from_sys"/>
        <function name="from_sys" link="to_from_sys"/>
    </class>

    <class name="std::chrono::file_time" link="cpp/chrono/file_clock" since="c++20">
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::local_t" link="cpp/chrono/local_t" since="c++20"/>

    <class name="std::chrono::local_time" link="cpp/chrono/local_t" since="c++20">
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>
    <typedef name="std::chrono::local_seconds" alias="std::chrono::local_time" since="c++20"/>
    <typedef name="std::chrono::local_days" alias="std::chrono::local_time" since="c++20"/>

    <class name="std::chrono::is_clock" link="cpp/chrono/is_clock" since="c++20"/>
    <const name="std::chrono::is_clock_v" link="cpp/chrono/is_clock" since="c++20"/>

    <class name="std::chrono::clock_time_conversion" link="cpp/chrono/clock_time_conversion" since="c++20"/>
    <function name="std::chrono::clock_cast" link="cpp/chrono/clock_cast" since="c++20"/>

    <class name="std::chrono::hh_mm_ss" link="cpp/chrono/hh_mm_ss" since="c++20">
        <const name="fractional_width" link="."/>
        <typedef name="precision" link="."/>
        <constructor/>
        <function name="is_negative" link="accessors"/>
        <function name="hours" link="accessors"/>
        <function name="minutes" link="accessors"/>
        <function name="seconds" link="accessors"/>
        <function name="subseconds" link="accessors"/>
        <function name="operator precision" link="duration"/>
        <function name="to_duration" link="duration"/>

        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>
    <function name="std::chrono::is_am" link="cpp/chrono/hour_fun" since="c++20"/>
    <function name="std::chrono::is_pm" link="cpp/chrono/hour_fun" since="c++20"/>
    <function name="std::chrono::make12" link="cpp/chrono/hour_fun" since="c++20"/>
    <function name="std::chrono::make24" link="cpp/chrono/hour_fun" since="c++20"/>

    <class name="std::chrono::last_spec" link="cpp/chrono/last_spec" since="c++20"/>
    <class name="std::chrono::last" link="cpp/chrono/last_spec" since="c++20"/>

    <class name="std::chrono::day" link="cpp/chrono/day" since="c++20">
        <constructor/>
        <function name="operator++" link="operator_inc_dec"/>
        <function name="operator++(int)" link="operator_inc_dec"/>
        <function name="operator--" link="operator_inc_dec"/>
        <function name="operator--(int)" link="operator_inc_dec"/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="operator unsigned" link="operator_unsigned"/>
        <function name="ok"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp"/>
        <overload name="operator+" link="operator_arith_2"/>
        <overload name="operator-" link="operator_arith_2"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>

        <specialization name="std::formatter" link="formatter"/>
    </class>
    <function name="std::literals::chrono_literals::operator&quot;&quot;d" link="cpp/chrono/operator&quot;&quot;d" since="c++20"/>

    <class name="std::chrono::month" link="cpp/chrono/month" since="c++20">
        <constructor/>
        <function name="operator++" link="operator_inc_dec"/>
        <function name="operator++(int)" link="operator_inc_dec"/>
        <function name="operator--" link="operator_inc_dec"/>
        <function name="operator--(int)" link="operator_inc_dec"/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="operator unsigned" link="operator_unsigned"/>
        <function name="ok"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp"/>
        <overload name="operator+" link="operator_arith_2"/>
        <overload name="operator-" link="operator_arith_2"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>

        <specialization name="std::formatter" link="formatter"/>
    </class>
    <const name="std::chrono::January" link="cpp/chrono/month" since="c++20"/>
    <const name="std::chrono::February" link="cpp/chrono/month" since="c++20"/>
    <const name="std::chrono::March" link="cpp/chrono/month" since="c++20"/>
    <const name="std::chrono::April" link="cpp/chrono/month" since="c++20"/>
    <const name="std::chrono::May" link="cpp/chrono/month" since="c++20"/>
    <const name="std::chrono::June" link="cpp/chrono/month" since="c++20"/>
    <const name="std::chrono::July" link="cpp/chrono/month" since="c++20"/>
    <const name="std::chrono::August" link="cpp/chrono/month" since="c++20"/>
    <const name="std::chrono::September" link="cpp/chrono/month" since="c++20"/>
    <const name="std::chrono::October" link="cpp/chrono/month" since="c++20"/>
    <const name="std::chrono::November" link="cpp/chrono/month" since="c++20"/>
    <const name="std::chrono::December" link="cpp/chrono/month" since="c++20"/>

    <class name="std::chrono::year" link="cpp/chrono/year" since="c++20">
        <constructor/>
        <function name="operator++" link="operator_inc_dec"/>
        <function name="operator++(int)" link="operator_inc_dec"/>
        <function name="operator--" link="operator_inc_dec"/>
        <function name="operator--(int)" link="operator_inc_dec"/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="operator+" link="operator_sign"/>
        <function name="operator-" link="operator_sign"/>
        <function name="is_leap"/>
        <function name="operator int" link="operator_int"/>
        <function name="ok"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp"/>
        <overload name="operator+" link="operator_arith_2"/>
        <overload name="operator-" link="operator_arith_2"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>

        <specialization name="std::formatter" link="formatter"/>
    </class>
    <function name="std::literals::chrono_literals::operator&quot;&quot;y" link="cpp/chrono/operator&quot;&quot;y" since="c++20"/>

    <class name="std::chrono::weekday" link="cpp/chrono/weekday" since="c++20">
        <constructor/>
        <function name="operator++" link="operator_inc_dec"/>
        <function name="operator++(int)" link="operator_inc_dec"/>
        <function name="operator--" link="operator_inc_dec"/>
        <function name="operator--(int)" link="operator_inc_dec"/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="c_encoding" link="encoding"/>
        <function name="iso_encoding" link="encoding"/>
        <function name="ok"/>
        <function name="operator[]" link="operator_at"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator+" link="operator_arith_2"/>
        <overload name="operator-" link="operator_arith_2"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>

        <specialization name="std::formatter" link="formatter"/>
    </class>
    <const name="std::chrono::Sunday" link="cpp/chrono/weekday" since="c++20"/>
    <const name="std::chrono::Monday" link="cpp/chrono/weekday" since="c++20"/>
    <const name="std::chrono::Tuesday" link="cpp/chrono/weekday" since="c++20"/>
    <const name="std::chrono::Wednesday" link="cpp/chrono/weekday" since="c++20"/>
    <const name="std::chrono::Thursday" link="cpp/chrono/weekday" since="c++20"/>
    <const name="std::chrono::Friday" link="cpp/chrono/weekday" since="c++20"/>
    <const name="std::chrono::Saturday" link="cpp/chrono/weekday" since="c++20"/>

    <class name="std::chrono::weekday_indexed" link="cpp/chrono/weekday_indexed" since="c++20">
        <constructor/>
        <function name="weekday"/>
        <function name="index"/>
        <function name="ok"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::weekday_last" link="cpp/chrono/weekday_last" since="c++20">
        <constructor/>
        <function name="weekday"/>
        <function name="ok"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::month_day" link="cpp/chrono/month_day" since="c++20">
        <constructor/>
        <function name="month" link="accessors"/>
        <function name="day" link="accessors"/>
        <function name="ok"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::month_day_last" link="cpp/chrono/month_day_last" since="c++20">
        <constructor/>
        <function name="month"/>
        <function name="ok"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::month_weekday" link="cpp/chrono/month_weekday" since="c++20">
        <constructor/>
        <function name="month" link="accessors"/>
        <function name="weekday_indexed" link="accessors"/>
        <function name="ok"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::month_weekday_last" link="cpp/chrono/month_weekday_last" since="c++20">
        <constructor/>
        <function name="month" link="accessors"/>
        <function name="weekday_last" link="accessors"/>
        <function name="ok"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::year_month" link="cpp/chrono/year_month" since="c++20">
        <constructor/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="year" link="accessors"/>
        <function name="month" link="accessors"/>
        <function name="ok"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp"/>
        <overload name="operator+" link="operator_arith_2"/>
        <overload name="operator-" link="operator_arith_2"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::year_month_day" link="cpp/chrono/year_month_day" since="c++20">
        <constructor/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="year" link="accessors"/>
        <function name="month" link="accessors"/>
        <function name="day" link="accessors"/>
        <function name="operator sys_days" link="operator_days"/>
        <function name="operator local_days" link="operator_days"/>
        <function name="ok"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp"/>
        <overload name="operator+" link="operator_arith_2"/>
        <overload name="operator-" link="operator_arith_2"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <overload name="std::chrono::from_stream" link="from_stream"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::year_month_day_last" link="cpp/chrono/year_month_day_last" since="c++20">
        <constructor/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="year" link="accessors"/>
        <function name="month" link="accessors"/>
        <function name="day" link="accessors"/>
        <function name="month_day_last" link="accessors"/>
        <function name="operator sys_days" link="operator_days"/>
        <function name="operator local_days" link="operator_days"/>
        <function name="ok"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp"/>
        <overload name="operator+" link="operator_arith_2"/>
        <overload name="operator-" link="operator_arith_2"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::year_month_weekday" link="cpp/chrono/year_month_weekday" since="c++20">
        <constructor/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="year" link="accessors"/>
        <function name="month" link="accessors"/>
        <function name="weekday" link="accessors"/>
        <function name="index" link="accessors"/>
        <function name="weekday_indexed" link="accessors"/>
        <function name="operator sys_days" link="operator_days"/>
        <function name="operator local_days" link="operator_days"/>
        <function name="ok"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator+" link="operator_arith_2"/>
        <overload name="operator-" link="operator_arith_2"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::year_month_weekday_last" link="cpp/chrono/year_month_weekday_last" since="c++20">
        <constructor/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="year" link="accessors"/>
        <function name="month" link="accessors"/>
        <function name="weekday" link="accessors"/>
        <function name="weekday_last" link="accessors"/>
        <function name="operator sys_days" link="operator_days"/>
        <function name="operator local_days" link="operator_days"/>
        <function name="ok"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator+" link="operator_arith_2"/>
        <overload name="operator-" link="operator_arith_2"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <function name="std::chrono::operator/" link="cpp/chrono/operator_slash" since="c++20"/>

    <class name="std::chrono::tzdb" link="cpp/chrono/tzdb" since="c++20">
        <function name="locate_zone"/>
        <function name="current_zone"/>
    </class>

    <class name="std::chrono::tzdb_list" link="cpp/chrono/tzdb_list" since="c++20">
        <function name="front"/>
        <function name="erase_after"/>
        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
    </class>

    <function name="std::chrono::get_tzdb_list" link="cpp/chrono/tzdb_functions" since="c++20"/>
    <function name="std::chrono::get_tzdb" link="cpp/chrono/tzdb_functions" since="c++20"/>
    <function name="std::chrono::remote_version" link="cpp/chrono/tzdb_functions" since="c++20"/>
    <function name="std::chrono::reload_tzdb" link="cpp/chrono/tzdb_functions" since="c++20"/>

    <function name="std::chrono::locate_zone" link="cpp/chrono/locate_zone" since="c++20"/>
    <function name="std::chrono::current_zone" link="cpp/chrono/current_zone" since="c++20"/>

    <class name="std::chrono::time_zone" link="cpp/chrono/time_zone" since="c++20">
        <function name="name"/>
        <function name="get_info"/>
        <function name="to_sys"/>
        <function name="to_local"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp"/>
    </class>

    <class name="std::chrono::sys_info" link="cpp/chrono/sys_info" since="c++20">
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::local_info" link="cpp/chrono/local_info" since="c++20">
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <enum name="std::chrono::choose" link="cpp/chrono/choose" since="c++20"/>

    <class name="std::chrono::zoned_traits" link="cpp/chrono/zoned_traits" since="c++20"/>

    <class name="std::chrono::zoned_time" link="cpp/chrono/zoned_time" since="c++20">
        <constructor/>
        <function name="operator="/>
        <function name="get_time_zone"/>
        <function name="operator local_time" link="get_local_time"/>
        <function name="get_local_time"/>
        <function name="operator sys_time" link="get_sys_time"/>
        <function name="get_sys_time"/>
        <function name="get_info"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::formatter" link="formatter"/>
    </class>

    <class name="std::chrono::leap_second" link="cpp/chrono/leap_second" since="c++20">
        <function name="date"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp"/>
    </class>

    <class name="std::chrono::leap_second_info" link="cpp/chrono/utc_clock/leap_second_info" since="c++20"/>
    <class name="std::chrono::get_leap_second_info" link="cpp/chrono/utc_clock/get_leap_second_info" since="c++20"/>

    <class name="std::chrono::time_zone_link" link="cpp/chrono/time_zone_link" since="c++20">
        <function name="name" link="accessors"/>
        <function name="target" link="accessors"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;=&gt;" link="operator_cmp"/>
    </class>

    <class name="std::chrono::nonexistent_local_time" link="cpp/chrono/nonexistent_local_time" since="c++20">
        <inherits name="std::runtime_error"/>
        <constructor link="."/>
        <function name="operator=" link="."/>
        <function name="what" link="."/>
    </class>

    <class name="std::chrono::ambiguous_local_time" link="cpp/chrono/ambiguous_local_time" since="c++20">
        <inherits name="std::runtime_error"/>
        <constructor link="."/>
        <function name="operator=" link="."/>
        <function name="what" link="."/>
    </class>

    <function name="std::chrono::parse" link="cpp/chrono/parse" since="c++20"/>

    <!-- cpp/chrono/c -->

    <function name="std::difftime" link="cpp/chrono/c/difftime"/>
    <function name="std::time" link="cpp/chrono/c/time"/>
    <function name="std::clock" link="cpp/chrono/c/clock"/>
    <function name="std::timespec_get" link="cpp/chrono/c/timespec_get" since="c++17"/>
    <const name="TIME_UTC" link="cpp/chrono/c/timespec_get" since="c++17"/>

    <function name="std::asctime" link="cpp/chrono/c/asctime"/>
    <function name="std::ctime" link="cpp/chrono/c/ctime"/>
    <function name="std::strftime" link="cpp/chrono/c/strftime"/>
    <function name="std::wcsftime" link="cpp/chrono/c/wcsftime"/>
    <function name="std::gmtime" link="cpp/chrono/c/gmtime"/>
    <function name="std::localtime" link="cpp/chrono/c/localtime"/>
    <function name="std::mktime" link="cpp/chrono/c/mktime"/>

    <const name="CLOCKS_PER_SEC" link="cpp/chrono/c/CLOCKS_PER_SEC"/>

    <class name="std::tm" link="cpp/chrono/c/tm"/>
    <typedef name="std::time_t" link="cpp/chrono/c/time_t"/>
    <typedef name="std::clock_t" link="cpp/chrono/c/clock_t"/>
    <function name="std::timespec" link="cpp/chrono/c/timespec" since="c++17"/>

    <!--=======================================================================-->
    <!-- cpp/utility/bitset -->

    <class name="std::bitset" link="cpp/utility/bitset">
        <constructor/>

        <class name="reference"/>

        <function name="operator==" link="operator_cmp"/>
        <function name="operator!=" link="operator_cmp"/>

        <function name="operator[]" link="operator_at"/>
        <function name="test"/>
        <function name="all" link="all_any_none"/>
        <function name="any" link="all_any_none"/>
        <function name="none" link="all_any_none"/>
        <function name="count"/>

        <function name="size"/>
        <function name="operator&amp;=" link="operator_logic"/>
        <function name="operator|=" link="operator_logic"/>
        <function name="operator^=" link="operator_logic"/>
        <function name="operator~" link="operator_logic"/>
        <function name="operator&lt;&lt;=" link="operator_ltltgtgt"/>
        <function name="operator&gt;&gt;=" link="operator_ltltgtgt"/>
        <function name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <function name="operator&gt;&gt;" link="operator_ltltgtgt"/>
        <function name="set"/>
        <function name="reset"/>
        <function name="flip"/>

        <function name="to_string"/>
        <function name="to_ulong"/>
        <function name="to_ullong"/>

        <overload name="operator&amp;" link="operator_logic2"/>
        <overload name="operator|" link="operator_logic2"/>
        <overload name="operator^" link="operator_logic2"/>
        <overload name="operator&lt;&lt;" link="operator_ltltgtgt2"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt2"/>

        <specialization name="std::hash" link="hash"/>
    </class>

    <!--=======================================================================-->
    <!-- cpp/utility/functional -->

    <class name="std::plus" link="cpp/utility/functional/plus">
        <function name="operator()" link="."/>
    </class>

    <class name="std::minus" link="cpp/utility/functional/minus">
        <function name="operator()" link="."/>
    </class>

    <class name="std::multiplies" link="cpp/utility/functional/multiplies">
        <function name="operator()" link="."/>
    </class>

    <class name="std::divides" link="cpp/utility/functional/divides">
        <function name="operator()" link="."/>
    </class>

    <class name="std::modulus" link="cpp/utility/functional/modulus">
        <function name="operator()" link="."/>
    </class>

    <class name="std::negate" link="cpp/utility/functional/negate">
        <function name="operator()" link="."/>
    </class>

    <class name="std::equal_to" link="cpp/utility/functional/equal_to">
        <function name="operator()" link="."/>
    </class>

    <class name="std::not_equal_to" link="cpp/utility/functional/not_equal_to">
        <function name="operator()" link="."/>
    </class>

    <class name="std::greater" link="cpp/utility/functional/greater">
        <function name="operator()" link="."/>
    </class>

    <class name="std::greater_equal" link="cpp/utility/functional/greater_equal">
        <function name="operator()" link="."/>
    </class>

    <class name="std::less" link="cpp/utility/functional/less">
        <function name="operator()" link="."/>
    </class>

    <class name="std::less_equal" link="cpp/utility/functional/less_equal">
        <function name="operator()" link="."/>
    </class>

    <class name="std::logical_and" link="cpp/utility/functional/logical_and">
        <function name="operator()" link="."/>
    </class>

    <class name="std::logical_or" link="cpp/utility/functional/logical_or">
        <function name="operator()" link="."/>
    </class>

    <class name="std::logical_not" link="cpp/utility/functional/logical_not">
        <function name="operator()" link="."/>
    </class>

    <class name="std::bit_and" link="cpp/utility/functional/bit_and">
        <function name="operator()" link="."/>
    </class>

    <class name="std::bit_or" link="cpp/utility/functional/bit_or">
        <function name="operator()" link="."/>
    </class>

    <class name="std::bit_xor" link="cpp/utility/functional/bit_xor">
        <function name="operator()" link="."/>
    </class>

    <class name="std::bit_not" link="cpp/utility/functional/bit_not" since="c++14">
        <function name="operator()" link="."/>
    </class>

    <function name="std::not_fn" link="cpp/utility/functional/not_fn" since="c++17"/>

    <class name="std::identity" link="cpp/utility/functional/identity" since="c++20">
        <function name="operator()" link="."/>
    </class>

    <function name="std::bind" link="cpp/utility/functional/bind" since="c++11"/>
    <function name="std::bind_front" link="cpp/utility/functional/bind_front" since="c++20"/>
    <class name="std::is_bind_expression" link="cpp/utility/functional/is_bind_expression" since="c++11"/>
    <class name="std::is_placeholder" link="cpp/utility/functional/is_placeholder" since="c++11"/>
    <variable name="std::is_bind_expression_v" link="cpp/utility/functional/is_bind_expression" since="c++17"/>
    <variable name="std::is_placeholder_v" link="cpp/utility/functional/is_placeholder" since="c++17"/>

    <function name="std::mem_fn" link="cpp/utility/functional/mem_fn" since="c++11"/>
    <function name="std::invoke" link="cpp/utility/functional/invoke" since="c++17"/>
    <class name="std::function" link="cpp/utility/functional/function" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="swap"/>
        <function name="assign"/>
        <function name="operator bool" link="operator_bool"/>
        <function name="operator()"/>
        <function name="target_type"/>
        <function name="target"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <specialization name="std::uses_allocator" link="uses_allocator"/>
    </class>

    <class name="std::bad_function_call" link="cpp/utility/functional/bad_function_call" since="c++11">
        <inherits name="std::exception"/>
        <constructor link="."/>
    </class>

    <class name="std::placeholders" link="cpp/utility/functional/placeholders" since="c++11"/>

    <class name="std::default_searcher" link="cpp/utility/functional/default_searcher" since="c++17">
        <constructor link="."/>
        <function name="operator()" link="."/>
    </class>
    <class name="std::boyer_moore_searcher" link="cpp/utility/functional/boyer_moore_searcher" since="c++17">
        <constructor link="."/>
        <function name="operator()" link="."/>
    </class>
    <class name="std::boyer_moore_horspool_searcher" link="cpp/utility/functional/boyer_moore_horspool_searcher" since="c++17">
        <constructor link="."/>
        <function name="operator()" link="."/>
    </class>

    <class name="std::reference_wrapper" link="cpp/utility/functional/reference_wrapper" since="c++11">
        <constructor/>
        <function name="operator="/>
        <function name="operator T&amp;" link="get"/>
        <function name="get"/>
        <function name="operator()"/>
    </class>

    <function name="std::ref" link="cpp/utility/functional/ref" since="c++11"/>
    <function name="std::cref" link="cpp/utility/functional/ref" since="c++11"/>

    <class name="std::unwrap_reference" link="cpp/utility/functional/unwrap_reference" since="c++20"/>
    <typedef name="std::unwrap_reference_t" alias="std::unwrap_reference" since="c++20"/>
    <class name="std::unwrap_ref_decay" link="cpp/utility/functional/unwrap_reference" since="c++20"/>
    <typedef name="std::unwrap_ref_decay_t" alias="std::unwrap_ref_decay" since="c++20"/>

    <class name="std::ranges::equal_to" link="cpp/utility/functional/ranges/equal_to" since="c++20">
        <typedef name="is_transparent" link="."/>
        <function name="operator()" link="."/>
    </class>

    <class name="std::ranges::not_equal_to" link="cpp/utility/functional/ranges/not_equal_to" since="c++20">
        <typedef name="is_transparent" link="."/>
        <function name="operator()" link="."/>
    </class>

    <class name="std::ranges::less" link="cpp/utility/functional/ranges/less" since="c++20">
        <typedef name="is_transparent" link="."/>
        <function name="operator()" link="."/>
    </class>

    <class name="std::ranges::greater" link="cpp/utility/functional/ranges/greater" since="c++20">
        <typedef name="is_transparent" link="."/>
        <function name="operator()" link="."/>
    </class>

    <class name="std::ranges::less_equal" link="cpp/utility/functional/ranges/less_equal" since="c++20">
        <typedef name="is_transparent" link="."/>
        <function name="operator()" link="."/>
    </class>

    <class name="std::ranges::greater_equal" link="cpp/utility/functional/ranges/greater_equal" since="c++20">
        <typedef name="is_transparent" link="."/>
        <function name="operator()" link="."/>
    </class>

    <class name="std::unary_function" link="cpp/utility/functional/unary_function"/>
    <class name="std::binary_function" link="cpp/utility/functional/binary_function"/>

    <class name="std::binder1st" link="cpp/utility/functional/binder12"/>
    <class name="std::binder2nd" link="cpp/utility/functional/binder12"/>
    <function name="std::bind1st" link="cpp/utility/functional/bind12"/>
    <function name="std::bind2nd" link="cpp/utility/functional/bind12"/>

    <class name="std::pointer_to_unary_function" link="cpp/utility/functional/pointer_to_unary_function"/>
    <class name="std::pointer_to_binary_function" link="cpp/utility/functional/pointer_to_binary_function"/>
    <function name="std::ptr_fun" link="cpp/utility/functional/ptr_fun"/>
    <class name="std::mem_fun_t" link="cpp/utility/functional/mem_fun_t"/>
    <class name="std::mem_fun1_t" link="cpp/utility/functional/mem_fun_t"/>
    <class name="std::const_mem_fun_t" link="cpp/utility/functional/mem_fun_t"/>
    <class name="std::const_mem_fun1_t" link="cpp/utility/functional/mem_fun_t"/>
    <function name="std::mem_fun" link="cpp/utility/functional/mem_fun"/>
    <class name="std::mem_fun_ref_t" link="cpp/utility/functional/mem_fun_ref_t"/>
    <class name="std::mem_fun1_ref_t" link="cpp/utility/functional/mem_fun_ref_t"/>
    <class name="std::const_mem_fun_ref_t" link="cpp/utility/functional/mem_fun_ref_t"/>
    <class name="std::const_mem_fun1_ref_t" link="cpp/utility/functional/mem_fun_ref_t"/>
    <function name="std::mem_fun_ref" link="cpp/utility/functional/mem_fun_ref"/>
    <class name="std::unary_negate" link="cpp/utility/functional/unary_negate">
        <constructor link="."/>
        <function name="operator()" link="."/>
    </class>

    <class name="std::binary_negate" link="cpp/utility/functional/binary_negate">
        <constructor link="."/>
        <function name="operator()" link="."/>
    </class>

    <function name="std::not1" link="cpp/utility/functional/not1"/>
    <function name="std::not2" link="cpp/utility/functional/not2"/>

    <!--=======================================================================-->
    <!-- cpp/utility/any -->

    <class name="std::any" link="cpp/utility/any" since="c++17">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="emplace"/>
        <function name="reset"/>
        <function name="swap"/>
        <function name="has_value"/>
        <function name="type"/>

        <overload name="std::swap" link="swap2"/>
    </class>

    <function name="std::any_cast" link="cpp/utility/any/any_cast" since="c++17"/>
    <function name="std::make_any" link="cpp/utility/any/make_any" since="c++17"/>

    <class name="std::bad_any_cast" link="cpp/utility/any/bad_any_cast" since="c++17">
    </class>

    <!--=======================================================================-->
    <!-- cpp/utility/optional -->
    <class name="std::optional" link="cpp/utility/optional" since="c++17">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="operator*"/>
        <function name="operator-&gt;" link="operator*"/>
        <function name="operator bool" link="operator_bool"/>
        <function name="has_value" link="operator_bool"/>
        <function name="value"/>
        <function name="value_or"/>
        <function name="swap"/>
        <function name="reset"/>
        <function name="emplace"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <specialization name="std::hash" link="hash"/>

    </class>
    <function name="std::make_optional" link="cpp/utility/optional/make_optional" since="c++17"/>
    <class name="std::bad_optional_access" link="cpp/utility/optional/bad_optional_access" since="c++17"/>

    <class name="std::nullopt_t" link="cpp/utility/optional/nullopt_t" since="c++17"/>
    <const name="std::nullopt" link="cpp/utility/optional/nullopt" since="c++17"/>

    <class name="std::in_place_t" link="cpp/utility/in_place" since="c++17"/>
    <variable name="std::in_place" link="cpp/utility/in_place" since="c++17"/>
    <class name="std::in_place_type_t" link="cpp/utility/in_place" since="c++17"/>
    <variable name="std::in_place_type" link="cpp/utility/in_place" since="c++17"/>
    <class name="std::in_place_index_t" link="cpp/utility/in_place" since="c++17"/>
    <variable name="std::in_place_index" link="cpp/utility/in_place" since="c++17"/>

    <!--=======================================================================-->
    <!-- cpp/utility/variant -->
    <class name="std::variant" link="cpp/utility/variant" since="c++17">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="index"/>
        <function name="valueless_by_exception"/>
        <function name="swap"/>
        <function name="emplace"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::get" link="get"/>
        <specialization name="std::hash" link="hash"/>
    </class>

    <function name="std::visit" link="cpp/utility/variant/visit" since="c++17"/>
    <function name="std::holds_alternative" link="cpp/utility/variant/holds_alternative" since="c++17"/>
    <function name="std::get_if" link="cpp/utility/variant/get_if" since="c++17"/>
    <class name="std::bad_variant_access" link="cpp/utility/variant/bad_variant_access" since="c++17"/>
    <const name="std::variant_npos" link="cpp/utility/variant/variant_npos" since="c++17"/>

    <class name="std::variant_size" link="cpp/utility/variant/variant_size" since="c++17"/>
    <variable name="std::variant_size_v" link="cpp/utility/variant/variant_size" since="c++17"/>
    <class name="std::variant_alternative" link="cpp/utility/variant/variant_alternative" since="c++17"/>
    <typedef name="std::variant_alternative_t" link="cpp/utility/variant/variant_alternative" since="c++17"/>

    <class name="std::monostate" link="cpp/utility/variant/monostate" since="c++17">
        <constructor link="."/>
        <destructor link="."/>
        <function name="operator=" link="."/>

        <overload name="operator==" link="."/>
        <overload name="operator!=" link="."/>
        <overload name="operator&lt;" link="."/>
        <overload name="operator&lt;=" link="."/>
        <overload name="operator&gt;" link="."/>
        <overload name="operator&gt;=" link="."/>

        <specialization name="std::hash" link="."/>
    </class>

    <!--=======================================================================-->
    <!-- cpp/utility/format -->

    <function name="std::format" link="cpp/utility/format/format" since="c++20"/>
    <function name="std::format_to" link="cpp/utility/format/format_to" since="c++20"/>
    <function name="std::format_to_n" link="cpp/utility/format/format_to_n" since="c++20"/>
    <class name="std::format_to_n_result" link="cpp/utility/format/format_to_n" since="c++20"/>
    <function name="std::formatted_size" link="cpp/utility/format/formatted_size" since="c++20"/>
    <function name="std::vformat" link="cpp/utility/format/vformat" since="c++20"/>
    <function name="std::vformat_to" link="cpp/utility/format/vformat_to" since="c++20"/>
    <class name="std::formatter" link="cpp/utility/format/formatter" since="c++20"/>
    <class name="std::basic_format_parse_context" link="cpp/utility/format/basic_format_parse_context" since="c++20"/>
    <typedef name="std::format_parse_context" alias="std::basic_format_parse_context" since="c++20"/>
    <typedef name="std::wformat_parse_context" alias="std::basic_format_parse_context" since="c++20"/>
    <class name="std::basic_format_context" link="cpp/utility/format/basic_format_context" since="c++20"/>
    <typedef name="std::format_context" alias="std::basic_format_context" since="c++20"/>
    <typedef name="std::wformat_context" alias="std::basic_format_context" since="c++20"/>
    <class name="std::basic_format_arg" link="cpp/utility/format/basic_format_arg" since="c++20"/>
    <class name="std::basic_format_args" link="cpp/utility/format/basic_format_args" since="c++20"/>
    <typedef name="std::format_args" alias="std::basic_format_args" since="c++20"/>
    <typedef name="std::wformat_args" alias="std::basic_format_args" since="c++20"/>
    <function name="std::visit_format_arg" link="cpp/utility/format/visit_format_arg" since="c++20"/>
    <function name="std::make_format_args" link="cpp/utility/format/make_format_args" since="c++20"/>
    <function name="std::make_wformat_args" link="cpp/utility/format/make_format_args" since="c++20"/>
    <class name="std::format_error" link="cpp/utility/format/format_error" since="c++20"/>

    <!--=======================================================================-->
    <!-- cpp/utility -->

    <class name="std::pair" link="cpp/utility/pair">
        <constructor/>
        <variable name="first" link="."/>
        <variable name="second" link="."/>
        <function name="operator="/>
        <function name="swap"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::get" link="get" since="c++11"/>

        <specialization name="std::tuple_size" link="tuple_size" since="c++11"/>
        <specialization name="std::tuple_element" link="tuple_element" since="c++11"/>
    </class>

    <function name="std::make_pair" link="cpp/utility/pair/make_pair"/>

    <class name="std::tuple" link="cpp/utility/tuple" since="c++11">
        <constructor/>
        <function name="operator="/>
        <function name="swap"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::get" link="get"/>

        <specialization name="std::tuple_size" link="tuple_size"/>
        <specialization name="std::tuple_element" link="tuple_element"/>
        <specialization name="std::uses_allocator" link="uses_allocator"/>
    </class>

    <variable name="std::tuple_size_v" link="cpp/utility/tuple/tuple_size" since="c++17"/>

    <function name="std::apply" link="cpp/utility/apply" since="c++17"/>
    <function name="std::make_from_tuple" link="cpp/utility/make_from_tuple" since="c++17"/>
    <class name="std::piecewise_construct_t" link="cpp/utility/piecewise_construct_t" since="c++11"/>
    <variable name="std::piecewise_construct" link="cpp/utility/piecewise_construct" since="c++11"/>

    <class name="std::integer_sequence" link="cpp/utility/integer_sequence" since="c++14"/>
    <typedef name="std::index_sequence" link="cpp/utility/integer_sequence" since="c++14"/>
    <typedef name="std::make_integer_sequence" link="cpp/utility/integer_sequence" since="c++14"/>
    <typedef name="std::make_index_sequence" link="cpp/utility/integer_sequence" since="c++14"/>
    <typedef name="std::index_sequence_for" link="cpp/utility/integer_sequence" since="c++14"/>

    <function name="std::make_tuple" link="cpp/utility/tuple/make_tuple" since="c++11"/>
    <function name="std::tie" link="cpp/utility/tuple/tie" since="c++11"/>
    <function name="std::forward_as_tuple" link="cpp/utility/tuple/forward_as_tuple" since="c++11"/>
    <function name="std::tuple_cat" link="cpp/utility/tuple/tuple_cat" since="c++11"/>
    <const name="std::ignore" link="cpp/utility/tuple/ignore" since="c++11"/>

    <function name="std::ranges::swap" link="cpp/utility/ranges/swap" since="c++20"/>
    <function name="std::forward" link="cpp/utility/forward" since="c++11"/>
    <function name="std::exchange" link="cpp/utility/exchange" since="c++14"/>
    <function name="std::move (utility)" link="cpp/utility/move" since="c++11"/>
    <function name="std::move_if_noexcept" link="cpp/utility/move_if_noexcept" since="c++11"/>
    <function name="std::declval" link="cpp/utility/declval" since="c++11"/>
    <function name="std::as_const" link="cpp/utility/as_const" since="c++17"/>
    <function name="std::launder" link="cpp/utility/launder" since="c++17"/>

    <class name="std::partial_ordering" link="cpp/utility/compare/partial_ordering" since="c++20">
        <const name="less" link="."/>
        <const name="equivalent" link="."/>
        <const name="greater" link="."/>
        <const name="unordered" link="."/>

        <overload name="operator==" link="."/>
        <overload name="operator!=" link="."/>
        <overload name="operator&lt;" link="."/>
        <overload name="operator&lt;=" link="."/>
        <overload name="operator&gt;" link="."/>
        <overload name="operator&gt;=" link="."/>
        <overload name="operator&lt;=&gt;" link="."/>
    </class>

    <class name="std::weak_ordering" link="cpp/utility/compare/weak_ordering" since="c++20">
        <const name="less" link="."/>
        <const name="equivalent" link="."/>
        <const name="greater" link="."/>

        <function name="operator partial_ordering" link="."/>

        <overload name="operator==" link="."/>
        <overload name="operator!=" link="."/>
        <overload name="operator&lt;" link="."/>
        <overload name="operator&lt;=" link="."/>
        <overload name="operator&gt;" link="."/>
        <overload name="operator&gt;=" link="."/>
        <overload name="operator&lt;=&gt;" link="."/>
    </class>

    <class name="std::strong_ordering" link="cpp/utility/compare/strong_ordering" since="c++20">
        <const name="less" link="."/>
        <const name="equivalent" link="."/>
        <const name="equal" link="."/>
        <const name="greater" link="."/>

        <function name="operator partial_ordering" link="."/>
        <function name="operator weak_ordering" link="."/>

        <overload name="operator==" link="."/>
        <overload name="operator!=" link="."/>
        <overload name="operator&lt;" link="."/>
        <overload name="operator&lt;=" link="."/>
        <overload name="operator&gt;" link="."/>
        <overload name="operator&gt;=" link="."/>
        <overload name="operator&lt;=&gt;" link="."/>
    </class>

    <function name="std::is_eq" link="cpp/utility/compare/named_comparison_functions" since="c++20"/>
    <function name="std::is_neq" link="cpp/utility/compare/named_comparison_functions" since="c++20"/>
    <function name="std::is_lt" link="cpp/utility/compare/named_comparison_functions" since="c++20"/>
    <function name="std::is_lteq" link="cpp/utility/compare/named_comparison_functions" since="c++20"/>
    <function name="std::is_gt" link="cpp/utility/compare/named_comparison_functions" since="c++20"/>
    <function name="std::is_gteq" link="cpp/utility/compare/named_comparison_functions" since="c++20"/>

    <class name="std::common_comparison_category" link="cpp/utility/compare/common_comparison_category" since="c++20"/>
    <typedef name="std::common_comparison_category_t" alias="std::common_comparison_category" since="c++20"/>

    <function name="std::strong_order" link="cpp/utility/compare/strong_order" since="c++20"/>
    <function name="std::weak_order" link="cpp/utility/compare/weak_order" since="c++20"/>
    <function name="std::partial_order" link="cpp/utility/compare/partial_order" since="c++20"/>

    <function name="std::rel_ops::operator!=" link="cpp/utility/rel_ops/operator_cmp"/>
    <function name="std::rel_ops::operator&gt;" link="cpp/utility/rel_ops/operator_cmp"/>
    <function name="std::rel_ops::operator&lt;=" link="cpp/utility/rel_ops/operator_cmp"/>
    <function name="std::rel_ops::operator&gt;=" link="cpp/utility/rel_ops/operator_cmp"/>

    <function name="std::to_chars" link="cpp/utility/to_chars" since="c++17"/>
    <class name="std::to_chars_result" link="cpp/utility/to_chars" since="c++17"/>
    <function name="std::from_chars" link="cpp/utility/from_chars" since="c++17"/>
    <class name="std::from_chars_result" link="cpp/utility/from_chars" since="c++17"/>

    <class name="std::chars_format" link="cpp/utility/chars_format" since="c++17">
        <const name="scientific" link="."/>
        <const name="fixed" link="."/>
        <const name="hex" link="."/>
        <const name="general" link="."/>

        <overload name="operator|=" link="."/>
        <overload name="operator&amp;=" link="."/>
        <overload name="operator^=" link="."/>
        <overload name="operator|" link="."/>
        <overload name="operator&amp;" link="."/>
        <overload name="operator^" link="."/>
        <overload name="operator~" link="."/>
    </class>

    <class name="std::hash" link="cpp/utility/hash" since="c++11">
        <constructor/>
        <function name="operator()"/>
    </class>

    <!--=======================================================================-->
    <!-- cpp/string/byte -->

    <function name="std::isalnum (&lt;cctype&gt;)" link="cpp/string/byte/isalnum"/>
    <function name="std::isalpha (&lt;cctype&gt;)" link="cpp/string/byte/isalpha"/>
    <function name="std::islower (&lt;cctype&gt;)" link="cpp/string/byte/islower"/>
    <function name="std::isupper (&lt;cctype&gt;)" link="cpp/string/byte/isupper"/>
    <function name="std::isdigit (&lt;cctype&gt;)" link="cpp/string/byte/isdigit"/>
    <function name="std::isxdigit (&lt;cctype&gt;)" link="cpp/string/byte/isxdigit"/>
    <function name="std::iscntrl (&lt;cctype&gt;)" link="cpp/string/byte/iscntrl"/>
    <function name="std::isgraph (&lt;cctype&gt;)" link="cpp/string/byte/isgraph"/>
    <function name="std::isspace (&lt;cctype&gt;)" link="cpp/string/byte/isspace"/>
    <function name="std::isblank (&lt;cctype&gt;)" link="cpp/string/byte/isblank"/>
    <function name="std::isprint (&lt;cctype&gt;)" link="cpp/string/byte/isprint"/>
    <function name="std::ispunct (&lt;cctype&gt;)" link="cpp/string/byte/ispunct"/>
    <function name="std::tolower (&lt;cctype&gt;)" link="cpp/string/byte/tolower"/>
    <function name="std::toupper (&lt;cctype&gt;)" link="cpp/string/byte/toupper"/>

    <function name="std::atof" link="cpp/string/byte/atof"/>
    <function name="std::atoi" link="cpp/string/byte/atoi"/>
    <function name="std::atol" link="cpp/string/byte/atoi"/>
    <function name="std::atoll" link="cpp/string/byte/atoi" since="c++11"/>
    <function name="std::strtol" link="cpp/string/byte/strtol"/>
    <function name="std::strtoll" link="cpp/string/byte/strtol"/>
    <function name="std::strtoul" link="cpp/string/byte/strtoul"/>
    <function name="std::strtoull" link="cpp/string/byte/strtoul" since="c++11"/>
    <function name="std::strtof" link="cpp/string/byte/strtof"/>
    <function name="std::strtod" link="cpp/string/byte/strtof"/>
    <function name="std::strtold" link="cpp/string/byte/strtof"/>
    <function name="std::strtoimax" link="cpp/string/byte/strtoimax"/>
    <function name="std::strtoumax" link="cpp/string/byte/strtoimax"/>

    <function name="std::strcpy" link="cpp/string/byte/strcpy"/>
    <function name="std::strncpy" link="cpp/string/byte/strncpy"/>
    <function name="std::strcat" link="cpp/string/byte/strcat"/>
    <function name="std::strncat" link="cpp/string/byte/strncat"/>
    <function name="std::strxfrm" link="cpp/string/byte/strxfrm"/>

    <function name="std::strlen" link="cpp/string/byte/strlen"/>
    <function name="std::strcmp" link="cpp/string/byte/strcmp"/>
    <function name="std::strncmp" link="cpp/string/byte/strncmp"/>
    <function name="std::strcoll" link="cpp/string/byte/strcoll"/>
    <function name="std::strchr" link="cpp/string/byte/strchr"/>
    <function name="std::strrchr" link="cpp/string/byte/strrchr"/>
    <function name="std::strspn" link="cpp/string/byte/strspn"/>
    <function name="std::strcspn" link="cpp/string/byte/strcspn"/>
    <function name="std::strpbrk" link="cpp/string/byte/strpbrk"/>
    <function name="std::strstr" link="cpp/string/byte/strstr"/>
    <function name="std::strtok" link="cpp/string/byte/strtok"/>

    <function name="std::memchr" link="cpp/string/byte/memchr"/>
    <function name="std::memcmp" link="cpp/string/byte/memcmp"/>
    <function name="std::memset" link="cpp/string/byte/memset"/>
    <function name="std::memcpy" link="cpp/string/byte/memcpy"/>
    <function name="std::memmove" link="cpp/string/byte/memmove"/>

    <function name="std::strerror" link="cpp/string/byte/strerror"/>

    <!-- cpp/string/multibyte -->

    <function name="std::mblen" link="cpp/string/multibyte/mblen"/>
    <function name="std::mbtowc" link="cpp/string/multibyte/mbtowc"/>
    <function name="std::wctomb" link="cpp/string/multibyte/wctomb"/>
    <function name="std::mbstowcs" link="cpp/string/multibyte/mbstowcs"/>
    <function name="std::wcstombs" link="cpp/string/multibyte/wcstombs"/>

    <function name="std::mbsinit" link="cpp/string/multibyte/mbsinit"/>
    <function name="std::btowc" link="cpp/string/multibyte/btowc"/>
    <function name="std::wctob" link="cpp/string/multibyte/wctob"/>
    <function name="std::mbrlen" link="cpp/string/multibyte/mbrlen"/>
    <function name="std::mbrtowc" link="cpp/string/multibyte/mbrtowc"/>
    <function name="std::wcrtomb" link="cpp/string/multibyte/wcrtomb"/>
    <function name="std::mbsrtowcs" link="cpp/string/multibyte/mbsrtowcs"/>
    <function name="std::wcsrtombs" link="cpp/string/multibyte/wcsrtombs"/>

    <function name="std::mbrtoc8" link="cpp/string/multibyte/mbrtoc8" since="c++20"/>
    <function name="std::c8rtomb" link="cpp/string/multibyte/c8rtomb" since="c++20"/>
    <function name="std::mbrtoc16" link="cpp/string/multibyte/mbrtoc16" since="c++11"/>
    <function name="std::c16rtomb" link="cpp/string/multibyte/c16rtomb" since="c++11"/>
    <function name="std::mbrtoc32" link="cpp/string/multibyte/mbrtoc32" since="c++11"/>
    <function name="std::c32rtomb" link="cpp/string/multibyte/c32rtomb" since="c++11"/>

    <class name="std::mbstate_t" link="cpp/string/multibyte/mbstate_t"/>

    <!-- cpp/string/wide -->

    <function name="std::iswalnum" link="cpp/string/wide/iswalnum"/>
    <function name="std::iswalpha" link="cpp/string/wide/iswalpha"/>
    <function name="std::iswlower" link="cpp/string/wide/iswlower"/>
    <function name="std::iswupper" link="cpp/string/wide/iswupper"/>
    <function name="std::iswdigit" link="cpp/string/wide/iswdigit"/>
    <function name="std::iswxdigit" link="cpp/string/wide/iswxdigit"/>
    <function name="std::iswcntrl" link="cpp/string/wide/iswcntrl"/>
    <function name="std::iswgraph" link="cpp/string/wide/iswgraph"/>
    <function name="std::iswspace" link="cpp/string/wide/iswspace"/>
    <function name="std::iswblank" link="cpp/string/wide/iswblank"/>
    <function name="std::iswprint" link="cpp/string/wide/iswprint"/>
    <function name="std::iswpunct" link="cpp/string/wide/iswpunct"/>
    <function name="std::iswctype" link="cpp/string/wide/iswctype"/>
    <function name="std::wctype" link="cpp/string/wide/wctype"/>

    <function name="std::towlower" link="cpp/string/wide/towlower"/>
    <function name="std::towupper" link="cpp/string/wide/towupper"/>
    <function name="std::towctrans" link="cpp/string/wide/towctrans"/>
    <function name="std::wctrans" link="cpp/string/wide/wctrans"/>

    <function name="std::wcstof" link="cpp/string/wide/wcstof"/>
    <function name="std::wcstod" link="cpp/string/wide/wcstof"/>
    <function name="std::wcstold" link="cpp/string/wide/wcstof"/>
    <function name="std::wcstol" link="cpp/string/wide/wcstol"/>
    <function name="std::wcstoll" link="cpp/string/wide/wcstol"/>
    <function name="std::wcstoul" link="cpp/string/wide/wcstoul"/>
    <function name="std::wcstoull" link="cpp/string/wide/wcstoul"/>
    <function name="std::wcstoimax" link="cpp/string/wide/wcstoimax"/>
    <function name="std::wcstoumax" link="cpp/string/wide/wcstoimax"/>

    <function name="std::wcscpy" link="cpp/string/wide/wcscpy"/>
    <function name="std::wcsncpy" link="cpp/string/wide/wcsncpy"/>
    <function name="std::wcscat" link="cpp/string/wide/wcscat"/>
    <function name="std::wcsncat" link="cpp/string/wide/wcsncat"/>
    <function name="std::wcsxfrm" link="cpp/string/wide/wcsxfrm"/>

    <function name="std::wcslen" link="cpp/string/wide/wcslen"/>
    <function name="std::wcscmp" link="cpp/string/wide/wcscmp"/>
    <function name="std::wcsncmp" link="cpp/string/wide/wcsncmp"/>
    <function name="std::wcscoll" link="cpp/string/wide/wcscoll"/>
    <function name="std::wcschr" link="cpp/string/wide/wcschr"/>
    <function name="std::wcsrchr" link="cpp/string/wide/wcsrchr"/>
    <function name="std::wcsspn" link="cpp/string/wide/wcsspn"/>
    <function name="std::wcscspn" link="cpp/string/wide/wcscspn"/>
    <function name="std::wcspbrk" link="cpp/string/wide/wcspbrk"/>
    <function name="std::wcsstr" link="cpp/string/wide/wcsstr"/>
    <function name="std::wcstok" link="cpp/string/wide/wcstok"/>

    <function name="std::wmemchr" link="cpp/string/wide/wmemchr"/>
    <function name="std::wmemcmp" link="cpp/string/wide/wmemcmp"/>
    <function name="std::wmemset" link="cpp/string/wide/wmemset"/>
    <function name="std::wmemcpy" link="cpp/string/wide/wmemcpy"/>
    <function name="std::wmemmove" link="cpp/string/wide/wmemmove"/>

    <!-- cpp/string/basic_string -->

    <class name="std::basic_string" link="cpp/string/basic_string">
        <constructor/>
        <function name="assign"/>
        <function name="operator="/>
        <function name="get_allocator"/>

        <function name="at"/>
        <function name="operator[]" link="operator_at"/>
        <function name="front" since="c++11"/>
        <function name="back" since="c++11"/>
        <function name="data"/>
        <function name="c_str"/>
        <function name="operator basic_string_view" link="operator_basic_string_view" since="c++17"/>

        <function name="begin"/>
        <function name="cbegin" link="begin" since="c++11"/>
        <function name="end"/>
        <function name="cend" link="end" since="c++11"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin" since="c++11"/>
        <function name="rend"/>
        <function name="crend" link="rend" since="c++11"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="length" link="size"/>
        <function name="max_size"/>
        <function name="reserve"/>
        <function name="capacity"/>
        <function name="shrink_to_fit" since="c++11"/>

        <function name="clear"/>
        <function name="insert"/>
        <function name="erase"/>
        <function name="push_back"/>
        <function name="pop_back" since="c++11"/>
        <function name="append"/>
        <function name="operator+="/>
        <function name="compare"/>
        <function name="starts_with" since="c++20"/>
        <function name="ends_with" since="c++20"/>
        <function name="replace"/>
        <function name="substr"/>
        <function name="copy"/>
        <function name="resize"/>
        <function name="swap"/>

        <function name="find"/>
        <function name="rfind"/>
        <function name="find_first_of"/>
        <function name="find_first_not_of"/>
        <function name="find_last_of"/>
        <function name="find_last_not_of"/>

        <const name="npos"/>

        <overload name="operator+"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase" link="erase2" since="c++20"/>
        <overload name="std::erase_if" link="erase2" since="c++20"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>

        <specialization name="std::hash" link="hash"/>
    </class>

    <typedef name="std::string" alias="std::basic_string"/>
    <typedef name="std::wstring" alias="std::basic_string"/>
    <typedef name="std::u8string" alias="std::basic_string" since="c++20"/>
    <typedef name="std::u16string" alias="std::basic_string" since="c++11"/>
    <typedef name="std::u32string" alias="std::basic_string" since="c++11"/>

    <typedef name="std::pmr::string" alias="std::basic_string" since="c++17"/>
    <typedef name="std::pmr::wstring" alias="std::basic_string" since="c++17"/>
    <typedef name="std::pmr::u8string" alias="std::basic_string" since="c++20"/>
    <typedef name="std::pmr::u16string" alias="std::basic_string" since="c++17"/>
    <typedef name="std::pmr::u32string" alias="std::basic_string" since="c++17"/>

    <function name="std::literals::string_literals::operator&quot;&quot;s" link="cpp/string/basic_string/operator&quot;&quot;s" since="c++14"/>

    <!-- cpp/string/basic_string_view -->

    <class name="std::basic_string_view" link="cpp/string/basic_string_view" since="c++17">
        <constructor/>
        <function name="operator="/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
        <function name="rend"/>
        <function name="crend" link="rend"/>

        <function name="operator[]" link="operator_at"/>
        <function name="at"/>
        <function name="front"/>
        <function name="back"/>
        <function name="data"/>

        <function name="size"/>
        <function name="length" link="size"/>
        <function name="max_size"/>
        <function name="empty"/>
        <function name="remove_prefix"/>
        <function name="remove_suffix"/>
        <function name="swap"/>

        <function name="copy"/>
        <function name="substr"/>
        <function name="compare"/>
        <function name="starts_with" since="c++20"/>
        <function name="ends_with" since="c++20"/>
        <function name="find"/>
        <function name="rfind"/>
        <function name="find_first_of"/>
        <function name="find_last_of"/>
        <function name="find_first_not_of"/>
        <function name="find_last_not_of"/>
        <const name="npos"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
        <overload name="std::begin" link="begin_end_nonmem" since="c++20"/>
        <overload name="std::end" link="begin_end_nonmem" since="c++20"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::hash" link="hash"/>
    </class>

    <typedef name="std::string_view" alias="std::basic_string_view" since="c++17"/>
    <typedef name="std::wstring_view" alias="std::basic_string_view" since="c++17"/>
    <typedef name="std::u8string_view" alias="std::basic_string_view" since="c++20"/>
    <typedef name="std::u16string_view" alias="std::basic_string_view" since="c++17"/>
    <typedef name="std::u32string_view" alias="std::basic_string_view" since="c++17"/>

    <function name="std::literals::string_view_literals::operator&quot;&quot;sv" link="cpp/string/basic_string_view/operator&quot;&quot;sv" since="c++17"/>

    <!-- cpp/string -->

    <function name="std::getline" link="cpp/string/basic_string/getline"/>
    <function name="std::stoi" link="cpp/string/basic_string/stol" since="c++11"/>
    <function name="std::stol" link="cpp/string/basic_string/stol" since="c++11"/>
    <function name="std::stoll" link="cpp/string/basic_string/stol" since="c++11"/>
    <function name="std::stoul" link="cpp/string/basic_string/stoul" since="c++11"/>
    <function name="std::stoull" link="cpp/string/basic_string/stoul" since="c++11"/>
    <function name="std::stof" link="cpp/string/basic_string/stof" since="c++11"/>
    <function name="std::stod" link="cpp/string/basic_string/stof" since="c++11"/>
    <function name="std::stold" link="cpp/string/basic_string/stof" since="c++11"/>
    <function name="std::to_string" link="cpp/string/basic_string/to_string" since="c++11"/>
    <function name="std::to_wstring" link="cpp/string/basic_string/to_wstring" since="c++11"/>

    <class name="std::char_traits" link="cpp/string/char_traits">
        <function name="assign"/>
        <function name="eq" link="cmp"/>
        <function name="lt" link="cmp"/>
        <function name="move"/>
        <function name="copy"/>
        <function name="compare"/>
        <function name="length"/>
        <function name="find"/>
        <function name="to_char_type"/>
        <function name="to_int_type"/>
        <function name="eq_int_type"/>
        <function name="eof"/>
        <function name="not_eof"/>
    </class>

    <!-- cpp/container -->

    <class name="std::array" link="cpp/container/array" since="c++11">

        <function name="at"/>
        <function name="operator[]" link="operator_at"/>
        <function name="front"/>
        <function name="back"/>
        <function name="data"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin"/>
        <function name="rend"/>
        <function name="crend" link="rend"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>

        <function name="fill"/>
        <function name="swap"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::get" link="get"/>

        <specialization name="std::tuple_size" link="tuple_size"/>
        <specialization name="std::tuple_element" link="tuple_element"/>
    </class>

    <!--
    <class name="std::dynarray" link="cpp/container/dynarray" since="c++14">
        <constructor/>
        <destructor/>

        <function name="at"/>
        <function name="operator[]" link="operator_at"/>
        <function name="front"/>
        <function name="back"/>
        <function name="data"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin"/>
        <function name="rend"/>
        <function name="crend" link="rend"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>

        <function name="fill"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <specialization name="std::uses_allocator" link="uses_allocator"/>
    </class>
    -->

    <class name="std::vector" link="cpp/container/vector">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="assign"/>
        <function name="get_allocator"/>

        <function name="at"/>
        <function name="operator[]" link="operator_at"/>
        <function name="front"/>
        <function name="back"/>
        <function name="data"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin"/>
        <function name="rend"/>
        <function name="crend" link="rend"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>
        <function name="reserve"/>
        <function name="capacity"/>
        <function name="shrink_to_fit"/>

        <function name="clear"/>
        <function name="insert"/>
        <function name="emplace"/>
        <function name="erase"/>
        <function name="push_back"/>
        <function name="emplace_back"/>
        <function name="pop_back"/>
        <function name="resize"/>
        <function name="swap"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase" link="erase2" since="c++20"/>
        <overload name="std::erase_if" link="erase2" since="c++20"/>
    </class>
    <typedef name="std::pmr::vector" alias="std::vector" since="c++17"/>

    <class name="std::deque" link="cpp/container/deque">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="assign"/>
        <function name="get_allocator"/>

        <function name="at"/>
        <function name="operator[]" link="operator_at"/>
        <function name="front"/>
        <function name="back"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin"/>
        <function name="rend"/>
        <function name="crend" link="rend"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>
        <function name="shrink_to_fit"/>

        <function name="clear"/>
        <function name="insert"/>
        <function name="emplace"/>
        <function name="erase"/>
        <function name="push_back"/>
        <function name="emplace_back"/>
        <function name="pop_back"/>
        <function name="push_front"/>
        <function name="emplace_front"/>
        <function name="pop_front"/>
        <function name="resize"/>
        <function name="swap"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase" link="erase2" since="c++20"/>
        <overload name="std::erase_if" link="erase2" since="c++20"/>
    </class>
    <typedef name="std::pmr::deque" alias="std::deque" since="c++17"/>

    <class name="std::forward_list" link="cpp/container/forward_list" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="assign"/>
        <function name="get_allocator"/>

        <function name="front"/>

        <function name="before_begin"/>
        <function name="cbefore_begin" link="before_begin"/>
        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>

        <function name="empty"/>
        <function name="max_size"/>

        <function name="clear"/>
        <function name="insert_after"/>
        <function name="emplace_after"/>
        <function name="erase_after"/>
        <function name="push_front"/>
        <function name="emplace_front"/>
        <function name="pop_front"/>
        <function name="resize"/>
        <function name="swap"/>

        <function name="merge"/>
        <function name="splice_after"/>
        <function name="remove"/>
        <function name="remove_if" link="remove"/>
        <function name="reverse"/>
        <function name="unique"/>
        <function name="sort"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase" link="erase2" since="c++20"/>
        <overload name="std::erase_if" link="erase2" since="c++20"/>
    </class>
    <typedef name="std::pmr::forward_list" alias="std::forward_list" since="c++17"/>

    <class name="std::list" link="cpp/container/list">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="assign"/>
        <function name="get_allocator"/>

        <function name="front"/>
        <function name="back"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin"/>
        <function name="rend"/>
        <function name="crend" link="rend"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>

        <function name="clear"/>
        <function name="insert"/>
        <function name="emplace"/>
        <function name="erase"/>
        <function name="push_back"/>
        <function name="emplace_back"/>
        <function name="pop_back"/>
        <function name="push_front"/>
        <function name="emplace_front"/>
        <function name="pop_front"/>
        <function name="resize"/>
        <function name="swap"/>

        <function name="merge"/>
        <function name="splice"/>
        <function name="remove"/>
        <function name="remove_if" link="remove"/>
        <function name="reverse"/>
        <function name="unique"/>
        <function name="sort"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase" link="erase2" since="c++20"/>
        <overload name="std::erase_if" link="erase2" since="c++20"/>
    </class>
    <typedef name="std::pmr::list" alias="std::list" since="c++17"/>

    <class name="std::set" link="cpp/container/set">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="get_allocator"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin"/>
        <function name="rend"/>
        <function name="crend" link="rend"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>

        <function name="clear"/>
        <function name="insert"/>
        <function name="emplace"/>
        <function name="emplace_hint"/>
        <function name="erase"/>
        <function name="swap"/>
        <function name="extract" since="c++17"/>
        <function name="merge" since="c++17"/>

        <function name="count"/>
        <function name="find"/>
        <function name="contains" since="c++20"/>
        <function name="equal_range"/>
        <function name="lower_bound"/>
        <function name="upper_bound"/>

        <function name="key_comp"/>
        <function name="value_comp"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase_if" link="erase_if" since="c++20"/>
    </class>
    <typedef name="std::pmr::set" alias="std::set" since="c++17"/>

    <class name="std::multiset" link="cpp/container/multiset">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="get_allocator"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin"/>
        <function name="rend"/>
        <function name="crend" link="rend"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>

        <function name="clear"/>
        <function name="insert"/>
        <function name="emplace"/>
        <function name="emplace_hint"/>
        <function name="erase"/>
        <function name="swap"/>
        <function name="extract" since="c++17"/>
        <function name="merge" since="c++17"/>

        <function name="count"/>
        <function name="find"/>
        <function name="contains" since="c++20"/>
        <function name="equal_range"/>
        <function name="lower_bound"/>
        <function name="upper_bound"/>

        <function name="key_comp"/>
        <function name="value_comp"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase_if" link="erase_if" since="c++20"/>
    </class>
    <typedef name="std::pmr::multiset" alias="std::multiset" since="c++17"/>

    <class name="std::map" link="cpp/container/map">
        <class name="value_compare">
            <function name="operator()" link="."/>
            <!-- protected -->
            <constructor link="."/>
            <variable name="comp" link="."/>
        </class>

        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="get_allocator"/>

        <function name="at"/>
        <function name="operator[]" link="operator_at"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin"/>
        <function name="rend"/>
        <function name="crend" link="rend"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>

        <function name="clear"/>
        <function name="insert"/>
        <function name="insert_or_assign" since="c++17"/>
        <function name="emplace" since="c++11"/>
        <function name="emplace_hint" since="c++11"/>
        <function name="try_emplace" since="c++17"/>
        <function name="erase"/>
        <function name="swap"/>
        <function name="extract" since="c++17"/>
        <function name="merge" since="c++17"/>

        <function name="count"/>
        <function name="find"/>
        <function name="contains" since="c++20"/>
        <function name="equal_range"/>
        <function name="lower_bound"/>
        <function name="upper_bound"/>

        <function name="key_comp"/>
        <function name="value_comp"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase_if" link="erase_if" since="c++20"/>
    </class>
    <typedef name="std::pmr::map" alias="std::map" since="c++17"/>

    <class name="std::multimap" link="cpp/container/multimap">
        <class name="value_compare">
            <function name="operator()" link="."/>
            <!-- protected -->
            <constructor link="."/>
            <variable name="comp" link="."/>
        </class>

        <constructor/>
        <destructor/>

        <function name="operator="/>
        <function name="get_allocator"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin"/>
        <function name="rend"/>
        <function name="crend" link="rend"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>

        <function name="clear"/>
        <function name="insert"/>
        <function name="emplace" since="c++11"/>
        <function name="emplace_hint" since="c++11"/>
        <function name="erase"/>
        <function name="swap"/>
        <function name="extract" since="c++17"/>
        <function name="merge" since="c++17"/>

        <function name="count"/>
        <function name="find"/>
        <function name="contains" since="c++20"/>
        <function name="equal_range"/>
        <function name="lower_bound"/>
        <function name="upper_bound"/>

        <function name="key_comp"/>
        <function name="value_comp"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase_if" link="erase_if" since="c++20"/>
    </class>
    <typedef name="std::pmr::multimap" alias="std::multimap" since="c++17"/>

    <class name="std::unordered_set" link="cpp/container/unordered_set" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="get_allocator"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>

        <function name="clear"/>
        <function name="insert"/>
        <function name="emplace"/>
        <function name="emplace_hint"/>
        <function name="erase"/>
        <function name="swap"/>
        <function name="extract" since="c++17"/>
        <function name="merge" since="c++17"/>

        <function name="count"/>
        <function name="find"/>
        <function name="contains" since="c++20"/>
        <function name="equal_range"/>

        <function name="begin(int)" link="begin2"/>
        <function name="cbegin(int)" link="begin2"/>
        <function name="end(int)" link="end2"/>
        <function name="cend(int)" link="end2"/>

        <function name="bucket_count"/>
        <function name="max_bucket_count"/>
        <function name="bucket_size"/>
        <function name="bucket"/>

        <function name="load_factor"/>
        <function name="max_load_factor"/>
        <function name="reserve"/>
        <function name="rehash"/>

        <function name="hash_function"/>
        <function name="key_eq"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase_if" link="erase_if" since="c++20"/>
    </class>
    <typedef name="std::pmr::unordered_set" alias="std::unordered_set" since="c++17"/>

    <class name="std::unordered_multiset" link="cpp/container/unordered_multiset" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="get_allocator"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>

        <function name="clear"/>
        <function name="insert"/>
        <function name="emplace"/>
        <function name="emplace_hint"/>
        <function name="erase"/>
        <function name="swap"/>
        <function name="extract" since="c++17"/>
        <function name="merge" since="c++17"/>

        <function name="count"/>
        <function name="find"/>
        <function name="contains" since="c++20"/>
        <function name="equal_range"/>

        <function name="begin(int)" link="begin2"/>
        <function name="cbegin(int)" link="begin2"/>
        <function name="end(int)" link="end2"/>
        <function name="cend(int)" link="end2"/>

        <function name="bucket_count"/>
        <function name="max_bucket_count"/>
        <function name="bucket_size"/>
        <function name="bucket"/>

        <function name="load_factor"/>
        <function name="max_load_factor"/>
        <function name="reserve"/>
        <function name="rehash"/>

        <function name="hash_function"/>
        <function name="key_eq"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase_if" link="erase_if" since="c++20"/>
    </class>
    <typedef name="std::pmr::unordered_multiset" alias="std::unordered_multiset" since="c++17"/>

    <class name="std::unordered_map" link="cpp/container/unordered_map" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="get_allocator"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>

        <function name="clear"/>
        <function name="insert"/>
        <function name="insert_or_assign" since="c++17"/>
        <function name="emplace"/>
        <function name="emplace_hint"/>
        <function name="try_emplace" since="c++17"/>
        <function name="erase"/>
        <function name="swap"/>
        <function name="extract" since="c++17"/>
        <function name="merge" since="c++17"/>

        <function name="at"/>
        <function name="operator[]" link="operator_at"/>
        <function name="count"/>
        <function name="find"/>
        <function name="contains" since="c++20"/>
        <function name="equal_range"/>

        <function name="begin(int)" link="begin2"/>
        <function name="cbegin(int)" link="begin2"/>
        <function name="end(int)" link="end2"/>
        <function name="cend(int)" link="end2"/>

        <function name="bucket_count"/>
        <function name="max_bucket_count"/>
        <function name="bucket_size"/>
        <function name="bucket"/>

        <function name="load_factor"/>
        <function name="max_load_factor"/>
        <function name="reserve"/>
        <function name="rehash"/>

        <function name="hash_function"/>
        <function name="key_eq"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase_if" link="erase_if" since="c++20"/>
    </class>
    <typedef name="std::pmr::unordered_map" alias="std::unordered_map" since="c++17"/>

    <class name="std::unordered_multimap" link="cpp/container/unordered_multimap" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="get_allocator"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>

        <function name="clear"/>
        <function name="insert"/>
        <function name="emplace"/>
        <function name="emplace_hint"/>
        <function name="erase"/>
        <function name="swap"/>
        <function name="extract" since="c++17"/>
        <function name="merge" since="c++17"/>

        <function name="count"/>
        <function name="find"/>
        <function name="contains" since="c++20"/>
        <function name="equal_range"/>

        <function name="begin(int)" link="begin2"/>
        <function name="cbegin(int)" link="begin2"/>
        <function name="end(int)" link="end2"/>
        <function name="cend(int)" link="end2"/>

        <function name="bucket_count"/>
        <function name="max_bucket_count"/>
        <function name="bucket_size"/>
        <function name="bucket"/>

        <function name="load_factor"/>
        <function name="max_load_factor"/>
        <function name="reserve"/>
        <function name="rehash"/>

        <function name="hash_function"/>
        <function name="key_eq"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::erase_if" link="erase_if" since="c++20"/>
    </class>
    <typedef name="std::pmr::unordered_multimap" alias="std::unordered_multimap" since="c++17"/>

    <class name="std::stack" link="cpp/container/stack">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="top"/>

        <function name="empty"/>
        <function name="size"/>

        <function name="push"/>
        <function name="emplace"/>
        <function name="pop"/>
        <function name="swap"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <specialization name="std::uses_allocator" link="uses_allocator"/>

        <!-- protected -->
        <variable name="c" link="."/>
    </class>

    <class name="std::queue" link="cpp/container/queue">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="front"/>
        <function name="back"/>

        <function name="empty"/>
        <function name="size"/>

        <function name="push"/>
        <function name="emplace"/>
        <function name="pop"/>
        <function name="swap"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>

        <overload name="std::swap" link="swap2"/>
        <specialization name="std::uses_allocator" link="uses_allocator"/>

        <!-- protected -->
        <variable name="c" link="."/>
    </class>

    <class name="std::priority_queue" link="cpp/container/priority_queue">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="top"/>

        <function name="empty"/>
        <function name="size"/>

        <function name="push"/>
        <function name="emplace"/>
        <function name="pop"/>
        <function name="swap"/>

        <overload name="std::swap" link="swap2"/>
        <specialization name="std::uses_allocator" link="uses_allocator"/>

        <!-- protected -->
        <variable name="c" link="."/>
    </class>

    <class name="std::span" link="cpp/container/span" since="c++20">
        <constructor/>
        <function name="operator="/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin"/>
        <function name="rend"/>
        <function name="crend" link="rend"/>

        <function name="front"/>
        <function name="back"/>
        <function name="operator[]" link="operator_at"/>
        <function name="data"/>

        <function name="size"/>
        <function name="size_bytes"/>
        <function name="empty"/>

        <function name="first"/>
        <function name="last"/>
        <function name="subspan"/>

        <overload name="std::as_bytes" link="as_bytes"/>
        <overload name="std::as_writable_bytes" link="as_bytes"/>
    </class>
    <const name="std::dynamic_extent" link="cpp/container/span/dynamic_extent" since="c++20"/>

    <!-- cpp/numeric -->

    <function name="std::midpoint" link="cpp/numeric/midpoint" since="c++20"/>
    <function name="std::lerp" link="cpp/numeric/lerp" since="c++20"/>

    <function name="std::bit_cast" link="cpp/numeric/bit_cast" since="c++20"/>
    <function name="std::has_single_bit" link="cpp/numeric/has_single_bit" since="c++20"/>
    <function name="std::bit_ceil" link="cpp/numeric/bit_ceil" since="c++20"/>
    <function name="std::bit_floor" link="cpp/numeric/bit_floor" since="c++20"/>
    <function name="std::bit_width" link="cpp/numeric/bit_width" since="c++20"/>
    <function name="std::rotl" link="cpp/numeric/rotl" since="c++20"/>
    <function name="std::rotr" link="cpp/numeric/rotr" since="c++20"/>
    <function name="std::countl_zero" link="cpp/numeric/countl_zero" since="c++20"/>
    <function name="std::countl_one" link="cpp/numeric/countl_one" since="c++20"/>
    <function name="std::countr_zero" link="cpp/numeric/countr_zero" since="c++20"/>
    <function name="std::countr_one" link="cpp/numeric/countr_one" since="c++20"/>
    <function name="std::popcount" link="cpp/numeric/popcount" since="c++20"/>

    <!-- cpp/numeric/math -->

    <function name="std::abs(int)" link="cpp/numeric/math/abs"/>
    <function name="std::labs" link="cpp/numeric/math/abs"/>
    <function name="std::llabs" link="cpp/numeric/math/abs" since="c++11"/>
    <function name="std::imaxabs" link="cpp/numeric/math/abs" since="c++11"/>
    <function name="std::abs(float)" link="cpp/numeric/math/fabs"/>
    <function name="std::fabs" link="cpp/numeric/math/fabs"/>
    <function name="std::div" link="cpp/numeric/math/div"/>
    <function name="std::ldiv" link="cpp/numeric/math/div"/>
    <function name="std::lldiv" link="cpp/numeric/math/div" since="c++11"/>
    <function name="std::imaxdiv" link="cpp/numeric/math/div" since="c++11"/>
    <class name="std::div_t" link="cpp/numeric/math/div">
        <variable name="quot" link="."/>
        <variable name="rem" link="."/>
    </class>
    <class name="std::ldiv_t" link="cpp/numeric/math/div">
        <variable name="quot" link="."/>
        <variable name="rem" link="."/>
    </class>
    <class name="std::lldiv_t" link="cpp/numeric/math/div" since="c++11">
        <variable name="quot" link="."/>
        <variable name="rem" link="."/>
    </class>
    <class name="std::imaxdiv_t" link="cpp/numeric/math/div" since="c++11">
        <variable name="quot" link="."/>
        <variable name="rem" link="."/>
    </class>
    <function name="std::fmod" link="cpp/numeric/math/fmod"/>
    <function name="std::remainder" link="cpp/numeric/math/remainder"/>
    <function name="std::remquo" link="cpp/numeric/math/remquo"/>
    <function name="std::fma" link="cpp/numeric/math/fma"/>
    <function name="std::fmax" link="cpp/numeric/math/fmax"/>
    <function name="std::fmin" link="cpp/numeric/math/fmin"/>
    <function name="std::fdim" link="cpp/numeric/math/fdim"/>
    <function name="std::nan" link="cpp/numeric/math/nan"/>
    <function name="std::nanf" link="cpp/numeric/math/nan"/>
    <function name="std::nanl" link="cpp/numeric/math/nan"/>

    <function name="std::exp" link="cpp/numeric/math/exp"/>
    <function name="std::exp2" link="cpp/numeric/math/exp2"/>
    <function name="std::expm1" link="cpp/numeric/math/expm1"/>
    <function name="std::log" link="cpp/numeric/math/log"/>
    <function name="std::log10" link="cpp/numeric/math/log10"/>
    <function name="std::log1p" link="cpp/numeric/math/log1p"/>
    <function name="std::log2" link="cpp/numeric/math/log2"/>
    <function name="std::ilogb" link="cpp/numeric/math/ilogb"/>
    <function name="std::logb" link="cpp/numeric/math/logb"/>

    <function name="std::sqrt" link="cpp/numeric/math/sqrt"/>
    <function name="std::cbrt" link="cpp/numeric/math/cbrt"/>
    <function name="std::hypot" link="cpp/numeric/math/hypot"/>
    <function name="std::pow" link="cpp/numeric/math/pow"/>

    <function name="std::sin" link="cpp/numeric/math/sin"/>
    <function name="std::cos" link="cpp/numeric/math/cos"/>
    <function name="std::tan" link="cpp/numeric/math/tan"/>
    <function name="std::asin" link="cpp/numeric/math/asin"/>
    <function name="std::acos" link="cpp/numeric/math/acos"/>
    <function name="std::atan" link="cpp/numeric/math/atan"/>
    <function name="std::atan2" link="cpp/numeric/math/atan2"/>
    <function name="std::sinh" link="cpp/numeric/math/sinh"/>
    <function name="std::cosh" link="cpp/numeric/math/cosh"/>
    <function name="std::tanh" link="cpp/numeric/math/tanh"/>
    <function name="std::asinh" link="cpp/numeric/math/asinh"/>
    <function name="std::acosh" link="cpp/numeric/math/acosh"/>
    <function name="std::atanh" link="cpp/numeric/math/atanh"/>

    <function name="std::erf" link="cpp/numeric/math/erf"/>
    <function name="std::erfc" link="cpp/numeric/math/erfc"/>
    <function name="std::lgamma" link="cpp/numeric/math/lgamma"/>
    <function name="std::tgamma" link="cpp/numeric/math/tgamma"/>

    <function name="std::ceil" link="cpp/numeric/math/ceil"/>
    <function name="std::floor" link="cpp/numeric/math/floor"/>
    <function name="std::trunc" link="cpp/numeric/math/trunc"/>
    <function name="std::round" link="cpp/numeric/math/round"/>
    <function name="std::lround" link="cpp/numeric/math/round"/>
    <function name="std::llround" link="cpp/numeric/math/round"/>
    <function name="std::nearbyint" link="cpp/numeric/math/nearbyint"/>
    <function name="std::rint" link="cpp/numeric/math/rint"/>
    <function name="std::lrint" link="cpp/numeric/math/rint"/>
    <function name="std::llrint" link="cpp/numeric/math/rint"/>

    <function name="std::frexp" link="cpp/numeric/math/frexp"/>
    <function name="std::ldexp" link="cpp/numeric/math/ldexp"/>
    <function name="std::modf" link="cpp/numeric/math/modf"/>
    <function name="std::scalbn" link="cpp/numeric/math/scalbn"/>
    <function name="std::scalbln" link="cpp/numeric/math/scalbn"/>
    <function name="std::nextafter" link="cpp/numeric/math/nextafter"/>
    <function name="std::nexttoward" link="cpp/numeric/math/nextafter"/>
    <function name="std::copysign" link="cpp/numeric/math/copysign"/>

    <function name="std::fpclassify" link="cpp/numeric/math/fpclassify"/>
    <function name="std::isfinite" link="cpp/numeric/math/isfinite"/>
    <function name="std::isinf" link="cpp/numeric/math/isinf"/>
    <function name="std::isnan" link="cpp/numeric/math/isnan"/>
    <function name="std::isnormal" link="cpp/numeric/math/isnormal"/>
    <function name="std::signbit" link="cpp/numeric/math/signbit"/>
    <function name="std::isgreater" link="cpp/numeric/math/isgreater"/>
    <function name="std::isgreaterequal" link="cpp/numeric/math/isgreaterequal"/>
    <function name="std::isless" link="cpp/numeric/math/isless"/>
    <function name="std::islessequal" link="cpp/numeric/math/islessequal"/>
    <function name="std::islessgreater" link="cpp/numeric/math/islessgreater"/>
    <function name="std::isunordered" link="cpp/numeric/math/isunordered"/>

    <const name="HUGE_VAL" link="cpp/numeric/math/HUGE_VAL"/>
    <const name="HUGE_VALF" link="cpp/numeric/math/HUGE_VAL"/>
    <const name="HUGE_VALL" link="cpp/numeric/math/HUGE_VAL"/>

    <const name="INFINITY" link="cpp/numeric/math/INFINITY"/>
    <const name="NAN" link="cpp/numeric/math/NAN"/>

    <const name="math_errhandling" link="cpp/numeric/math/math_errhandling"/>
    <const name="MATH_ERRNO" link="cpp/numeric/math/math_errhandling"/>
    <const name="MATH_ERREXCEPT" link="cpp/numeric/math/math_errhandling"/>

    <const name="FP_INFINITE" link="cpp/numeric/math/FP_categories"/>
    <const name="FP_NAN" link="cpp/numeric/math/FP_categories"/>
    <const name="FP_NORMAL" link="cpp/numeric/math/FP_categories"/>
    <const name="FP_SUBNORMAL" link="cpp/numeric/math/FP_categories"/>
    <const name="FP_ZERO" link="cpp/numeric/math/FP_categories"/>

    <!-- cpp/numeric/special_functions -->

    <function name="std::assoc_laguerre" link="cpp/numeric/special_functions/assoc_laguerre" since="c++17"/>
    <function name="std::assoc_laguerref" link="cpp/numeric/special_functions/assoc_laguerre" since="c++17"/>
    <function name="std::assoc_laguerrel" link="cpp/numeric/special_functions/assoc_laguerre" since="c++17"/>
    <function name="std::assoc_legendre" link="cpp/numeric/special_functions/assoc_legendre" since="c++17"/>
    <function name="std::assoc_legendref" link="cpp/numeric/special_functions/assoc_legendre" since="c++17"/>
    <function name="std::assoc_legendrel" link="cpp/numeric/special_functions/assoc_legendre" since="c++17"/>
    <function name="std::beta" link="cpp/numeric/special_functions/beta" since="c++17"/>
    <function name="std::betaf" link="cpp/numeric/special_functions/beta" since="c++17"/>
    <function name="std::betal" link="cpp/numeric/special_functions/beta" since="c++17"/>
    <function name="std::comp_ellint_1" link="cpp/numeric/special_functions/comp_ellint_1" since="c++17"/>
    <function name="std::comp_ellint_1f" link="cpp/numeric/special_functions/comp_ellint_1" since="c++17"/>
    <function name="std::comp_ellint_1l" link="cpp/numeric/special_functions/comp_ellint_1" since="c++17"/>
    <function name="std::comp_ellint_2" link="cpp/numeric/special_functions/comp_ellint_2" since="c++17"/>
    <function name="std::comp_ellint_2f" link="cpp/numeric/special_functions/comp_ellint_2" since="c++17"/>
    <function name="std::comp_ellint_2l" link="cpp/numeric/special_functions/comp_ellint_2" since="c++17"/>
    <function name="std::comp_ellint_3" link="cpp/numeric/special_functions/comp_ellint_3" since="c++17"/>
    <function name="std::comp_ellint_3f" link="cpp/numeric/special_functions/comp_ellint_3" since="c++17"/>
    <function name="std::comp_ellint_3l" link="cpp/numeric/special_functions/comp_ellint_3" since="c++17"/>
    <function name="std::cyl_bessel_i" link="cpp/numeric/special_functions/cyl_bessel_i" since="c++17"/>
    <function name="std::cyl_bessel_if" link="cpp/numeric/special_functions/cyl_bessel_i" since="c++17"/>
    <function name="std::cyl_bessel_il" link="cpp/numeric/special_functions/cyl_bessel_i" since="c++17"/>
    <function name="std::cyl_bessel_j" link="cpp/numeric/special_functions/cyl_bessel_j" since="c++17"/>
    <function name="std::cyl_bessel_jf" link="cpp/numeric/special_functions/cyl_bessel_j" since="c++17"/>
    <function name="std::cyl_bessel_jl" link="cpp/numeric/special_functions/cyl_bessel_j" since="c++17"/>
    <function name="std::cyl_bessel_k" link="cpp/numeric/special_functions/cyl_bessel_k" since="c++17"/>
    <function name="std::cyl_bessel_kf" link="cpp/numeric/special_functions/cyl_bessel_k" since="c++17"/>
    <function name="std::cyl_bessel_kl" link="cpp/numeric/special_functions/cyl_bessel_k" since="c++17"/>
    <function name="std::cyl_neumann" link="cpp/numeric/special_functions/cyl_neumann" since="c++17"/>
    <function name="std::cyl_neumannf" link="cpp/numeric/special_functions/cyl_neumann" since="c++17"/>
    <function name="std::cyl_neumannl" link="cpp/numeric/special_functions/cyl_neumann" since="c++17"/>
    <function name="std::ellint_1" link="cpp/numeric/special_functions/ellint_1" since="c++17"/>
    <function name="std::ellint_1f" link="cpp/numeric/special_functions/ellint_1" since="c++17"/>
    <function name="std::ellint_1l" link="cpp/numeric/special_functions/ellint_1" since="c++17"/>
    <function name="std::ellint_2" link="cpp/numeric/special_functions/ellint_2" since="c++17"/>
    <function name="std::ellint_2f" link="cpp/numeric/special_functions/ellint_2" since="c++17"/>
    <function name="std::ellint_2l" link="cpp/numeric/special_functions/ellint_2" since="c++17"/>
    <function name="std::ellint_3" link="cpp/numeric/special_functions/ellint_3" since="c++17"/>
    <function name="std::ellint_3f" link="cpp/numeric/special_functions/ellint_3" since="c++17"/>
    <function name="std::ellint_3l" link="cpp/numeric/special_functions/ellint_3" since="c++17"/>
    <function name="std::expint" link="cpp/numeric/special_functions/expint" since="c++17"/>
    <function name="std::expintf" link="cpp/numeric/special_functions/expint" since="c++17"/>
    <function name="std::expintl" link="cpp/numeric/special_functions/expint" since="c++17"/>
    <function name="std::hermite" link="cpp/numeric/special_functions/hermite" since="c++17"/>
    <function name="std::hermitef" link="cpp/numeric/special_functions/hermite" since="c++17"/>
    <function name="std::hermitel" link="cpp/numeric/special_functions/hermite" since="c++17"/>
    <function name="std::legendre" link="cpp/numeric/special_functions/legendre" since="c++17"/>
    <function name="std::legendref" link="cpp/numeric/special_functions/legendre" since="c++17"/>
    <function name="std::legendrel" link="cpp/numeric/special_functions/legendre" since="c++17"/>
    <function name="std::laguerre" link="cpp/numeric/special_functions/laguerre" since="c++17"/>
    <function name="std::laguerref" link="cpp/numeric/special_functions/laguerre" since="c++17"/>
    <function name="std::laguerrel" link="cpp/numeric/special_functions/laguerre" since="c++17"/>
    <function name="std::riemann_zeta" link="cpp/numeric/special_functions/riemann_zeta" since="c++17"/>
    <function name="std::riemann_zetaf" link="cpp/numeric/special_functions/riemann_zeta" since="c++17"/>
    <function name="std::riemann_zetal" link="cpp/numeric/special_functions/riemann_zeta" since="c++17"/>
    <function name="std::sph_bessel" link="cpp/numeric/special_functions/sph_bessel" since="c++17"/>
    <function name="std::sph_besself" link="cpp/numeric/special_functions/sph_bessel" since="c++17"/>
    <function name="std::sph_bessell" link="cpp/numeric/special_functions/sph_bessel" since="c++17"/>
    <function name="std::sph_legendre" link="cpp/numeric/special_functions/sph_legendre" since="c++17"/>
    <function name="std::sph_legendref" link="cpp/numeric/special_functions/sph_legendre" since="c++17"/>
    <function name="std::sph_legendrel" link="cpp/numeric/special_functions/sph_legendre" since="c++17"/>
    <function name="std::sph_neumann" link="cpp/numeric/special_functions/sph_neumann" since="c++17"/>
    <function name="std::sph_neumannf" link="cpp/numeric/special_functions/sph_neumann" since="c++17"/>
    <function name="std::sph_neumannl" link="cpp/numeric/special_functions/sph_neumann" since="c++17"/>

    <!-- cpp/numeric/fenv -->

    <function name="std::feclearexcept" link="cpp/numeric/fenv/feclearexcept"/>
    <function name="std::fetestexcept" link="cpp/numeric/fenv/fetestexcept"/>
    <function name="std::feraiseexcept" link="cpp/numeric/fenv/feraiseexcept"/>
    <function name="std::fegetexceptflag" link="cpp/numeric/fenv/feexceptflag"/>
    <function name="std::fesetexceptflag" link="cpp/numeric/fenv/feexceptflag"/>
    <function name="std::fegetround" link="cpp/numeric/fenv/feround"/>
    <function name="std::fesetround" link="cpp/numeric/fenv/feround"/>
    <function name="std::fegetenv" link="cpp/numeric/fenv/feenv"/>
    <function name="std::fesetenv" link="cpp/numeric/fenv/feenv"/>
    <function name="std::feholdexcept" link="cpp/numeric/fenv/feholdexcept"/>
    <function name="std::feupdateenv" link="cpp/numeric/fenv/feupdateenv"/>

    <const name="FE_ALL_EXCEPT" link="cpp/numeric/fenv/FE_exceptions"/>
    <const name="FE_DIVBYZERO" link="cpp/numeric/fenv/FE_exceptions"/>
    <const name="FE_INEXACT" link="cpp/numeric/fenv/FE_exceptions"/>
    <const name="FE_INVALID" link="cpp/numeric/fenv/FE_exceptions"/>
    <const name="FE_OVERFLOW" link="cpp/numeric/fenv/FE_exceptions"/>
    <const name="FE_UNDERFLOW" link="cpp/numeric/fenv/FE_exceptions"/>

    <const name="FE_DOWNWARD" link="cpp/numeric/fenv/FE_round"/>
    <const name="FE_TONEAREST" link="cpp/numeric/fenv/FE_round"/>
    <const name="FE_TOWARDZERO" link="cpp/numeric/fenv/FE_round"/>
    <const name="FE_UPWARD" link="cpp/numeric/fenv/FE_round"/>

    <const name="FE_DFL_ENV" link="cpp/numeric/fenv/FE_DFL_ENV"/>

    <!-- cpp/numeric/complex -->

    <class name="std::complex" link="cpp/numeric/complex">
        <constructor/>
        <function name="operator="/>
        <function name="real"/>
        <function name="imag"/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="operator*=" link="operator_arith"/>
        <function name="operator/=" link="operator_arith"/>

        <overload name="operator+" link="operator_arith2"/>
        <overload name="operator-" link="operator_arith2"/>

        <overload name="operator+" link="operator_arith3"/>
        <overload name="operator-" link="operator_arith3"/>
        <overload name="operator*" link="operator_arith3"/>
        <overload name="operator/" link="operator_arith3"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>

        <overload name="std::real" link="real"/>
        <overload name="std::imag" link="imag"/>
        <overload name="std::abs" link="abs"/>
        <overload name="std::arg" link="arg"/>
        <overload name="std::norm" link="norm"/>
        <overload name="std::conj" link="conj"/>
        <overload name="std::proj" link="proj"/>
        <overload name="std::polar" link="polar"/>

        <overload name="std::exp" link="exp"/>
        <overload name="std::log" link="log"/>
        <overload name="std::log10" link="log10"/>

        <overload name="std::pow" link="pow"/>
        <overload name="std::sqrt" link="sqrt"/>

        <overload name="std::sin" link="sin"/>
        <overload name="std::cos" link="cos"/>
        <overload name="std::tan" link="tan"/>
        <overload name="std::asin" link="asin"/>
        <overload name="std::acos" link="acos"/>
        <overload name="std::atan" link="atan"/>

        <overload name="std::sinh" link="sinh"/>
        <overload name="std::cosh" link="cosh"/>
        <overload name="std::tanh" link="tanh"/>
        <overload name="std::asinh" link="asinh"/>
        <overload name="std::acosh" link="acosh"/>
        <overload name="std::atanh" link="atanh"/>
    </class>
    <function name="std::literals::complex_literals::operator&quot;&quot;i" link="cpp/numeric/complex/operator&quot;&quot;i" since="c++14"/>
    <function name="std::literals::complex_literals::operator&quot;&quot;if" link="cpp/numeric/complex/operator&quot;&quot;i" since="c++14"/>
    <function name="std::literals::complex_literals::operator&quot;&quot;il" link="cpp/numeric/complex/operator&quot;&quot;i" since="c++14"/>

    <!-- cpp/numeric/valarray -->

    <class name="std::valarray" link="cpp/numeric/valarray">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="operator[]" link="operator_at"/>
        <function name="operator+" link="operator_arith"/>
        <function name="operator-" link="operator_arith"/>
        <function name="operator~" link="operator_arith"/>
        <function name="operator!" link="operator_arith"/>
        <function name="operator+=" link="operator_arith2"/>
        <function name="operator-=" link="operator_arith2"/>
        <function name="operator*=" link="operator_arith2"/>
        <function name="operator/=" link="operator_arith2"/>
        <function name="operator%=" link="operator_arith2"/>
        <function name="operator&amp;=" link="operator_arith2"/>
        <function name="operator|=" link="operator_arith2"/>
        <function name="operator^=" link="operator_arith2"/>
        <function name="operator&lt;&lt;=" link="operator_arith2"/>
        <function name="operator&gt;&gt;=" link="operator_arith2"/>
        <function name="swap"/>
        <function name="size"/>
        <function name="resize"/>
        <function name="sum"/>
        <function name="min"/>
        <function name="max"/>
        <function name="shift"/>
        <function name="cshift"/>
        <function name="apply"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="std::begin" link="begin2"/>
        <overload name="std::end" link="end2"/>
        <overload name="operator+" link="operator_arith3"/>
        <overload name="operator-" link="operator_arith3"/>
        <overload name="operator*" link="operator_arith3"/>
        <overload name="operator/" link="operator_arith3"/>
        <overload name="operator%" link="operator_arith3"/>
        <overload name="operator&amp;" link="operator_arith3"/>
        <overload name="operator|" link="operator_arith3"/>
        <overload name="operator^" link="operator_arith3"/>
        <overload name="operator&lt;&lt;" link="operator_arith3"/>
        <overload name="operator&gt;&gt;" link="operator_arith3"/>
        <overload name="operator&amp;&amp;" link="operator_arith3"/>
        <overload name="operator||" link="operator_arith3"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
        <overload name="std::abs" link="abs"/>

        <overload name="std::exp" link="exp"/>
        <overload name="std::log" link="log"/>
        <overload name="std::log10" link="log10"/>

        <overload name="std::pow" link="pow"/>
        <overload name="std::sqrt" link="sqrt"/>

        <overload name="std::sin" link="sin"/>
        <overload name="std::cos" link="cos"/>
        <overload name="std::tan" link="tan"/>
        <overload name="std::asin" link="asin"/>
        <overload name="std::acos" link="acos"/>
        <overload name="std::atan" link="atan"/>
        <overload name="std::atan2" link="atan2"/>

        <overload name="std::sinh" link="sinh"/>
        <overload name="std::cosh" link="cosh"/>
        <overload name="std::tanh" link="tanh"/>
    </class>
    <class name="std::slice" link="cpp/numeric/valarray/slice">
        <constructor link="."/>
        <function name="start" link="."/>
        <function name="size" link="."/>
        <function name="stride" link="."/>
        <overload name="operator==" link="." since="c++20"/>
    </class>
    <class name="std::gslice" link="cpp/numeric/valarray/gslice">
        <constructor link="."/>
        <function name="start" link="."/>
        <function name="size" link="."/>
        <function name="stride" link="."/>
    </class>
    <class name="std::slice_array" link="cpp/numeric/valarray/slice_array">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="operator*=" link="operator_arith"/>
        <function name="operator/=" link="operator_arith"/>
        <function name="operator%=" link="operator_arith"/>
        <function name="operator&amp;=" link="operator_arith"/>
        <function name="operator|=" link="operator_arith"/>
        <function name="operator^=" link="operator_arith"/>
        <function name="operator&lt;&lt;=" link="operator_arith"/>
        <function name="operator&gt;&gt;=" link="operator_arith"/>
    </class>
    <class name="std::gslice_array" link="cpp/numeric/valarray/gslice_array">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="operator*=" link="operator_arith"/>
        <function name="operator/=" link="operator_arith"/>
        <function name="operator%=" link="operator_arith"/>
        <function name="operator&amp;=" link="operator_arith"/>
        <function name="operator|=" link="operator_arith"/>
        <function name="operator^=" link="operator_arith"/>
        <function name="operator&lt;&lt;=" link="operator_arith"/>
        <function name="operator&gt;&gt;=" link="operator_arith"/>
    </class>
    <class name="std::mask_array" link="cpp/numeric/valarray/mask_array">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="operator*=" link="operator_arith"/>
        <function name="operator/=" link="operator_arith"/>
        <function name="operator%=" link="operator_arith"/>
        <function name="operator&amp;=" link="operator_arith"/>
        <function name="operator|=" link="operator_arith"/>
        <function name="operator^=" link="operator_arith"/>
        <function name="operator&lt;&lt;=" link="operator_arith"/>
        <function name="operator&gt;&gt;=" link="operator_arith"/>
    </class>
    <class name="std::indirect_array" link="cpp/numeric/valarray/indirect_array">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="operator+=" link="operator_arith"/>
        <function name="operator-=" link="operator_arith"/>
        <function name="operator*=" link="operator_arith"/>
        <function name="operator/=" link="operator_arith"/>
        <function name="operator%=" link="operator_arith"/>
        <function name="operator&amp;=" link="operator_arith"/>
        <function name="operator|=" link="operator_arith"/>
        <function name="operator^=" link="operator_arith"/>
        <function name="operator&lt;&lt;=" link="operator_arith"/>
        <function name="operator&gt;&gt;=" link="operator_arith"/>
    </class>

    <!-- cpp/numeric/random -->

    <class name="std::linear_congruential_engine" link="cpp/numeric/random/linear_congruential_engine" since="c++11">
        <constructor/>
        <function name="seed"/>
        <function name="operator()"/>
        <function name="discard"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>

        <const name="multiplier" link="."/>
        <const name="increment" link="."/>
        <const name="modulus" link="."/>
        <const name="default_seed" link="."/>
    </class>

    <class name="std::mersenne_twister_engine" link="cpp/numeric/random/mersenne_twister_engine" since="c++11">
        <constructor/>
        <function name="seed"/>
        <function name="operator()"/>
        <function name="discard"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>

        <const name="word_size" link="."/>
        <const name="state_size" link="."/>
        <const name="shift_size" link="."/>
        <const name="mask_bits" link="."/>
        <const name="xor_mask" link="."/>
        <const name="tempering_u" link="."/>
        <const name="tempering_d" link="."/>
        <const name="tempering_s" link="."/>
        <const name="tempering_b" link="."/>
        <const name="tempering_t" link="."/>
        <const name="tempering_c" link="."/>
        <const name="tempering_l" link="."/>
        <const name="initialization_multiplier" link="."/>
        <const name="default_seed" link="."/>
    </class>

    <class name="std::subtract_with_carry_engine" link="cpp/numeric/random/subtract_with_carry_engine" since="c++11">
        <constructor/>
        <function name="seed"/>
        <function name="operator()"/>
        <function name="discard"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>

        <const name="word_size" link="."/>
        <const name="short_lag" link="."/>
        <const name="long_lag" link="."/>
        <const name="default_seed" link="."/>
    </class>

    <class name="std::discard_block_engine" link="cpp/numeric/random/discard_block_engine" since="c++11">
        <constructor/>
        <function name="seed"/>
        <function name="base"/>
        <function name="operator()"/>
        <function name="discard"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>

        <const name="block_size" link="."/>
        <const name="used_block" link="."/>
    </class>

    <class name="std::independent_bits_engine" link="cpp/numeric/random/independent_bits_engine" since="c++11">
        <constructor/>
        <function name="seed"/>
        <function name="base"/>
        <function name="operator()"/>
        <function name="discard"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::shuffle_order_engine" link="cpp/numeric/random/shuffle_order_engine" since="c++11">
        <constructor/>
        <function name="seed"/>
        <function name="base"/>
        <function name="operator()"/>
        <function name="discard"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>

        <const name="table_size" link="."/>
    </class>

    <class name="std::random_device" link="cpp/numeric/random/random_device" since="c++11">
        <constructor/>
        <function name="operator()"/>
        <function name="min"/>
        <function name="max"/>
        <function name="entropy"/>
    </class>

    <typedef name="std::minstd_rand0" alias="std::linear_congruential_engine" since="c++11"/>
    <typedef name="std::minstd_rand" alias="std::linear_congruential_engine" since="c++11"/>
    <typedef name="std::mt19937" alias="std::mersenne_twister_engine" since="c++11"/>
    <typedef name="std::mt19937_64" alias="std::mersenne_twister_engine" since="c++11"/>
    <typedef name="std::ranlux24_base" alias="std::subtract_with_carry_engine" since="c++11"/>
    <typedef name="std::ranlux48_base" alias="std::subtract_with_carry_engine" since="c++11"/>
    <typedef name="std::ranlux24" alias="std::discard_block_engine" since="c++11"/>
    <typedef name="std::ranlux48" alias="std::discard_block_engine" since="c++11"/>
    <typedef name="std::knuth_b" alias="std::shuffle_order_engine" since="c++11"/>
    <typedef name="std::default_random_engine" link="cpp/numeric/random" since="c++11"/>

    <function name="std::srand" link="cpp/numeric/random/srand"/>
    <function name="std::rand" link="cpp/numeric/random/rand"/>
    <const name="RAND_MAX" link="cpp/numeric/random/RAND_MAX"/>

    <function name="std::generate_canonical" link="cpp/numeric/random/generate_canonical" since="c++11"/>

    <class name="std::seed_seq" link="cpp/numeric/random/seed_seq" since="c++11">
        <constructor/>
        <function name="generate"/>
        <function name="size"/>
        <function name="param"/>
    </class>

    <class name="std::uniform_int_distribution" link="cpp/numeric/random/uniform_int_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="a" link="params"/>
        <function name="b" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::uniform_real_distribution" link="cpp/numeric/random/uniform_real_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="a" link="params"/>
        <function name="b" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::bernoulli_distribution" link="cpp/numeric/random/bernoulli_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="p"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::binomial_distribution" link="cpp/numeric/random/binomial_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <overload name="operator()"/>

        <function name="t" link="params"/>
        <function name="p" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::negative_binomial_distribution" link="cpp/numeric/random/negative_binomial_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="k" link="params"/>
        <function name="p" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::geometric_distribution" link="cpp/numeric/random/geometric_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="p"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::poisson_distribution" link="cpp/numeric/random/poisson_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="mean"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::exponential_distribution" link="cpp/numeric/random/exponential_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="lambda"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::gamma_distribution" link="cpp/numeric/random/gamma_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="alpha" link="params"/>
        <function name="beta" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::weibull_distribution" link="cpp/numeric/random/weibull_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="a" link="params"/>
        <function name="b" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::extreme_value_distribution" link="cpp/numeric/random/extreme_value_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="a" link="params"/>
        <function name="b" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::normal_distribution" link="cpp/numeric/random/normal_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="mean" link="params"/>
        <function name="stddev" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::lognormal_distribution" link="cpp/numeric/random/lognormal_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="m" link="params"/>
        <function name="s" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::chi_squared_distribution" link="cpp/numeric/random/chi_squared_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="n"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::cauchy_distribution" link="cpp/numeric/random/cauchy_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="a" link="params"/>
        <function name="b" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::fisher_f_distribution" link="cpp/numeric/random/fisher_f_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="m" link="params"/>
        <function name="n" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::student_t_distribution" link="cpp/numeric/random/student_t_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="n"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::discrete_distribution" link="cpp/numeric/random/discrete_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="probabilities"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::piecewise_constant_distribution" link="cpp/numeric/random/piecewise_constant_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="intervals" link="params"/>
        <function name="densities" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <class name="std::piecewise_linear_distribution" link="cpp/numeric/random/piecewise_linear_distribution" since="c++11">
        <constructor/>
        <function name="reset"/>
        <function name="operator()"/>

        <function name="intervals" link="params"/>
        <function name="densities" link="params"/>
        <function name="param"/>
        <function name="min"/>
        <function name="max"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>

        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
    </class>

    <!-- cpp/numeric/ratio -->

    <class name="std::ratio" link="cpp/numeric/ratio/ratio" since="c++11"/>

    <typedef name="std::yocto" alias="std::ratio" since="c++11"/>
    <typedef name="std::zepto" alias="std::ratio" since="c++11"/>
    <typedef name="std::atto" alias="std::ratio" since="c++11"/>
    <typedef name="std::femto" alias="std::ratio" since="c++11"/>
    <typedef name="std::pico" alias="std::ratio" since="c++11"/>
    <typedef name="std::nano" alias="std::ratio" since="c++11"/>
    <typedef name="std::micro" alias="std::ratio" since="c++11"/>
    <typedef name="std::milli" alias="std::ratio" since="c++11"/>
    <typedef name="std::centi" alias="std::ratio" since="c++11"/>
    <typedef name="std::deci" alias="std::ratio" since="c++11"/>
    <typedef name="std::deca" alias="std::ratio" since="c++11"/>
    <typedef name="std::hecto" alias="std::ratio" since="c++11"/>
    <typedef name="std::kilo" alias="std::ratio" since="c++11"/>
    <typedef name="std::mega" alias="std::ratio" since="c++11"/>
    <typedef name="std::giga" alias="std::ratio" since="c++11"/>
    <typedef name="std::tera" alias="std::ratio" since="c++11"/>
    <typedef name="std::peta" alias="std::ratio" since="c++11"/>
    <typedef name="std::exa" alias="std::ratio" since="c++11"/>
    <typedef name="std::zetta" alias="std::ratio" since="c++11"/>
    <typedef name="std::yotta" alias="std::ratio" since="c++11"/>

    <class name="std::ratio_add" link="cpp/numeric/ratio/ratio_add" since="c++11"/>
    <class name="std::ratio_subtract" link="cpp/numeric/ratio/ratio_subtract" since="c++11"/>
    <class name="std::ratio_multiply" link="cpp/numeric/ratio/ratio_multiply" since="c++11"/>
    <class name="std::ratio_divide" link="cpp/numeric/ratio/ratio_divide" since="c++11"/>

    <class name="std::ratio_equal" link="cpp/numeric/ratio/ratio_equal" since="c++11"/>
    <variable name="std::ratio_equal_v" link="cpp/numeric/ratio/ratio_equal" since="c++17"/>
    <class name="std::ratio_not_equal" link="cpp/numeric/ratio/ratio_not_equal" since="c++11"/>
    <variable name="std::ratio_not_equal_v" link="cpp/numeric/ratio/ratio_not_equal" since="c++17"/>
    <class name="std::ratio_less" link="cpp/numeric/ratio/ratio_less" since="c++11"/>
    <variable name="std::ratio_less_v" link="cpp/numeric/ratio/ratio_less" since="c++17"/>
    <class name="std::ratio_less_equal" link="cpp/numeric/ratio/ratio_less_equal" since="c++11"/>
    <variable name="std::ratio_less_equal_v" link="cpp/numeric/ratio/ratio_less_equal" since="c++17"/>
    <class name="std::ratio_greater" link="cpp/numeric/ratio/ratio_greater" since="c++11"/>
    <variable name="std::ratio_greater_v" link="cpp/numeric/ratio/ratio_greater" since="c++17"/>
    <class name="std::ratio_greater_equal" link="cpp/numeric/ratio/ratio_greater_equal" since="c++11"/>
    <variable name="std::ratio_greater_equal_v" link="cpp/numeric/ratio/ratio_greater_equal" since="c++17"/>

    <!-- cpp/algorithm -->

    <class name="std::execution::sequenced_policy" link="cpp/algorithm/execution_policy_tag_t" since="c++17"/>
    <class name="std::execution::parallel_policy" link="cpp/algorithm/execution_policy_tag_t" since="c++17"/>
    <class name="std::execution::parallel_unsequenced_policy" link="cpp/algorithm/execution_policy_tag_t" since="c++17"/>
    <class name="std::execution::unsequenced_policy" link="cpp/algorithm/execution_policy_tag_t" since="c++20"/>
    <const name="std::execution::seq" link="cpp/algorithm/execution_policy_tag" since="c++17"/>
    <const name="std::execution::par" link="cpp/algorithm/execution_policy_tag" since="c++17"/>
    <const name="std::execution::par_unseq" link="cpp/algorithm/execution_policy_tag" since="c++17"/>
    <const name="std::execution::unseq" link="cpp/algorithm/execution_policy_tag" since="c++20"/>
    <class name="std::is_execution_policy" link="cpp/algorithm/is_execution_policy" since="c++17"/>
    <variable name="std::is_execution_policy_v" link="cpp/algorithm/is_execution_policy" since="c++17"/>

    <function name="std::all_of" link="cpp/algorithm/all_any_none_of" since="c++11"/>
    <function name="std::any_of" link="cpp/algorithm/all_any_none_of" since="c++11"/>
    <function name="std::none_of" link="cpp/algorithm/all_any_none_of" since="c++11"/>
    <function name="std::for_each" link="cpp/algorithm/for_each"/>
    <function name="std::for_each_n" link="cpp/algorithm/for_each_n" since="c++17"/>
    <function name="std::count" link="cpp/algorithm/count"/>
    <function name="std::count_if" link="cpp/algorithm/count"/>
    <function name="std::mismatch" link="cpp/algorithm/mismatch"/>
    <function name="std::find" link="cpp/algorithm/find"/>
    <function name="std::find_if" link="cpp/algorithm/find"/>
    <function name="std::find_if_not" link="cpp/algorithm/find" since="c++11"/>
    <function name="std::find_end" link="cpp/algorithm/find_end"/>
    <function name="std::find_first_of" link="cpp/algorithm/find_first_of"/>
    <function name="std::adjacent_find" link="cpp/algorithm/adjacent_find"/>
    <function name="std::search" link="cpp/algorithm/search"/>
    <function name="std::search_n" link="cpp/algorithm/search_n"/>

    <function name="std::copy" link="cpp/algorithm/copy"/>
    <function name="std::copy_if" link="cpp/algorithm/copy" since="c++11"/>
    <function name="std::copy_n" link="cpp/algorithm/copy_n" since="c++11"/>
    <function name="std::copy_backward" link="cpp/algorithm/copy_backward"/>
    <function name="std::move (algorithm)" link="cpp/algorithm/move" since="c++11"/>
    <function name="std::move_backward" link="cpp/algorithm/move_backward" since="c++11"/>
    <function name="std::fill" link="cpp/algorithm/fill"/>
    <function name="std::fill_n" link="cpp/algorithm/fill_n"/>
    <function name="std::transform" link="cpp/algorithm/transform"/>
    <function name="std::generate" link="cpp/algorithm/generate"/>
    <function name="std::generate_n" link="cpp/algorithm/generate_n"/>
    <function name="std::remove (&lt;algorithm&gt;)" link="cpp/algorithm/remove"/>
    <function name="std::remove_if" link="cpp/algorithm/remove"/>
    <function name="std::remove_copy" link="cpp/algorithm/remove_copy"/>
    <function name="std::remove_copy_if" link="cpp/algorithm/remove_copy"/>
    <function name="std::replace" link="cpp/algorithm/replace"/>
    <function name="std::replace_if" link="cpp/algorithm/replace"/>
    <function name="std::replace_copy" link="cpp/algorithm/replace_copy"/>
    <function name="std::replace_copy_if" link="cpp/algorithm/replace_copy"/>
    <function name="std::swap" link="cpp/algorithm/swap"/>
    <function name="std::swap_ranges" link="cpp/algorithm/swap_ranges"/>
    <function name="std::iter_swap" link="cpp/algorithm/iter_swap"/>
    <function name="std::reverse" link="cpp/algorithm/reverse"/>
    <function name="std::reverse_copy" link="cpp/algorithm/reverse_copy"/>
    <function name="std::rotate" link="cpp/algorithm/rotate"/>
    <function name="std::rotate_copy" link="cpp/algorithm/rotate_copy"/>
    <function name="std::shift_left" link="cpp/algorithm/shift" since="c++20"/>
    <function name="std::shift_right" link="cpp/algorithm/shift" since="c++20"/>
    <function name="std::random_shuffle" link="cpp/algorithm/random_shuffle"/>
    <function name="std::shuffle" link="cpp/algorithm/random_shuffle" since="c++11"/>
    <function name="std::sample" link="cpp/algorithm/sample" since="c++17"/>
    <function name="std::unique" link="cpp/algorithm/unique"/>
    <function name="std::unique_copy" link="cpp/algorithm/unique_copy"/>

    <function name="std::is_partitioned" link="cpp/algorithm/is_partitioned" since="c++11"/>
    <function name="std::partition" link="cpp/algorithm/partition"/>
    <function name="std::partition_copy" link="cpp/algorithm/partition_copy" since="c++11"/>
    <function name="std::stable_partition" link="cpp/algorithm/stable_partition"/>
    <function name="std::partition_point" link="cpp/algorithm/partition_point" since="c++11"/>

    <function name="std::is_sorted" link="cpp/algorithm/is_sorted" since="c++11"/>
    <function name="std::is_sorted_until" link="cpp/algorithm/is_sorted_until" since="c++11"/>
    <function name="std::sort" link="cpp/algorithm/sort"/>
    <function name="std::partial_sort" link="cpp/algorithm/partial_sort"/>
    <function name="std::partial_sort_copy" link="cpp/algorithm/partial_sort_copy"/>
    <function name="std::stable_sort" link="cpp/algorithm/stable_sort"/>
    <function name="std::nth_element" link="cpp/algorithm/nth_element"/>

    <function name="std::lower_bound" link="cpp/algorithm/lower_bound"/>
    <function name="std::upper_bound" link="cpp/algorithm/upper_bound"/>
    <function name="std::binary_search" link="cpp/algorithm/binary_search"/>
    <function name="std::equal_range" link="cpp/algorithm/equal_range"/>

    <function name="std::merge" link="cpp/algorithm/merge"/>
    <function name="std::inplace_merge" link="cpp/algorithm/inplace_merge"/>
    <function name="std::includes" link="cpp/algorithm/includes"/>
    <function name="std::set_difference" link="cpp/algorithm/set_difference"/>
    <function name="std::set_intersection" link="cpp/algorithm/set_intersection"/>
    <function name="std::set_symmetric_difference" link="cpp/algorithm/set_symmetric_difference"/>
    <function name="std::set_union" link="cpp/algorithm/set_union"/>

    <function name="std::is_heap" link="cpp/algorithm/is_heap" since="c++11"/>
    <function name="std::is_heap_until" link="cpp/algorithm/is_heap_until" since="c++11"/>
    <function name="std::make_heap" link="cpp/algorithm/make_heap"/>
    <function name="std::push_heap" link="cpp/algorithm/push_heap"/>
    <function name="std::pop_heap" link="cpp/algorithm/pop_heap"/>
    <function name="std::sort_heap" link="cpp/algorithm/sort_heap"/>

    <function name="std::max" link="cpp/algorithm/max"/>
    <function name="std::max_element" link="cpp/algorithm/max_element"/>
    <function name="std::min" link="cpp/algorithm/min"/>
    <function name="std::min_element" link="cpp/algorithm/min_element"/>
    <function name="std::minmax" link="cpp/algorithm/minmax" since="c++11"/>
    <function name="std::minmax_element" link="cpp/algorithm/minmax_element" since="c++11"/>
    <function name="std::clamp" link="cpp/algorithm/clamp" since="c++17"/>

    <function name="std::equal" link="cpp/algorithm/equal"/>
    <function name="std::lexicographical_compare" link="cpp/algorithm/lexicographical_compare"/>
    <function name="std::lexicographical_compare_three_way" link="cpp/algorithm/lexicographical_compare_three_way" since="c++20"/>

    <function name="std::is_permutation" link="cpp/algorithm/is_permutation" since="c++11"/>
    <function name="std::next_permutation" link="cpp/algorithm/next_permutation"/>
    <function name="std::prev_permutation" link="cpp/algorithm/prev_permutation"/>

    <function name="std::accumulate" link="cpp/algorithm/accumulate"/>
    <function name="std::inner_product" link="cpp/algorithm/inner_product"/>
    <function name="std::adjacent_difference" link="cpp/algorithm/adjacent_difference"/>
    <function name="std::partial_sum" link="cpp/algorithm/partial_sum"/>
    <function name="std::iota" link="cpp/algorithm/iota" since="c++11"/>

    <function name="std::reduce" link="cpp/algorithm/reduce" since="c++17"/>
    <function name="std::inclusive_scan" link="cpp/algorithm/inclusive_scan" since="c++17"/>
    <function name="std::exclusive_scan" link="cpp/algorithm/exclusive_scan" since="c++17"/>
    <function name="std::transform_reduce" link="cpp/algorithm/transform_reduce" since="c++17"/>
    <function name="std::transform_inclusive_scan" link="cpp/algorithm/transform_inclusive_scan" since="c++17"/>
    <function name="std::transform_exclusive_scan" link="cpp/algorithm/transform_exclusive_scan" since="c++17"/>

    <function name="std::ranges::all_of" link="cpp/algorithm/ranges/all_any_none_of" since="c++20"/>
    <function name="std::ranges::any_of" link="cpp/algorithm/ranges/all_any_none_of" since="c++20"/>
    <function name="std::ranges::none_of" link="cpp/algorithm/ranges/all_any_none_of" since="c++20"/>
    <function name="std::ranges::for_each" link="cpp/algorithm/ranges/for_each" since="c++20"/>
    <function name="std::ranges::for_each_n" link="cpp/algorithm/ranges/for_each_n" since="c++20"/>
    <function name="std::ranges::count" link="cpp/algorithm/ranges/count" since="c++20"/>
    <function name="std::ranges::count_if" link="cpp/algorithm/ranges/count" since="c++20"/>
    <function name="std::ranges::mismatch" link="cpp/algorithm/ranges/mismatch" since="c++20"/>
    <function name="std::ranges::find" link="cpp/algorithm/ranges/find" since="c++20"/>
    <function name="std::ranges::find_if" link="cpp/algorithm/ranges/find" since="c++20"/>
    <function name="std::ranges::find_if_not" link="cpp/algorithm/ranges/find" since="c++20"/>
    <function name="std::ranges::find_end" link="cpp/algorithm/ranges/find_end" since="c++20"/>
    <function name="std::ranges::find_first_of" link="cpp/algorithm/ranges/find_first_of" since="c++20"/>
    <function name="std::ranges::adjacent_find" link="cpp/algorithm/ranges/adjacent_find" since="c++20"/>
    <function name="std::ranges::search" link="cpp/algorithm/ranges/search" since="c++20"/>
    <function name="std::ranges::search_n" link="cpp/algorithm/ranges/search_n" since="c++20"/>

    <function name="std::ranges::copy" link="cpp/algorithm/ranges/copy" since="c++20"/>
    <function name="std::ranges::copy_if" link="cpp/algorithm/ranges/copy" since="c++20"/>
    <function name="std::ranges::copy_n" link="cpp/algorithm/ranges/copy_n" since="c++20"/>
    <function name="std::ranges::copy_backward" link="cpp/algorithm/ranges/copy_backward" since="c++20"/>
    <function name="std::ranges::move" link="cpp/algorithm/ranges/move" since="c++20"/>
    <function name="std::ranges::move_backward" link="cpp/algorithm/ranges/move_backward" since="c++20"/>
    <function name="std::ranges::fill" link="cpp/algorithm/ranges/fill" since="c++20"/>
    <function name="std::ranges::fill_n" link="cpp/algorithm/ranges/fill_n" since="c++20"/>
    <function name="std::ranges::transform" link="cpp/algorithm/ranges/transform" since="c++20"/>
    <function name="std::ranges::generate" link="cpp/algorithm/ranges/generate" since="c++20"/>
    <function name="std::ranges::generate_n" link="cpp/algorithm/ranges/generate_n" since="c++20"/>
    <function name="std::ranges::remove" link="cpp/algorithm/ranges/remove" since="c++20"/>
    <function name="std::ranges::remove_if" link="cpp/algorithm/ranges/remove" since="c++20"/>
    <function name="std::ranges::remove_copy" link="cpp/algorithm/ranges/remove_copy" since="c++20"/>
    <function name="std::ranges::remove_copy_if" link="cpp/algorithm/ranges/remove_copy" since="c++20"/>
    <function name="std::ranges::replace" link="cpp/algorithm/ranges/replace" since="c++20"/>
    <function name="std::ranges::replace_if" link="cpp/algorithm/ranges/replace" since="c++20"/>
    <function name="std::ranges::replace_copy" link="cpp/algorithm/ranges/replace_copy" since="c++20"/>
    <function name="std::ranges::replace_copy_if" link="cpp/algorithm/ranges/replace_copy" since="c++20"/>
    <function name="std::ranges::swap_ranges" link="cpp/algorithm/ranges/swap_ranges" since="c++20"/>
    <function name="std::ranges::reverse" link="cpp/algorithm/ranges/reverse" since="c++20"/>
    <function name="std::ranges::reverse_copy" link="cpp/algorithm/ranges/reverse_copy" since="c++20"/>
    <function name="std::ranges::rotate" link="cpp/algorithm/ranges/rotate" since="c++20"/>
    <function name="std::ranges::rotate_copy" link="cpp/algorithm/ranges/rotate_copy" since="c++20"/>
    <function name="std::ranges::shuffle" link="cpp/algorithm/ranges/shuffle" since="c++20"/>
    <function name="std::ranges::sample" link="cpp/algorithm/ranges/sample" since="c++20"/>
    <function name="std::ranges::unique" link="cpp/algorithm/ranges/unique" since="c++20"/>
    <function name="std::ranges::unique_copy" link="cpp/algorithm/ranges/unique_copy" since="c++20"/>

    <function name="std::ranges::is_partitioned" link="cpp/algorithm/ranges/is_partitioned" since="c++20"/>
    <function name="std::ranges::partition" link="cpp/algorithm/ranges/partition" since="c++20"/>
    <function name="std::ranges::partition_copy" link="cpp/algorithm/ranges/partition_copy" since="c++20"/>
    <function name="std::ranges::stable_partition" link="cpp/algorithm/ranges/stable_partition" since="c++20"/>
    <function name="std::ranges::partition_point" link="cpp/algorithm/ranges/partition_point" since="c++20"/>

    <function name="std::ranges::is_sorted" link="cpp/algorithm/ranges/is_sorted" since="c++20"/>
    <function name="std::ranges::is_sorted_until" link="cpp/algorithm/ranges/is_sorted_until" since="c++20"/>
    <function name="std::ranges::sort" link="cpp/algorithm/ranges/sort" since="c++20"/>
    <function name="std::ranges::partial_sort" link="cpp/algorithm/ranges/partial_sort" since="c++20"/>
    <function name="std::ranges::partial_sort_copy" link="cpp/algorithm/ranges/partial_sort_copy" since="c++20"/>
    <function name="std::ranges::stable_sort" link="cpp/algorithm/ranges/stable_sort" since="c++20"/>
    <function name="std::ranges::nth_element" link="cpp/algorithm/ranges/nth_element" since="c++20"/>

    <function name="std::ranges::lower_bound" link="cpp/algorithm/ranges/lower_bound" since="c++20"/>
    <function name="std::ranges::upper_bound" link="cpp/algorithm/ranges/upper_bound" since="c++20"/>
    <function name="std::ranges::binary_search" link="cpp/algorithm/ranges/binary_search" since="c++20"/>
    <function name="std::ranges::equal_range" link="cpp/algorithm/ranges/equal_range" since="c++20"/>

    <function name="std::ranges::merge" link="cpp/algorithm/ranges/merge" since="c++20"/>
    <function name="std::ranges::inplace_merge" link="cpp/algorithm/ranges/inplace_merge" since="c++20"/>
    <function name="std::ranges::includes" link="cpp/algorithm/ranges/includes" since="c++20"/>
    <function name="std::ranges::set_difference" link="cpp/algorithm/ranges/set_difference" since="c++20"/>
    <function name="std::ranges::set_intersection" link="cpp/algorithm/ranges/set_intersection" since="c++20"/>
    <function name="std::ranges::set_symmetric_difference" link="cpp/algorithm/ranges/set_symmetric_difference" since="c++20"/>
    <function name="std::ranges::set_union" link="cpp/algorithm/ranges/set_union" since="c++20"/>

    <function name="std::ranges::is_heap" link="cpp/algorithm/ranges/is_heap" since="c++20"/>
    <function name="std::ranges::is_heap_until" link="cpp/algorithm/ranges/is_heap_until" since="c++20"/>
    <function name="std::ranges::make_heap" link="cpp/algorithm/ranges/make_heap" since="c++20"/>
    <function name="std::ranges::push_heap" link="cpp/algorithm/ranges/push_heap" since="c++20"/>
    <function name="std::ranges::pop_heap" link="cpp/algorithm/ranges/pop_heap" since="c++20"/>
    <function name="std::ranges::sort_heap" link="cpp/algorithm/ranges/sort_heap" since="c++20"/>

    <function name="std::ranges::max" link="cpp/algorithm/ranges/max" since="c++20"/>
    <function name="std::ranges::max_element" link="cpp/algorithm/ranges/max_element" since="c++20"/>
    <function name="std::ranges::min" link="cpp/algorithm/ranges/min" since="c++20"/>
    <function name="std::ranges::min_element" link="cpp/algorithm/ranges/min_element" since="c++20"/>
    <function name="std::ranges::minmax" link="cpp/algorithm/ranges/minmax" since="c++20"/>
    <function name="std::ranges::minmax_element" link="cpp/algorithm/ranges/minmax_element" since="c++20"/>
    <function name="std::ranges::clamp" link="cpp/algorithm/ranges/clamp" since="c++20"/>

    <function name="std::ranges::equal" link="cpp/algorithm/ranges/equal" since="c++20"/>
    <function name="std::ranges::lexicographical_compare" link="cpp/algorithm/ranges/lexicographical_compare" since="c++20"/>

    <function name="std::ranges::is_permutation" link="cpp/algorithm/ranges/is_permutation" since="c++20"/>
    <function name="std::ranges::next_permutation" link="cpp/algorithm/ranges/next_permutation" since="c++20"/>
    <function name="std::ranges::prev_permutation" link="cpp/algorithm/ranges/prev_permutation" since="c++20"/>

    <function name="std::gcd" link="cpp/numeric/gcd" since="c++17"/>
    <function name="std::lcm" link="cpp/numeric/lcm" since="c++17"/>

    <function name="std::qsort" link="cpp/algorithm/qsort"/>
    <function name="std::bsearch" link="cpp/algorithm/bsearch"/>

    <!-- cpp/io -->

    <class name="std::basic_streambuf" link="cpp/io/basic_streambuf">
        <constructor/>
        <destructor/>

        <function name="pubimbue"/>
        <function name="getloc"/>

        <function name="pubsetbuf"/>
        <function name="pubseekoff"/>
        <function name="pubseekpos"/>
        <function name="pubsync"/>

        <function name="in_avail"/>
        <function name="snextc"/>
        <function name="sbumpc"/>
        <function name="sgetc"/>
        <function name="sgetn"/>

        <function name="sputc"/>
        <function name="sputn"/>

        <function name="sputbackc"/>
        <function name="sungetc"/>

        <function name="operator="/>
        <function name="swap"/>

        <function name="imbue" link="pubimbue"/>

        <function name="setbuf" link="pubsetbuf"/>
        <function name="seekoff" link="pubseekoff"/>
        <function name="seekpos" link="pubseekpos"/>
        <function name="sync" link="pubsync"/>

        <function name="showmanyc"/>
        <function name="underflow"/>
        <function name="uflow"/>
        <function name="xsgetn" link="sgetn"/>
        <function name="eback" link="gptr"/>
        <function name="gptr"/>
        <function name="egptr" link="gptr"/>
        <function name="gbump"/>
        <function name="setg"/>

        <function name="xsputn" link="sputn"/>
        <function name="overflow"/>
        <function name="pbase" link="pptr"/>
        <function name="pptr"/>
        <function name="epptr" link="pptr"/>

        <function name="pbump"/>
        <function name="setp"/>

        <function name="pbackfail"/>
    </class>

    <class name="std::basic_filebuf" link="cpp/io/basic_filebuf">
        <inherits name="std::basic_streambuf"/>

        <constructor/>
        <destructor/>

        <function name="operator="/>
        <function name="swap"/>

        <function name="is_open"/>
        <function name="open"/>
        <function name="close"/>

        <function name="showmanyc"/>
        <function name="underflow"/>
        <function name="uflow"/>
        <function name="pbackfail"/>
        <function name="overflow"/>
        <function name="setbuf"/>
        <function name="seekoff"/>
        <function name="seekpos"/>
        <function name="sync"/>
        <function name="imbue"/>

        <overload name="std::swap" link="swap2"/>
    </class>

    <class name="std::basic_stringbuf" link="cpp/io/basic_stringbuf">
        <inherits name="std::basic_streambuf"/>

        <constructor/>
        <function name="operator="/>
        <function name="swap"/>
        <function name="str"/>

        <function name="underflow"/>
        <function name="pbackfail"/>
        <function name="overflow"/>
        <function name="setbuf"/>
        <function name="seekoff"/>
        <function name="seekpos"/>

        <overload name="std::swap" link="swap2"/>
    </class>

    <class name="std::basic_syncbuf" link="cpp/io/basic_syncbuf" since="c++20">
        <inherits name="std::basic_streambuf"/>

        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="swap"/>

        <function name="emit"/>
        <function name="get_wrapped"/>
        <function name="get_allocator"/>
        <function name="set_emit_on_sync"/>

        <function name="sync"/>

        <overload name="std::swap" link="swap2"/>
    </class>

    <class name="std::ios_base" link="cpp/io/ios_base">
        <constructor/>
        <destructor/>
        <function name="flags"/>
        <function name="setf"/>
        <function name="unsetf"/>
        <function name="precision"/>
        <function name="width"/>

        <function name="imbue"/>
        <function name="getloc"/>

        <function name="xalloc"/>
        <function name="iword"/>
        <function name="pword"/>

        <function name="register_callback"/>
        <function name="sync_with_stdio"/>

        <class name="failure">
            <inherits name="std::exception"/>
            <constructor link="."/>
        </class>

        <enum name="openmode"/>
        <const name="app" link="openmode"/>
        <const name="binary" link="openmode"/>
        <const name="in" link="openmode"/>
        <const name="out" link="openmode"/>
        <const name="trunc" link="openmode"/>
        <const name="ate" link="openmode"/>

        <enum name="fmtflags"/>
        <const name="dec" link="fmtflags"/>
        <const name="oct" link="fmtflags"/>
        <const name="hex" link="fmtflags"/>
        <const name="basefield" link="fmtflags"/>
        <const name="left" link="fmtflags"/>
        <const name="right" link="fmtflags"/>
        <const name="internal" link="fmtflags"/>
        <const name="adjustfield" link="fmtflags"/>
        <const name="scientific" link="fmtflags"/>
        <const name="fixed" link="fmtflags"/>
        <const name="floatfield" link="fmtflags"/>
        <const name="boolalpha" link="fmtflags"/>
        <const name="showbase" link="fmtflags"/>
        <const name="showpoint" link="fmtflags"/>
        <const name="showpos" link="fmtflags"/>
        <const name="skipws" link="fmtflags"/>
        <const name="unitbuf" link="fmtflags"/>
        <const name="uppercase" link="fmtflags"/>

        <enum name="iostate"/>
        <const name="goodbit" link="iostate"/>
        <const name="badbit" link="iostate"/>
        <const name="failbit" link="iostate"/>
        <const name="eofbit" link="iostate"/>

        <enum name="seekdir"/>
        <const name="beg" link="seekdir"/>
        <const name="cur" link="seekdir"/>
        <const name="end" link="seekdir"/>

        <enum name="event"/>
        <const name="erase_event" link="event"/>
        <const name="imbue_event" link="event"/>
        <const name="copyfmt_event" link="event"/>

        <typedef name="event_callback"/>

        <class name="Init"/>
    </class>

    <class name="std::basic_ios" link="cpp/io/basic_ios">
        <inherits name="std::ios_base"/>
        <constructor/>
        <destructor/>

        <function name="good"/>
        <function name="eof"/>
        <function name="fail"/>
        <function name="bad"/>
        <function name="operator!"/>
        <function name="operator bool" link="operator_bool"/>
        <function name="rdstate"/>
        <function name="setstate"/>
        <function name="clear"/>

        <function name="copyfmt"/>
        <function name="fill"/>

        <function name="exceptions"/>
        <function name="imbue"/>
        <function name="rdbuf"/>
        <function name="tie"/>
        <function name="narrow"/>
        <function name="widen"/>

        <function name="init"/>
        <function name="move"/>
        <function name="swap"/>
        <function name="set_rdbuf"/>
    </class>

    <class name="std::basic_istream" link="cpp/io/basic_istream">
        <inherits name="std::basic_ios"/>
        <constructor/>
        <destructor/>
        <function name="operator=" since="c++11"/>
        <function name="swap" since="c++11"/>

        <function name="operator&gt;&gt;" link="operator_gtgt"/>
        <function name="get"/>
        <function name="peek"/>
        <function name="unget"/>
        <function name="putback"/>
        <function name="getline"/>
        <function name="ignore"/>
        <function name="read"/>
        <function name="readsome"/>
        <function name="gcount"/>

        <function name="tellg"/>
        <function name="seekg"/>
        <function name="sync"/>

        <class name="sentry">
            <constructor link="."/>
            <destructor link="."/>
            <function name="operator bool" link="."/>
        </class>

        <overload name="operator&gt;&gt;" link="operator_gtgt2"/>
    </class>

    <class name="std::basic_ostream" link="cpp/io/basic_ostream">
        <inherits name="std::basic_ios"/>
        <constructor/>
        <destructor/>
        <function name="operator=" since="c++11"/>
        <function name="swap" since="c++11"/>

        <function name="operator&lt;&lt;" link="operator_ltlt"/>
        <function name="put"/>
        <function name="write"/>
        <function name="tellp"/>
        <function name="seekp"/>
        <function name="flush"/>

        <class name="sentry">
            <constructor link="."/>
            <destructor link="."/>
            <function name="operator bool" link="."/>
        </class>

        <overload name="operator&lt;&lt;" link="operator_ltlt2"/>
    </class>

    <class name="std::basic_iostream" link="cpp/io/basic_iostream">
        <inherits name="std::basic_istream"/>
        <inherits name="std::basic_ostream"/>
        <constructor/>
        <destructor/>
        <function name="operator=" since="c++11"/>
        <function name="swap" since="c++11"/>
    </class>

    <class name="std::basic_ifstream" link="cpp/io/basic_ifstream">
        <inherits name="std::basic_istream"/>
        <constructor/>
        <function name="operator=" since="c++11"/>
        <function name="swap" since="c++11"/>
        <function name="rdbuf"/>

        <function name="is_open"/>
        <function name="open"/>
        <function name="close"/>

        <overload name="std::swap" link="swap2" since="c++11"/>
    </class>

    <class name="std::basic_ofstream" link="cpp/io/basic_ofstream">
        <inherits name="std::basic_ostream"/>
        <constructor/>
        <function name="operator=" since="c++11"/>
        <function name="swap" since="c++11"/>
        <function name="rdbuf"/>

        <function name="is_open"/>
        <function name="open"/>
        <function name="close"/>

        <overload name="std::swap" link="swap2" since="c++11"/>
    </class>

    <class name="std::basic_fstream" link="cpp/io/basic_fstream">
        <inherits name="std::basic_iostream"/>
        <constructor/>
        <function name="operator=" since="c++11"/>
        <function name="swap" since="c++11"/>
        <function name="rdbuf"/>

        <function name="is_open"/>
        <function name="open"/>
        <function name="close"/>

        <overload name="std::swap" link="swap2" since="c++11"/>
    </class>

    <class name="std::basic_istringstream" link="cpp/io/basic_istringstream">
        <inherits name="std::basic_istream"/>
        <constructor/>
        <function name="operator=" since="c++11"/>
        <function name="swap" since="c++11"/>
        <function name="rdbuf"/>

        <function name="str"/>

        <overload name="std::swap" link="swap2" since="c++11"/>
    </class>

    <class name="std::basic_ostringstream" link="cpp/io/basic_ostringstream">
        <inherits name="std::basic_ostream"/>
        <constructor/>
        <function name="operator=" since="c++11"/>
        <function name="swap" since="c++11"/>
        <function name="rdbuf"/>

        <function name="str"/>

        <overload name="std::swap" link="swap2" since="c++11"/>
    </class>

    <class name="std::basic_stringstream" link="cpp/io/basic_stringstream">
        <inherits name="std::basic_iostream"/>
        <constructor/>
        <function name="operator=" since="c++11"/>
        <function name="swap" since="c++11"/>
        <function name="rdbuf"/>

        <function name="str"/>

        <overload name="std::swap" link="swap2" since="c++11"/>
    </class>

    <class name="std::basic_osyncstream" link="cpp/io/basic_osyncstream" since="c++20">
        <inherits name="std::basic_ostream"/>

        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="rdbuf"/>
        <function name="get_wrapped"/>
        <function name="emit"/>
    </class>

    <typedef name="std::streambuf" alias="std::basic_streambuf"/>
    <typedef name="std::filebuf" alias="std::basic_filebuf"/>
    <typedef name="std::stringbuf" alias="std::basic_stringbuf"/>
    <typedef name="std::syncbuf" alias="std::basic_syncbuf" since="c++20"/>
    <typedef name="std::ios" alias="std::basic_ios"/>
    <typedef name="std::istream" alias="std::basic_istream"/>
    <typedef name="std::ostream" alias="std::basic_ostream"/>
    <typedef name="std::iostream" alias="std::basic_iostream"/>
    <typedef name="std::ifstream" alias="std::basic_ifstream"/>
    <typedef name="std::ofstream" alias="std::basic_ofstream"/>
    <typedef name="std::fstream" alias="std::basic_fstream"/>
    <typedef name="std::istringstream" alias="std::basic_istringstream"/>
    <typedef name="std::ostringstream" alias="std::basic_ostringstream"/>
    <typedef name="std::stringstream" alias="std::basic_stringstream"/>
    <typedef name="std::osyncstream" alias="std::basic_osyncstream" since="c++20"/>

    <typedef name="std::wstreambuf" alias="std::basic_streambuf"/>
    <typedef name="std::wfilebuf" alias="std::basic_filebuf"/>
    <typedef name="std::wstringbuf" alias="std::basic_stringbuf"/>
    <typedef name="std::wsyncbuf" alias="std::basic_syncbuf" since="c++20"/>
    <typedef name="std::wios" alias="std::basic_ios"/>
    <typedef name="std::wistream" alias="std::basic_istream"/>
    <typedef name="std::wostream" alias="std::basic_ostream"/>
    <typedef name="std::wiostream" alias="std::basic_iostream"/>
    <typedef name="std::wifstream" alias="std::basic_ifstream"/>
    <typedef name="std::wofstream" alias="std::basic_ofstream"/>
    <typedef name="std::wfstream" alias="std::basic_fstream"/>
    <typedef name="std::wistringstream" alias="std::basic_istringstream"/>
    <typedef name="std::wostringstream" alias="std::basic_ostringstream"/>
    <typedef name="std::wstringstream" alias="std::basic_stringstream"/>
    <typedef name="std::wosyncstream" alias="std::basic_osyncstream" since="c++20"/>

    <!-- deprecated classes -->
    <class name="std::strstreambuf" link="cpp/io/strstreambuf">
        <inherits name="std::basic_streambuf"/>

        <constructor/>
        <destructor/>
        <function name="freeze"/>
        <function name="str"/>
        <function name="pcount"/>

        <function name="underflow"/>
        <function name="pbackfail"/>
        <function name="overflow"/>
        <function name="setbuf"/>
        <function name="seekoff"/>
        <function name="seekpos"/>
    </class>

    <class name="std::istrstream" link="cpp/io/istrstream">
        <inherits name="std::basic_istream"/>

        <constructor/>
        <destructor/>
        <function name="rdbuf"/>
        <function name="str"/>
    </class>

    <class name="std::ostrstream" link="cpp/io/ostrstream">
        <inherits name="std::basic_ostream"/>

        <constructor/>
        <destructor/>
        <function name="rdbuf"/>
        <function name="str"/>
        <function name="freeze"/>
        <function name="pcount"/>
    </class>

    <class name="std::strstream" link="cpp/io/strstream">
        <inherits name="std::basic_iostream"/>

        <constructor/>
        <destructor/>
        <function name="rdbuf"/>
        <function name="str"/>
        <function name="freeze"/>
        <function name="pcount"/>
    </class>

    <!-- class doesn't fit here, but whatever -->
    <class name="std::cin" link="cpp/io/cin"/>
    <class name="std::wcin" link="cpp/io/cin"/>
    <class name="std::cout" link="cpp/io/cout"/>
    <class name="std::wcout" link="cpp/io/cout"/>
    <class name="std::cerr" link="cpp/io/cerr"/>
    <class name="std::wcerr" link="cpp/io/cerr"/>
    <class name="std::clog" link="cpp/io/clog"/>
    <class name="std::wclog" link="cpp/io/clog"/>

    <!-- manipulators-->

    <function name="std::boolalpha" link="cpp/io/manip/boolalpha"/>
    <function name="std::noboolalpha" link="cpp/io/manip/boolalpha"/>
    <function name="std::showbase" link="cpp/io/manip/showbase"/>
    <function name="std::noshowbase" link="cpp/io/manip/showbase"/>
    <function name="std::showpoint" link="cpp/io/manip/showpoint"/>
    <function name="std::noshowpoint" link="cpp/io/manip/showpoint"/>
    <function name="std::showpos" link="cpp/io/manip/showpos"/>
    <function name="std::noshowpos" link="cpp/io/manip/showpos"/>
    <function name="std::skipws" link="cpp/io/manip/skipws"/>
    <function name="std::noskipws" link="cpp/io/manip/skipws"/>
    <function name="std::uppercase" link="cpp/io/manip/uppercase"/>
    <function name="std::nouppercase" link="cpp/io/manip/uppercase"/>
    <function name="std::unitbuf" link="cpp/io/manip/unitbuf"/>
    <function name="std::nounitbuf" link="cpp/io/manip/unitbuf"/>
    <function name="std::internal" link="cpp/io/manip/left"/>
    <function name="std::left" link="cpp/io/manip/left"/>
    <function name="std::right" link="cpp/io/manip/left"/>
    <function name="std::dec" link="cpp/io/manip/hex"/>
    <function name="std::hex" link="cpp/io/manip/hex"/>
    <function name="std::oct" link="cpp/io/manip/hex"/>
    <function name="std::fixed" link="cpp/io/manip/fixed"/>
    <function name="std::scientific" link="cpp/io/manip/fixed"/>
    <function name="std::hexfloat" link="cpp/io/manip/fixed" since="c++11"/>
    <function name="std::defaultfloat" link="cpp/io/manip/fixed" since="c++11"/>

    <function name="std::ws" link="cpp/io/manip/ws"/>
    <function name="std::ends" link="cpp/io/manip/ends"/>
    <function name="std::flush" link="cpp/io/manip/flush"/>
    <function name="std::endl" link="cpp/io/manip/endl"/>
    <function name="std::emit_on_flush" link="cpp/io/manip/emit_on_flush" since="c++20"/>
    <function name="std::no_emit_on_flush" link="cpp/io/manip/emit_on_flush" since="c++20"/>
    <function name="std::flush_emit" link="cpp/io/manip/flush_emit" since="c++20"/>

    <function name="std::resetiosflags" link="cpp/io/manip/resetiosflags"/>
    <function name="std::setiosflags" link="cpp/io/manip/setiosflags"/>
    <function name="std::setbase" link="cpp/io/manip/setbase"/>
    <function name="std::setfill" link="cpp/io/manip/setfill"/>
    <function name="std::setprecision" link="cpp/io/manip/setprecision"/>
    <function name="std::setw" link="cpp/io/manip/setw"/>
    <function name="std::get_money" link="cpp/io/manip/get_money" since="c++11"/>
    <function name="std::put_money" link="cpp/io/manip/put_money" since="c++11"/>
    <function name="std::get_time" link="cpp/io/manip/get_time" since="c++11"/>
    <function name="std::put_time" link="cpp/io/manip/put_time" since="c++11"/>

    <!-- misc -->
    <function name="std::quoted" link="cpp/io/manip/quoted" since="c++14"/>
    <typedef name="std::streamoff" link="cpp/io/streamoff"/>
    <typedef name="std::streamsize" link="cpp/io/streamsize"/>
    <class name="std::fpos" link="cpp/io/fpos">
        <function name="state"/>
    </class>

    <typedef name="std::streampos" alias="std::fpos"/>
    <typedef name="std::u8streampos" alias="std::fpos" since="c++20"/>
    <typedef name="std::u16streampos" alias="std::fpos" since="c++11"/>
    <typedef name="std::u32streampos" alias="std::fpos" since="c++11"/>
    <typedef name="std::wstreampos" alias="std::fpos"/>

    <class name="std::io_errc" link="cpp/io/io_errc" since="c++11">
        <const name="stream" link="."/>
        <specialization name="std::is_error_code_enum" link="is_error_code_enum"/>
        <overload name="std::make_error_code" link="make_error_code"/>
        <overload name="std::make_error_condition" link="make_error_condition"/>
    </class>

    <function name="std::iostream_category" link="cpp/io/iostream_category" since="c++11"/>

    <!-- cpp/io/c -->

    <function name="std::fopen" link="cpp/io/c/fopen"/>
    <function name="std::freopen" link="cpp/io/c/freopen"/>
    <function name="std::fflush" link="cpp/io/c/fflush"/>
    <function name="std::fclose" link="cpp/io/c/fclose"/>
    <function name="std::fwide" link="cpp/io/c/fwide"/>
    <function name="std::setbuf" link="cpp/io/c/setbuf"/>
    <function name="std::setvbuf" link="cpp/io/c/setvbuf"/>
    <function name="std::fread" link="cpp/io/c/fread"/>
    <function name="std::fwrite" link="cpp/io/c/fwrite"/>
    <function name="std::fgetc" link="cpp/io/c/fgetc"/>
    <function name="std::getc" link="cpp/io/c/fgetc"/>
    <function name="std::fgets" link="cpp/io/c/fgets"/>
    <function name="std::fputc" link="cpp/io/c/fputc"/>
    <function name="std::putc" link="cpp/io/c/fputc"/>
    <function name="std::fputs" link="cpp/io/c/fputs"/>
    <function name="std::getchar" link="cpp/io/c/getchar"/>
    <function name="std::gets" link="cpp/io/c/gets"/>
    <function name="std::putchar" link="cpp/io/c/putchar"/>
    <function name="std::puts" link="cpp/io/c/puts"/>
    <function name="std::ungetc" link="cpp/io/c/ungetc"/>
    <function name="std::fgetwc" link="cpp/io/c/fgetwc"/>
    <function name="std::getwc" link="cpp/io/c/fgetwc"/>
    <function name="std::fgetws" link="cpp/io/c/fgetws"/>
    <function name="std::fputwc" link="cpp/io/c/fputwc"/>
    <function name="std::putwc" link="cpp/io/c/fputwc"/>
    <function name="std::fputws" link="cpp/io/c/fputws"/>
    <function name="std::getwchar" link="cpp/io/c/getwchar"/>
    <function name="std::putwchar" link="cpp/io/c/putwchar"/>
    <function name="std::ungetwc" link="cpp/io/c/ungetwc"/>
    <function name="std::scanf" link="cpp/io/c/fscanf"/>
    <function name="std::fscanf" link="cpp/io/c/fscanf"/>
    <function name="std::sscanf" link="cpp/io/c/fscanf"/>
    <function name="std::vscanf" link="cpp/io/c/vfscanf"/>
    <function name="std::vfscanf" link="cpp/io/c/vfscanf"/>
    <function name="std::vsscanf" link="cpp/io/c/vfscanf"/>
    <function name="std::printf" link="cpp/io/c/fprintf"/>
    <function name="std::fprintf" link="cpp/io/c/fprintf"/>
    <function name="std::sprintf" link="cpp/io/c/fprintf"/>
    <function name="std::snprintf" link="cpp/io/c/fprintf"/>
    <function name="std::vprintf" link="cpp/io/c/vfprintf"/>
    <function name="std::vfprintf" link="cpp/io/c/vfprintf"/>
    <function name="std::vsprintf" link="cpp/io/c/vfprintf"/>
    <function name="std::vsnprintf" link="cpp/io/c/vfprintf"/>
    <function name="std::wscanf" link="cpp/io/c/fwscanf"/>
    <function name="std::fwscanf" link="cpp/io/c/fwscanf"/>
    <function name="std::swscanf" link="cpp/io/c/fwscanf"/>
    <function name="std::vwscanf" link="cpp/io/c/vfwscanf"/>
    <function name="std::vfwscanf" link="cpp/io/c/vfwscanf"/>
    <function name="std::vswscanf" link="cpp/io/c/vfwscanf"/>
    <function name="std::wprintf" link="cpp/io/c/fwprintf"/>
    <function name="std::fwprintf" link="cpp/io/c/fwprintf"/>
    <function name="std::swprintf" link="cpp/io/c/fwprintf"/>
    <function name="std::vwprintf" link="cpp/io/c/vfwprintf"/>
    <function name="std::vfwprintf" link="cpp/io/c/vfwprintf"/>
    <function name="std::vswprintf" link="cpp/io/c/vfwprintf"/>
    <function name="std::ftell" link="cpp/io/c/ftell"/>
    <function name="std::fgetpos" link="cpp/io/c/fgetpos"/>
    <function name="std::fseek" link="cpp/io/c/fseek"/>
    <function name="std::fsetpos" link="cpp/io/c/fsetpos"/>
    <function name="std::rewind" link="cpp/io/c/rewind"/>
    <function name="std::clearerr" link="cpp/io/c/clearerr"/>
    <function name="std::feof" link="cpp/io/c/feof"/>
    <function name="std::ferror" link="cpp/io/c/ferror"/>
    <function name="std::perror" link="cpp/io/c/perror"/>
    <function name="std::remove (&lt;cstdio&gt;)" link="cpp/io/c/remove"/>
    <function name="std::rename" link="cpp/io/c/rename"/>
    <function name="std::tmpfile" link="cpp/io/c/tmpfile"/>
    <function name="std::tmpnam" link="cpp/io/c/tmpnam"/>

    <typedef name="std::FILE" link="cpp/io/c/FILE"/>
    <typedef name="std::fpos_t" link="cpp/io/c/fpos_t"/>
    <const name="stdin" link="cpp/io/c/std_streams"/>
    <const name="stdout" link="cpp/io/c/std_streams"/>
    <const name="stderr" link="cpp/io/c/std_streams"/>
    <const name="EOF" link="cpp/io/c"/>
    <const name="FOPEN_MAX" link="cpp/io/c"/>
    <const name="FILENAME_MAX" link="cpp/io/c"/>
    <const name="BUFSIZ" link="cpp/io/c"/>
    <const name="_IOFBF" link="cpp/io/c"/>
    <const name="_IOLBF" link="cpp/io/c"/>
    <const name="_IONBF" link="cpp/io/c"/>
    <const name="SEEK_SET" link="cpp/io/c"/>
    <const name="SEEK_CUR" link="cpp/io/c"/>
    <const name="SEEK_END" link="cpp/io/c"/>
    <const name="TMP_MAX" link="cpp/io/c"/>
    <const name="L_tmpnam" link="cpp/io/c"/>

    <!-- cpp/locale -->

    <function name="std::isalnum (&lt;clocale&gt;)" link="cpp/locale/isalnum"/>
    <function name="std::isalpha (&lt;clocale&gt;)" link="cpp/locale/isalpha"/>
    <function name="std::islower (&lt;clocale&gt;)" link="cpp/locale/islower"/>
    <function name="std::isupper (&lt;clocale&gt;)" link="cpp/locale/isupper"/>
    <function name="std::isdigit (&lt;clocale&gt;)" link="cpp/locale/isdigit"/>
    <function name="std::isxdigit (&lt;clocale&gt;)" link="cpp/locale/isxdigit"/>
    <function name="std::iscntrl (&lt;clocale&gt;)" link="cpp/locale/iscntrl"/>
    <function name="std::isgraph (&lt;clocale&gt;)" link="cpp/locale/isgraph"/>
    <function name="std::isspace (&lt;clocale&gt;)" link="cpp/locale/isspace"/>
    <function name="std::isblank (&lt;clocale&gt;)" link="cpp/locale/isblank"/>
    <function name="std::isprint (&lt;clocale&gt;)" link="cpp/locale/isprint"/>
    <function name="std::ispunct (&lt;clocale&gt;)" link="cpp/locale/ispunct"/>
    <function name="std::tolower (&lt;clocale&gt;)" link="cpp/locale/tolower"/>
    <function name="std::toupper (&lt;clocale&gt;)" link="cpp/locale/toupper"/>

    <class name="std::locale" link="cpp/locale/locale">
        <constructor/>
        <destructor/>

        <function name="operator="/>
        <function name="combine"/>
        <function name="name"/>
        <function name="operator==" link="operator_cmp"/>
        <function name="operator!=" link="operator_cmp"/>
        <function name="operator()"/>

        <function name="global"/>
        <function name="classic"/>
    </class>

    <class name="std::locale::id" link="cpp/locale/locale/id">
        <constructor/>
    </class>

    <class name="std::locale::facet" link="cpp/locale/locale/facet">
        <constructor/>
    </class>

    <function name="std::use_facet" link="cpp/locale/use_facet"/>
    <function name="std::has_facet" link="cpp/locale/has_facet"/>

    <class name="std::wstring_convert" link="cpp/locale/wstring_convert" since="c++11">
        <constructor/>
        <destructor/>
        <function name="from_bytes"/>
        <function name="to_bytes"/>
        <function name="converted"/>
        <function name="state"/>
    </class>

    <class name="std::wbuffer_convert" link="cpp/locale/wbuffer_convert" since="c++11">
        <constructor/>
        <destructor/>
        <function name="rdbuf"/>
        <function name="state"/>
    </class>

    <class name="std::ctype_base" link="cpp/locale/ctype_base">
        <typedef name="mask" link="."/>
        <const name="space" link="."/>
        <const name="print" link="."/>
        <const name="cntrl" link="."/>
        <const name="upper" link="."/>
        <const name="lower" link="."/>
        <const name="alpha" link="."/>
        <const name="digit" link="."/>
        <const name="punct" link="."/>
        <const name="xdigit" link="."/>
        <const name="blank" link="."/>
        <const name="alnum" link="."/>
        <const name="graph" link="."/>
    </class>

    <class name="std::codecvt_base" link="cpp/locale/codecvt_base">
        <enum name="result" link="."/>
        <const name="ok" link="."/>
        <const name="partial" link="."/>
        <const name="error" link="."/>
        <const name="noconv" link="."/>
    </class>

    <class name="std::messages_base" link="cpp/locale/messages_base">
        <typedef name="catalog" link="."/>
    </class>

    <class name="std::time_base" link="cpp/locale/time_base">
        <enum name="dateorder" link="."/>
        <const name="no_order" link="."/>
        <const name="dmy" link="."/>
        <const name="mdy" link="."/>
        <const name="ymd" link="."/>
        <const name="ydm" link="."/>
    </class>

    <class name="std::money_base" link="cpp/locale/money_base">
        <enum name="part" link="."/>
        <const name="none" link="."/>
        <const name="space" link="."/>
        <const name="symbol" link="."/>
        <const name="sign" link="."/>
        <const name="value" link="."/>
        <class name="pattern" link="."/>
    </class>

    <class name="std::ctype" link="cpp/locale/ctype">
        <inherits name="std::ctype_base"/>

        <constructor/>
        <destructor/>
        <function name="is"/>
        <function name="scan_is"/>
        <function name="scan_not"/>
        <function name="toupper"/>
        <function name="tolower"/>
        <function name="widen"/>
        <function name="narrow"/>

        <!-- protected -->
        <function name="do_is" link="is"/>
        <function name="do_scan_is" link="scan_is"/>
        <function name="do_scan_not" link="scan_not"/>
        <function name="do_toupper" link="toupper"/>
        <function name="do_tolower" link="tolower"/>
        <function name="do_widen" link="widen"/>
        <function name="do_narrow" link="narrow"/>

        <variable name="id" link="."/>
    </class>

    <!-- todo ctype<char> -->

    <class name="std::codecvt" link="cpp/locale/codecvt">
        <inherits name="std::codecvt_base"/>

        <typedef name="intern_type" link="."/>
        <typedef name="extern_type" link="."/>
        <typedef name="state_type" link="."/>

        <constructor/>
        <destructor/>
        <function name="out"/>
        <function name="in"/>
        <function name="unshift"/>
        <function name="encoding"/>
        <function name="always_noconv"/>
        <function name="length"/>
        <function name="max_length"/>

        <!-- protected -->
        <function name="do_out" link="out"/>
        <function name="do_in" link="in"/>
        <function name="do_unshift" link="unshift"/>
        <function name="do_encoding" link="encoding"/>
        <function name="do_always_noconv" link="always_noconv"/>
        <function name="do_length" link="length"/>
        <function name="do_max_length" link="max_length"/>

        <variable name="id" link="."/>
    </class>

    <class name="std::collate" link="cpp/locale/collate">
        <typedef name="char_type" link="."/>
        <typedef name="string_type" link="."/>

        <constructor/>
        <destructor/>
        <function name="compare"/>
        <function name="transform"/>
        <function name="hash"/>

        <!-- protected -->
        <function name="do_compare" link="compare"/>
        <function name="do_transform" link="transform"/>
        <function name="do_hash" link="hash"/>

        <variable name="id" link="."/>
    </class>

    <class name="std::messages" link="cpp/locale/messages">
        <inherits name="std::messages_base"/>

        <typedef name="char_type" link="."/>
        <typedef name="string_type" link="."/>

        <constructor/>
        <destructor/>
        <function name="open"/>
        <function name="get"/>
        <function name="close"/>

        <!-- protected -->
        <function name="do_open" link="open"/>
        <function name="do_get" link="get"/>
        <function name="do_close" link="close"/>

        <variable name="id" link="."/>
    </class>

    <class name="std::time_get" link="cpp/locale/time_get">
        <inherits name="std::time_base"/>

        <typedef name="char_type" link="."/>
        <typedef name="iter_type" link="."/>

        <constructor/>
        <destructor/>
        <function name="date_order"/>
        <function name="get_time"/>
        <function name="get_date"/>
        <function name="get_weekday"/>
        <function name="get_monthname"/>
        <function name="get_year"/>
        <function name="get"/>

        <!-- protected -->
        <function name="do_date_order" link="date_order"/>
        <function name="do_get_time" link="get_time"/>
        <function name="do_get_date" link="get_date"/>
        <function name="do_get_weekday" link="get_weekday"/>
        <function name="do_get_monthname" link="get_monthname"/>
        <function name="do_get_year" link="get_year"/>
        <function name="do_get" link="get"/>

        <variable name="id" link="."/>
    </class>

    <class name="std::time_put" link="cpp/locale/time_put">
        <inherits name="std::time_base"/>

        <typedef name="char_type" link="."/>
        <typedef name="iter_type" link="."/>

        <constructor/>
        <destructor/>
        <function name="put"/>

        <!-- protected -->
        <function name="do_put" link="put"/>

        <variable name="id" link="."/>
    </class>

    <class name="std::num_get" link="cpp/locale/num_get">
        <typedef name="char_type" link="."/>
        <typedef name="iter_type" link="."/>

        <constructor/>
        <destructor/>
        <function name="get"/>

        <!-- protected -->
        <function name="do_get" link="get"/>

        <variable name="id" link="."/>
    </class>

    <class name="std::num_put" link="cpp/locale/num_put">
        <typedef name="char_type" link="."/>
        <typedef name="iter_type" link="."/>

        <constructor/>
        <destructor/>
        <function name="put"/>

        <!-- protected -->
        <function name="do_put" link="put"/>

        <variable name="id" link="."/>
    </class>

    <class name="std::numpunct" link="cpp/locale/numpunct">
        <typedef name="char_type" link="."/>
        <typedef name="string_type" link="."/>

        <constructor/>
        <destructor/>
        <function name="decimal_point"/>
        <function name="thousands_sep"/>
        <function name="grouping"/>
        <function name="truename" link="truefalsename"/>
        <function name="falsename" link="truefalsename"/>

        <!-- protected -->
        <function name="do_decimal_point" link="decimal_point"/>
        <function name="do_thousands_sep" link="thousands_sep"/>
        <function name="do_grouping" link="grouping"/>
        <function name="do_truename" link="truefalsename"/>
        <function name="do_falsename" link="truefalsename"/>

        <variable name="id" link="."/>
    </class>

    <class name="std::money_get" link="cpp/locale/money_get">
        <inherits name="std::money_base"/>

        <typedef name="char_type" link="."/>
        <typedef name="iter_type" link="."/>
        <typedef name="string_type" link="."/>

        <constructor/>
        <destructor/>
        <function name="get"/>

        <!-- protected -->
        <function name="do_get" link="get"/>

        <variable name="id" link="."/>
    </class>

    <class name="std::money_put" link="cpp/locale/money_put">
        <inherits name="std::money_base"/>

        <typedef name="char_type" link="."/>
        <typedef name="iter_type" link="."/>
        <typedef name="string_type" link="."/>

        <constructor/>
        <destructor/>
        <function name="put"/>

        <!-- protected -->
        <function name="do_put" link="put"/>

        <variable name="id" link="."/>
    </class>

    <class name="std::moneypunct" link="cpp/locale/moneypunct">
        <typedef name="char_type" link="."/>
        <typedef name="string_type" link="."/>
        <inherits name="std::money_base"/>

        <constructor/>
        <destructor/>
        <function name="decimal_point"/>
        <function name="thousands_sep"/>
        <function name="grouping"/>
        <function name="curr_symbol"/>
        <function name="positive_sign"/>
        <function name="negative_sign" link="positive_sign"/>
        <function name="frac_digits"/>
        <function name="pos_format"/>
        <function name="neg_format" link="pos_format"/>

        <!-- protected -->
        <function name="do_decimal_point" link="decimal_point"/>
        <function name="do_thousands_sep" link="thousands_sep"/>
        <function name="do_grouping" link="grouping"/>
        <function name="do_curr_symbol" link="curr_symbol"/>
        <function name="do_positive_sign" link="positive_sign"/>
        <function name="do_negative_sign" link="positive_sign"/>
        <function name="do_frac_digits" link="frac_digits"/>
        <function name="do_pos_format" link="pos_format"/>
        <function name="do_neg_format" link="pos_format"/>

        <const name="intl" link="."/>
        <variable name="id" link="."/>
    </class>

    <class name="std::ctype_byname" link="cpp/locale/ctype_byname">
        <inherits name="std::ctype"/>
        <constructor link="."/>
        <destructor link="."/>
    </class>

    <class name="std::codecvt_byname" link="cpp/locale/codecvt_byname">
        <inherits name="std::codecvt"/>
        <constructor link="."/>
        <destructor link="."/>
    </class>

    <class name="std::messages_byname" link="cpp/locale/messages_byname">
        <inherits name="std::messages"/>
        <constructor link="."/>
        <destructor link="."/>
    </class>

    <class name="std::collate_byname" link="cpp/locale/collate_byname">
        <inherits name="std::collate"/>
        <constructor link="."/>
        <destructor link="."/>
    </class>

    <class name="std::time_get_byname" link="cpp/locale/time_get_byname">
        <inherits name="std::time_get"/>
        <constructor link="."/>
        <destructor link="."/>
    </class>

    <class name="std::time_put_byname" link="cpp/locale/time_put_byname">
        <inherits name="std::time_put"/>
        <constructor link="."/>
        <destructor link="."/>
    </class>

    <class name="std::numpunct_byname" link="cpp/locale/numpunct_byname">
        <inherits name="std::numpunct"/>
        <constructor link="."/>
        <destructor link="."/>
    </class>

    <class name="std::moneypunct_byname" link="cpp/locale/moneypunct_byname">
        <inherits name="std::moneypunct"/>
        <constructor link="."/>
        <destructor link="."/>
    </class>

    <class name="std::codecvt_utf8" link="cpp/locale/codecvt_utf8">
        <inherits name="std::codecvt"/>
    </class>
    <class name="std::codecvt_utf16" link="cpp/locale/codecvt_utf16">
        <inherits name="std::codecvt"/>
    </class>
    <class name="std::codecvt_utf8_utf16" link="cpp/locale/codecvt_utf8_utf16">
        <inherits name="std::codecvt"/>
    </class>

    <enum name="std::codecvt_mode" link="cpp/locale/codecvt_mode"/>
    <const name="std::consume_header" link="cpp/locale/codecvt_mode"/>
    <const name="std::generate_header" link="cpp/locale/codecvt_mode"/>
    <const name="std::little_endian" link="cpp/locale/codecvt_mode"/>

    <!-- c localization facilities -->

    <function name="std::setlocale" link="cpp/locale/setlocale"/>
    <function name="std::localeconv" link="cpp/locale/localeconv"/>
    <class name="std::lconv" link="cpp/locale/lconv"/>

    <const name="LC_ALL" link="cpp/locale/LC_categories"/>
    <const name="LC_COLLATE" link="cpp/locale/LC_categories"/>
    <const name="LC_CTYPE" link="cpp/locale/LC_categories"/>
    <const name="LC_MONETARY" link="cpp/locale/LC_categories"/>
    <const name="LC_NUMERIC" link="cpp/locale/LC_categories"/>
    <const name="LC_TIME" link="cpp/locale/LC_categories"/>

    <!-- cpp/regex -->

    <class name="std::basic_regex" link="cpp/regex/basic_regex" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="assign"/>

        <function name="mark_count"/>
        <function name="flags"/>

        <function name="getloc"/>
        <function name="imbue"/>

        <function name="swap"/>

        <overload name="std::swap" link="swap2"/>
    </class>

    <typedef name="std::regex" alias="std::basic_regex" since="c++11"/>
    <typedef name="std::wregex" alias="std::basic_regex" since="c++11"/>

    <class name="std::sub_match" link="cpp/regex/sub_match" since="c++11">
        <inherits name="std::pair"/>
        <variable name="matched" link="."/>
        <constructor/>
        <function name="length"/>
        <function name="str"/>
        <function name="operator string_type" link="str"/>
        <function name="compare"/>
    </class>

    <typedef name="std::csub_match" alias="std::sub_match" since="c++11"/>
    <typedef name="std::wcsub_match" alias="std::sub_match" since="c++11"/>
    <typedef name="std::ssub_match" alias="std::sub_match" since="c++11"/>
    <typedef name="std::wssub_match" alias="std::sub_match" since="c++11"/>

    <class name="std::match_results" link="cpp/regex/match_results" since="c++11">
        <constructor/>
        <destructor/>
        <function name="get_allocator"/>

        <function name="ready"/>

        <function name="empty"/>
        <function name="size"/>
        <function name="max_size"/>

        <function name="length"/>
        <function name="position"/>
        <function name="str"/>
        <function name="operator[]" link="operator_at"/>
        <function name="prefix"/>
        <function name="suffix"/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="end"/>
        <function name="cend" link="end"/>

        <function name="format"/>
        <function name="swap"/>
    </class>

    <typedef name="std::cmatch" alias="std::match_results" since="c++11"/>
    <typedef name="std::wcmatch" alias="std::match_results" since="c++11"/>
    <typedef name="std::smatch" alias="std::match_results" since="c++11"/>
    <typedef name="std::wsmatch" alias="std::match_results" since="c++11"/>

    <function name="std::regex_match" link="cpp/regex/regex_match" since="c++11"/>
    <function name="std::regex_search" link="cpp/regex/regex_search" since="c++11"/>
    <function name="std::regex_replace" link="cpp/regex/regex_replace" since="c++11"/>

    <class name="std::regex_iterator" link="cpp/regex/regex_iterator" since="c++11">
        <constructor/>
        <function name="operator="/>
        <function name="operator==" link="operator_cmp"/>
        <function name="operator!=" link="operator_cmp"/>
        <function name="operator*"/>
        <function name="operator->" link="operator*"/>
        <function name="operator++" link="operator_arith"/>
        <function name="operator++(int)" link="operator_arith"/>
    </class>

    <typedef name="std::cregex_iterator" alias="std::regex_iterator" since="c++11"/>
    <typedef name="std::wcregex_iterator" alias="std::regex_iterator" since="c++11"/>
    <typedef name="std::sregex_iterator" alias="std::regex_iterator" since="c++11"/>
    <typedef name="std::wsregex_iterator" alias="std::regex_iterator" since="c++11"/>

    <class name="std::regex_token_iterator" link="cpp/regex/regex_token_iterator" since="c++11">
        <constructor/>
        <function name="operator="/>
        <function name="operator==" link="operator_cmp"/>
        <function name="operator!=" link="operator_cmp"/>
        <function name="operator*"/>
        <function name="operator->" link="operator*"/>
        <function name="operator++" link="operator_arith"/>
        <function name="operator++(int)" link="operator_arith"/>
    </class>

    <typedef name="std::cregex_token_iterator" alias="std::regex_token_iterator" since="c++11"/>
    <typedef name="std::wcregex_token_iterator" alias="std::regex_token_iterator" since="c++11"/>
    <typedef name="std::sregex_token_iterator" alias="std::regex_token_iterator" since="c++11"/>
    <typedef name="std::wsregex_token_iterator" alias="std::regex_token_iterator" since="c++11"/>

    <class name="std::regex_error" link="cpp/regex/regex_error" since="c++11">
        <inherits name="std::runtime_error"/>
        <constructor/>
        <function name="code"/>
    </class>

    <class name="std::regex_traits" link="cpp/regex/regex_traits" since="c++11">
        <constructor/>
        <function name="length"/>
        <function name="translate"/>
        <function name="translate_nocase"/>
        <function name="transform"/>
        <function name="transform_primary"/>
        <function name="lookup_collatename"/>
        <function name="lookup_classname"/>
        <function name="isctype"/>
        <function name="value"/>
        <function name="imbue"/>
        <function name="getloc"/>
    </class>

    <enum name="std::regex_constants::syntax_option_type" link="cpp/regex/syntax_option_type" since="c++11"/>
    <const name="std::regex_constants::icase" link="cpp/regex/syntax_option_type" since="c++11"/>
    <const name="std::regex_constants::nosubs" link="cpp/regex/syntax_option_type" since="c++11"/>
    <const name="std::regex_constants::optimize" link="cpp/regex/syntax_option_type" since="c++11"/>
    <const name="std::regex_constants::collate" link="cpp/regex/syntax_option_type" since="c++11"/>
    <const name="std::regex_constants::ECMAScript" link="cpp/regex/syntax_option_type" since="c++11"/>
    <const name="std::regex_constants::basic" link="cpp/regex/syntax_option_type" since="c++11"/>
    <const name="std::regex_constants::extended" link="cpp/regex/syntax_option_type" since="c++11"/>
    <const name="std::regex_constants::awk" link="cpp/regex/syntax_option_type" since="c++11"/>
    <const name="std::regex_constants::grep" link="cpp/regex/syntax_option_type" since="c++11"/>
    <const name="std::regex_constants::egrep" link="cpp/regex/syntax_option_type" since="c++11"/>
    <const name="std::regex_constants::multiline" link="cpp/regex/syntax_option_type" since="c++17"/>

    <enum name="std::regex_constants::match_flag_type" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::match_default" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::match_not_bol" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::match_not_eol" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::match_not_bow" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::match_not_eow" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::match_any" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::match_not_null" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::match_continuous" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::match_prev_avail" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::format_default" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::format_sed" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::format_no_copy" link="cpp/regex/match_flag_type" since="c++11"/>
    <const name="std::regex_constants::format_first_only" link="cpp/regex/match_flag_type" since="c++11"/>

    <enum name="std::regex_constants::error_type" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_collate" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_ctype" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_escape" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_backref" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_brack" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_paren" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_brace" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_badbrace" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_range" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_space" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_badrepeat" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_complexity" link="cpp/regex/error_type" since="c++11"/>
    <const name="std::regex_constants::error_stack" link="cpp/regex/error_type" since="c++11"/>

    <!-- cpp/atomic -->

    <class name="std::atomic" link="cpp/atomic/atomic" since="c++11">
        <constructor/>
        <function name="operator="/>
        <function name="is_lock_free"/>
        <function name="store"/>
        <function name="load"/>
        <function name="operator T" link="operator_T"/>
        <function name="exchange"/>
        <function name="compare_exchange_strong" link="compare_exchange"/>
        <function name="compare_exchange_weak" link="compare_exchange"/>
        <function name="wait" since="c++20"/>
        <function name="notify_one" since="c++20"/>
        <function name="notify_all" since="c++20"/>

        <const name="is_always_lock_free" since="c++17"/>

        <function name="fetch_add"/>
        <function name="fetch_sub"/>
        <function name="fetch_and"/>
        <function name="fetch_or"/>
        <function name="fetch_xor"/>

        <function name="operator++" link="operator_arith"/>
        <function name="operator++(int)" link="operator_arith"/>
        <function name="operator--" link="operator_arith"/>
        <function name="operator--(int)" link="operator_arith"/>

        <function name="operator+=" link="operator_arith2"/>
        <function name="operator-=" link="operator_arith2"/>
        <function name="operator&amp;=" link="operator_arith2"/>
        <function name="operator|=" link="operator_arith2"/>
        <function name="operator^=" link="operator_arith2"/>
    </class>

    <class name="std::atomic_ref" link="cpp/atomic/atomic_ref" since="c++20">
        <constructor/>
        <function name="operator="/>
        <function name="is_lock_free"/>
        <function name="store"/>
        <function name="load"/>
        <function name="operator T" link="operator_T"/>
        <function name="exchange"/>
        <function name="compare_exchange_strong" link="compare_exchange"/>
        <function name="compare_exchange_weak" link="compare_exchange"/>
        <function name="wait"/>
        <function name="notify_one"/>
        <function name="notify_all"/>

        <const name="is_always_lock_free"/>
        <const name="required_alignment"/>

        <function name="fetch_add"/>
        <function name="fetch_sub"/>
        <function name="fetch_and"/>
        <function name="fetch_or"/>
        <function name="fetch_xor"/>

        <function name="operator++" link="operator_arith"/>
        <function name="operator++(int)" link="operator_arith"/>
        <function name="operator--" link="operator_arith"/>
        <function name="operator--(int)" link="operator_arith"/>

        <function name="operator+=" link="operator_arith2"/>
        <function name="operator-=" link="operator_arith2"/>
        <function name="operator&amp;=" link="operator_arith2"/>
        <function name="operator|=" link="operator_arith2"/>
        <function name="operator^=" link="operator_arith2"/>
    </class>

    <function name="std::atomic_is_lock_free" link="cpp/atomic/atomic_is_lock_free" since="c++11"/>

    <function name="std::atomic_store" link="cpp/atomic/atomic_store" since="c++11"/>
    <function name="std::atomic_store_explicit" link="cpp/atomic/atomic_store" since="c++11"/>

    <function name="std::atomic_load" link="cpp/atomic/atomic_load" since="c++11"/>
    <function name="std::atomic_load_explicit" link="cpp/atomic/atomic_load" since="c++11"/>

    <function name="std::atomic_exchange" link="cpp/atomic/atomic_exchange" since="c++11"/>
    <function name="std::atomic_exchange_explicit" link="cpp/atomic/atomic_exchange" since="c++11"/>

    <function name="std::atomic_compare_exchange_weak" link="cpp/atomic/atomic_compare_exchange" since="c++11"/>
    <function name="std::atomic_compare_exchange_weak_explicit" link="cpp/atomic/atomic_compare_exchange" since="c++11"/>
    <function name="std::atomic_compare_exchange_strong" link="cpp/atomic/atomic_compare_exchange" since="c++11"/>
    <function name="std::atomic_compare_exchange_strong_explicit" link="cpp/atomic/atomic_compare_exchange" since="c++11"/>

    <function name="std::atomic_fetch_add" link="cpp/atomic/atomic_fetch_add" since="c++11"/>
    <function name="std::atomic_fetch_add_explicit" link="cpp/atomic/atomic_fetch_add" since="c++11"/>

    <function name="std::atomic_fetch_sub" link="cpp/atomic/atomic_fetch_sub" since="c++11"/>
    <function name="std::atomic_fetch_sub_explicit" link="cpp/atomic/atomic_fetch_sub" since="c++11"/>

    <function name="std::atomic_fetch_and" link="cpp/atomic/atomic_fetch_and" since="c++11"/>
    <function name="std::atomic_fetch_and_explicit" link="cpp/atomic/atomic_fetch_and" since="c++11"/>

    <function name="std::atomic_fetch_or" link="cpp/atomic/atomic_fetch_or" since="c++11"/>
    <function name="std::atomic_fetch_or_explicit" link="cpp/atomic/atomic_fetch_or" since="c++11"/>

    <function name="std::atomic_fetch_xor" link="cpp/atomic/atomic_fetch_xor" since="c++11"/>
    <function name="std::atomic_fetch_xor_explicit" link="cpp/atomic/atomic_fetch_xor" since="c++11"/>

    <function name="std::atomic_wait" link="cpp/atomic/atomic_wait" since="c++20"/>
    <function name="std::atomic_wait_explicit" link="cpp/atomic/atomic_wait" since="c++20"/>
    <function name="std::atomic_notify_one" link="cpp/atomic/atomic_notify_one" since="c++20"/>
    <function name="std::atomic_notify_all" link="cpp/atomic/atomic_notify_all" since="c++20"/>

    <class name="std::atomic_flag" link="cpp/atomic/atomic_flag" since="c++11">
        <constructor/>
        <function name="operator="/>
        <function name="clear"/>
        <function name="test_and_set"/>
        <function name="test" since="c++20"/>
        <function name="wait" since="c++20"/>
        <function name="notify_one" since="c++20"/>
        <function name="notify_all" since="c++20"/>
    </class>

    <function name="std::atomic_flag_test_and_set" link="cpp/atomic/atomic_flag_test_and_set" since="c++11"/>
    <function name="std::atomic_flag_test_and_set_explicit" link="cpp/atomic/atomic_flag_test_and_set" since="c++11"/>
    <function name="std::atomic_flag_clear" link="cpp/atomic/atomic_flag_clear" since="c++11"/>
    <function name="std::atomic_flag_clear_explicit" link="cpp/atomic/atomic_flag_clear" since="c++11"/>
    <function name="std::atomic_flag_test" link="cpp/atomic/atomic_flag_test" since="c++20"/>
    <function name="std::atomic_flag_test_explicit" link="cpp/atomic/atomic_flag_test" since="c++20"/>
    <function name="std::atomic_flag_wait" link="cpp/atomic/atomic_flag_wait" since="c++20"/>
    <function name="std::atomic_flag_wait_explicit" link="cpp/atomic/atomic_flag_wait" since="c++20"/>
    <function name="std::atomic_flag_notify_one" link="cpp/atomic/atomic_flag_notify_one" since="c++20"/>
    <function name="std::atomic_flag_notify_all" link="cpp/atomic/atomic_flag_notify_all" since="c++20"/>

    <function name="std::atomic_init" link="cpp/atomic/atomic_init" since="c++11"/>
    <function name="ATOMIC_VAR_INIT" link="cpp/atomic/ATOMIC_VAR_INIT" since="c++11"/>
    <enum name="ATOMIC_FLAG_INIT" link="cpp/atomic/ATOMIC_FLAG_INIT" since="c++11"/>

    <enum name="std::memory_order" link="cpp/atomic/memory_order" since="c++11">
        <const name="relaxed" link="." since="c++20"/>
        <const name="consume" link="." since="c++20"/>
        <const name="acquire" link="." since="c++20"/>
        <const name="release" link="." since="c++20"/>
        <const name="acq_rel" link="." since="c++20"/>
        <const name="seq_cst" link="." since="c++20"/>
    </enum>
    <const name="std::memory_order_relaxed" link="cpp/atomic/memory_order" since="c++11"/>
    <const name="std::memory_order_consume" link="cpp/atomic/memory_order" since="c++11"/>
    <const name="std::memory_order_acquire" link="cpp/atomic/memory_order" since="c++11"/>
    <const name="std::memory_order_release" link="cpp/atomic/memory_order" since="c++11"/>
    <const name="std::memory_order_acq_rel" link="cpp/atomic/memory_order" since="c++11"/>
    <const name="std::memory_order_seq_cst" link="cpp/atomic/memory_order" since="c++11"/>

    <function name="std::kill_dependency" link="cpp/atomic/kill_dependency" since="c++11"/>
    <function name="std::atomic_thread_fence" link="cpp/atomic/atomic_thread_fence" since="c++11"/>
    <function name="std::atomic_signal_fence" link="cpp/atomic/atomic_signal_fence" since="c++11"/>

    <typedef name="std::atomic_bool" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_char" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_schar" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uchar" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_short" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_ushort" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_long" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_ulong" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_llong" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_ullong" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_char8_t" alias="std::atomic" since="c++20"/>
    <typedef name="std::atomic_char16_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_char32_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_wchar_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int8_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint8_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int16_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint16_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int32_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint32_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int64_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint64_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int_least8_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint_least8_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int_least16_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint_least16_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int_least32_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint_least32_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int_least64_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint_least64_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int_fast8_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint_fast8_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int_fast16_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint_fast16_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int_fast32_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint_fast32_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_int_fast64_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uint_fast64_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_intptr_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uintptr_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_size_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_ptrdiff_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_intmax_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_uintmax_t" alias="std::atomic" since="c++11"/>
    <typedef name="std::atomic_signed_lock_free" alias="std::atomic" since="c++20"/>
    <typedef name="std::atomic_unsigned_lock_free" alias="std::atomic" since="c++20"/>

    <!-- cpp/thread -->

    <class name="std::thread" link="cpp/thread/thread" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="joinable"/>
        <function name="get_id"/>
        <function name="native_handle"/>
        <function name="hardware_concurrency"/>

        <function name="join"/>
        <function name="detach"/>
        <function name="swap"/>

        <overload name="std::swap" link="swap2"/>
    </class>

    <class name="std::thread::id" link="cpp/thread/thread/id">
        <constructor/>
        <function name="operator==" link="operator_cmp"/>
        <function name="operator!=" link="operator_cmp"/>
        <function name="operator&lt;" link="operator_cmp"/>
        <function name="operator&lt;=" link="operator_cmp"/>
        <function name="operator&gt;" link="operator_cmp"/>
        <function name="operator&gt;=" link="operator_cmp"/>
        <function name="operator&lt;&lt;" link="operator_ltlt"/>

        <specialization name="std::hash" link="hash"/>
    </class>

    <class name="std::jthread" link="cpp/thread/jthread" since="c++20">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="joinable"/>
        <function name="get_id"/>
        <function name="native_handle"/>
        <function name="hardware_concurrency"/>

        <function name="join"/>
        <function name="detach"/>
        <function name="swap"/>

        <function name="get_stop_source"/>
        <function name="get_stop_token"/>
        <function name="request_stop"/>

        <overload name="swap" link="swap2"/>
    </class>

    <function name="std::this_thread::get_id" link="cpp/thread/get_id" since="c++11"/>
    <function name="std::this_thread::sleep_for" link="cpp/thread/sleep_for" since="c++11"/>
    <function name="std::this_thread::sleep_until" link="cpp/thread/sleep_until" since="c++11"/>
    <function name="std::this_thread::yield" link="cpp/thread/yield" since="c++11"/>

    <class name="std::stop_token" link="cpp/thread/stop_token" since="c++20">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="swap"/>

        <function name="stop_requested"/>
        <function name="stop_possible"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="swap" link="swap2"/>
    </class>

    <class name="std::stop_source" link="cpp/thread/stop_source" since="c++20">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="request_stop"/>
        <function name="swap"/>

        <function name="get_token"/>
        <function name="stop_requested"/>
        <function name="stop_possible"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="swap" link="swap2"/>
    </class>

    <class name="std::nostopstate_t" link="cpp/thread/stop_source/nostopstate_t" since="c++20"/>
    <const name="std::nostopstate" link="cpp/thread/stop_source/nostopstate" since="c++20"/>

    <class name="std::stop_callback" link="cpp/thread/stop_callback" since="c++20">
        <typedef name="callback_type" link="."/>

        <constructor/>
        <destructor/>
    </class>

    <const name="std::hardware_constructive_interference_size"
           link="cpp/thread/hardware_destructive_interference_size" since="c++17"/>
    <const name="std::hardware_destructive_interference_size"
           link="cpp/thread/hardware_destructive_interference_size" since="c++17"/>

    <class name="std::mutex" link="cpp/thread/mutex" since="c++11">
        <constructor/>
        <destructor/>
        <function name="lock"/>
        <function name="try_lock"/>
        <function name="unlock"/>
        <function name="native_handle"/>
    </class>

    <class name="std::recursive_mutex" link="cpp/thread/recursive_mutex" since="c++11">
        <constructor/>
        <destructor/>
        <function name="lock"/>
        <function name="try_lock"/>
        <function name="unlock"/>
        <function name="native_handle"/>
    </class>

    <class name="std::timed_mutex" link="cpp/thread/timed_mutex" since="c++11">
        <constructor/>
        <destructor/>
        <function name="lock"/>
        <function name="try_lock"/>
        <function name="try_lock_for"/>
        <function name="try_lock_until"/>
        <function name="unlock"/>
        <function name="native_handle"/>
    </class>

    <class name="std::recursive_timed_mutex" link="cpp/thread/recursive_timed_mutex" since="c++11">
        <constructor/>
        <destructor/>
        <function name="lock"/>
        <function name="try_lock"/>
        <function name="try_lock_for"/>
        <function name="try_lock_until"/>
        <function name="unlock"/>
        <function name="native_handle"/>
    </class>

    <class name="std::shared_timed_mutex" link="cpp/thread/shared_timed_mutex" since="c++14">
        <constructor/>
        <destructor/>
        <function name="lock"/>
        <function name="try_lock"/>
        <function name="try_lock_for"/>
        <function name="try_lock_until"/>
        <function name="unlock"/>

        <function name="lock_shared"/>
        <function name="try_lock_shared"/>
        <function name="try_lock_shared_for"/>
        <function name="try_lock_shared_until"/>
        <function name="unlock_shared"/>
    </class>

    <class name="std::shared_mutex" link="cpp/thread/shared_mutex" since="c++17">
        <constructor/>
        <destructor/>
        <function name="lock"/>
        <function name="try_lock"/>
        <function name="unlock"/>

        <function name="lock_shared"/>
        <function name="try_lock_shared"/>
        <function name="unlock_shared"/>
        <function name="native_handle"/>
    </class>

    <class name="std::defer_lock_t" link="cpp/thread/lock_tag_t" since="c++11"/>
    <class name="std::try_to_lock_t" link="cpp/thread/lock_tag_t" since="c++11"/>
    <class name="std::adopt_lock_t" link="cpp/thread/lock_tag_t" since="c++11"/>

    <const name="std::defer_lock" link="cpp/thread/lock_tag" since="c++11"/>
    <const name="std::try_to_lock" link="cpp/thread/lock_tag" since="c++11"/>
    <const name="std::adopt_lock" link="cpp/thread/lock_tag" since="c++11"/>

    <class name="std::lock_guard" link="cpp/thread/lock_guard" since="c++11">
        <constructor/>
        <destructor/>
    </class>

    <class name="std::scoped_lock" link="cpp/thread/scoped_lock" since="c++17">
        <constructor/>
        <destructor/>
    </class>

    <class name="std::unique_lock" link="cpp/thread/unique_lock" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="lock"/>
        <function name="try_lock"/>
        <function name="try_lock_for"/>
        <function name="try_lock_until"/>
        <function name="unlock"/>

        <function name="swap"/>
        <function name="release"/>

        <function name="mutex"/>
        <function name="owns_lock"/>
        <function name="operator bool" link="operator_bool"/>

        <overload name="std::swap" link="swap2"/>
    </class>

    <class name="std::shared_lock" link="cpp/thread/shared_lock" since="c++14">
        <constructor/>
        <destructor/>
        <function name="operator="/>

        <function name="lock"/>
        <function name="try_lock"/>
        <function name="try_lock_for"/>
        <function name="try_lock_until"/>
        <function name="unlock"/>

        <function name="swap"/>
        <function name="release"/>

        <function name="mutex"/>
        <function name="owns_lock"/>
        <function name="operator bool" link="operator_bool"/>

        <overload name="std::swap" link="swap2"/>
    </class>

    <function name="std::lock" link="cpp/thread/lock" since="c++11"/>
    <function name="std::try_lock" link="cpp/thread/try_lock" since="c++11"/>

    <class name="std::condition_variable" link="cpp/thread/condition_variable" since="c++11">
        <constructor/>
        <destructor/>

        <function name="notify_one"/>
        <function name="notify_all"/>

        <function name="wait"/>
        <function name="wait_for"/>
        <function name="wait_until"/>

        <function name="native_handle"/>
    </class>

    <class name="std::condition_variable_any" link="cpp/thread/condition_variable_any" since="c++11">
        <constructor/>
        <destructor/>

        <function name="notify_one"/>
        <function name="notify_all"/>

        <function name="wait"/>
        <function name="wait_for"/>
        <function name="wait_until"/>
    </class>

    <function name="std::notify_all_at_thread_exit" link="cpp/thread/notify_all_at_thread_exit" since="c++11"/>

    <class name="std::once_flag" link="cpp/thread/once_flag" since="c++11">
        <constructor link="."/>
    </class>
    <function name="std::call_once" link="cpp/thread/call_once" since="c++11"/>

    <enum name="std::cv_status" link="cpp/thread/cv_status" since="c++11">
        <const name="no_timeout" link="."/>
        <const name="timeout" link="."/>
    </enum>

    <class name="std::counting_semaphore" link="cpp/thread/counting_semaphore" since="c++20">
        <constructor/>
        <destructor/>

        <function name="release"/>
        <function name="acquire"/>
        <function name="try_acquire"/>
        <function name="try_acquire_for"/>
        <function name="try_acquire_until"/>

        <const name="max"/>
    </class>
    <typedef name="std::binary_semaphore" alias="std::counting_semaphore" since="c++20"/>

    <class name="std::latch" link="cpp/thread/latch" since="c++20">
        <constructor/>
        <destructor/>

        <function name="count_down"/>
        <function name="try_wait"/>
        <function name="wait"/>
        <function name="arrive_and_wait"/>

        <const name="max"/>
    </class>

    <class name="std::barrier" link="cpp/thread/barrier" since="c++20">
        <typedef name="arrival_token" link="."/>

        <constructor/>
        <destructor/>

        <function name="arrive"/>
        <function name="wait"/>
        <function name="arrive_and_wait"/>
        <function name="arrive_and_drop"/>

        <const name="max"/>
    </class>

    <class name="std::promise" link="cpp/thread/promise" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="swap"/>
        <function name="get_future"/>
        <function name="set_value"/>
        <function name="set_value_at_thread_exit"/>
        <function name="set_exception"/>
        <function name="set_exception_at_thread_exit"/>

        <overload name="std::swap" link="swap2"/>
        <specialization name="std::uses_allocator" link="uses_allocator"/>
    </class>

    <class name="std::future" link="cpp/thread/future" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="share"/>
        <function name="get"/>

        <function name="valid"/>
        <function name="wait"/>
        <function name="wait_for"/>
        <function name="wait_until"/>
    </class>

    <class name="std::shared_future" link="cpp/thread/shared_future" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="get"/>

        <function name="valid"/>
        <function name="wait"/>
        <function name="wait_for"/>
        <function name="wait_until"/>
    </class>

    <class name="std::packaged_task" link="cpp/thread/packaged_task" since="c++11">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="valid"/>
        <function name="swap"/>

        <function name="get_future"/>
        <function name="operator()"/>
        <function name="make_ready_at_thread_exit"/>
        <function name="reset"/>

        <overload name="std::swap" link="swap2"/>
        <specialization name="std::uses_allocator" link="uses_allocator"/>
    </class>

    <function name="std::async" link="cpp/thread/async" since="c++11"/>

    <enum name="std::launch" link="cpp/thread/launch" since="c++11">
        <const name="async" link="."/>
        <const name="deferred" link="."/>
    </enum>

    <enum name="std::future_status" link="cpp/thread/future_status" since="c++11">
        <const name="ready" link="."/>
        <const name="timeout" link="."/>
        <const name="deferred" link="."/>
    </enum>

    <class name="std::future_error" link="cpp/thread/future_error" since="c++11">
        <inherits name="std::logic_error"/>
        <constructor/>
        <function name="operator="/>
        <function name="code"/>
        <function name="what"/>
    </class>

    <function name="std::future_category" link="cpp/thread/future_category" since="c++11"/>

    <class name="std::future_errc" link="cpp/thread/future_errc" since="c++11">
        <const name="broken_promise" link="."/>
        <const name="future_already_retrieved" link="."/>
        <const name="promise_already_satisfied" link="."/>
        <const name="no_state" link="."/>

        <specialization name="std::is_error_code_enum" link="is_error_code_enum"/>
        <overload name="std::make_error_code" link="make_error_code"/>
        <overload name="std::make_error_condition" link="make_error_condition"/>
    </class>

    <!-- filesystem -->
    <class name="std::filesystem::path" link="cpp/filesystem/path" since="c++17">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="assign"/>

        <enum name="format"/>

        <const name="preferred_separator" link="."/>

        <function name="append"/>
        <function name="operator/=" link="append"/>
        <function name="concat"/>
        <function name="operator+=" link="concat"/>

        <function name="clear"/>
        <function name="make_preferred"/>
        <function name="remove_filename"/>
        <function name="replace_filename"/>
        <function name="replace_extension"/>
        <function name="swap"/>

        <function name="c_str" link="native"/>
        <function name="native"/>
        <function name="operator string_type" link="native"/>

        <function name="string"/>
        <function name="wstring" link="string"/>
        <function name="u8string" link="string"/>
        <function name="u16string" link="string"/>
        <function name="u32string" link="string"/>

        <function name="generic_string"/>
        <function name="generic_wstring" link="generic_string"/>
        <function name="generic_u8string" link="generic_string"/>
        <function name="generic_u16string" link="generic_string"/>
        <function name="generic_u32string" link="generic_string"/>

        <function name="compare"/>

        <function name="lexically_normal"/>
        <function name="lexically_proximate" link="lexically_normal"/>
        <function name="lexically_relative" link="lexically_normal"/>

        <function name="root_name"/>
        <function name="root_directory"/>
        <function name="root_path"/>
        <function name="relative_path"/>
        <function name="parent_path"/>
        <function name="filename"/>
        <function name="stem"/>
        <function name="extension"/>

        <function name="empty"/>
        <function name="has_root_path" link="has_path"/>
        <function name="has_root_name" link="has_path"/>
        <function name="has_root_directory" link="has_path"/>
        <function name="has_relative_path" link="has_path"/>
        <function name="has_parent_path" link="has_path"/>
        <function name="has_filename" link="has_path"/>
        <function name="has_stem" link="has_path"/>
        <function name="has_extension" link="has_path"/>

        <function name="is_absolute" link="is_absrel"/>
        <function name="is_relative" link="is_absrel"/>

        <function name="begin"/>
        <function name="end" link="begin"/>

        <overload name="std::swap" link="swap2"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
        <overload name="operator/" link="operator_slash"/>
        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>

        <overload name="std::filesystem::hash_value" link="hash_value"/>
    </class>
    <function name="std::filesystem::u8path" link="cpp/filesystem/path/u8path" since="c++17"/>

    <class name="std::filesystem::filesystem_error" link="cpp/filesystem/filesystem_error" since="c++17">
        <constructor/>
        <function name="operator="/>
        <function name="path1" link="path"/>
        <function name="path2" link="path"/>
        <function name="what"/>
    </class>

    <class name="std::filesystem::directory_entry" link="cpp/filesystem/directory_entry" since="c++17">
        <constructor/>
        <function name="operator="/>
        <function name="assign"/>
        <function name="replace_filename"/>
        <function name="refresh"/>

        <function name="path"/>
        <function name="operator const path&amp;" link="path"/>
        <function name="exists"/>
        <function name="is_block_file"/>
        <function name="is_character_file"/>
        <function name="is_directory"/>
        <function name="is_fifo"/>
        <function name="is_other"/>
        <function name="is_regular_file"/>
        <function name="is_socket"/>
        <function name="is_symlink"/>
        <function name="file_size"/>
        <function name="hard_link_count"/>
        <function name="last_write_time"/>
        <function name="status"/>
        <function name="symlink_status" link="status"/>

        <function name="operator==" link="operator_cmp"/>
        <function name="operator!=" link="operator_cmp"/>
        <function name="operator&lt;" link="operator_cmp"/>
        <function name="operator&lt;=" link="operator_cmp"/>
        <function name="operator&gt;" link="operator_cmp"/>
        <function name="operator&gt;=" link="operator_cmp"/>
        <function name="operator&lt;=&gt;" link="operator_cmp" since="c++20"/>

        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
    </class>

    <class name="std::filesystem::directory_iterator" link="cpp/filesystem/directory_iterator" since="c++17">
        <constructor/>
        <function name="operator="/>
        <function name="operator*"/>
        <function name="operator->" link="operator*"/>
        <function name="increment"/>
        <function name="operator++" link="increment"/>

        <overload name="operator==" link="."/>
        <overload name="operator!=" link="."/>
        <overload name="std::filesystem::begin" link="begin"/>
        <overload name="std::filesystem::end" link="begin"/>
    </class>

    <class name="std::filesystem::recursive_directory_iterator" link="cpp/filesystem/recursive_directory_iterator" since="c++17">
        <constructor/>
        <function name="operator*"/>
        <function name="operator->" link="operator*"/>
        <function name="options"/>
        <function name="depth"/>
        <function name="recursion_pending"/>

        <function name="operator="/>
        <function name="increment"/>
        <function name="operator++" link="increment"/>
        <function name="pop"/>
        <function name="disable_recursion_pending"/>

        <overload name="operator==" link="."/>
        <overload name="operator!=" link="."/>
        <overload name="std::filesystem::begin" link="begin"/>
        <overload name="std::filesystem::end" link="begin"/>
    </class>

    <class name="std::filesystem::file_status" link="cpp/filesystem/file_status" since="c++17">
        <constructor/>
        <destructor link="."/>
        <function name="operator="/>
        <function name="type"/>
        <function name="permissions"/>

        <overload name="operator==" since="c++20"/>
    </class>

    <class name="std::filesystem::space_info" link="cpp/filesystem/space_info" since="c++17">
        <variable name="capacity" link="."/>
        <variable name="free" link="."/>
        <variable name="available" link="."/>
    </class>

    <class name="std::filesystem::file_type" link="cpp/filesystem/file_type" since="c++17">
        <const name="none" link="."/>
        <const name="not_found" link="."/>
        <const name="regular" link="."/>
        <const name="directory" link="."/>
        <const name="symlink" link="."/>
        <const name="block" link="."/>
        <const name="character" link="."/>
        <const name="fifo" link="."/>
        <const name="socket" link="."/>
        <const name="unknown" link="."/>
    </class>

    <class name="std::filesystem::perms" link="cpp/filesystem/perms" since="c++17">
        <const name="none" link="."/>
        <const name="owner_read" link="."/>
        <const name="owner_write" link="."/>
        <const name="owner_exec" link="."/>
        <const name="owner_all" link="."/>
        <const name="group_read" link="."/>
        <const name="group_write" link="."/>
        <const name="group_exec" link="."/>
        <const name="group_all" link="."/>
        <const name="others_read" link="."/>
        <const name="others_write" link="."/>
        <const name="others_exec" link="."/>
        <const name="others_all" link="."/>
        <const name="all" link="."/>
        <const name="set_uid" link="."/>
        <const name="set_gid" link="."/>
        <const name="sticky_bit" link="."/>
        <const name="mask" link="."/>

        <const name="unknown" link="."/>
        <const name="add_perms" link="."/>
        <const name="remove_perms" link="."/>
        <const name="resolve_symlinks" link="."/>
    </class>

    <class name="std::filesystem::perm_options" link="cpp/filesystem/perm_options" since="c++17">
        <const name="replace" link="."/>
        <const name="add" link="."/>
        <const name="remove" link="."/>
        <const name="nofollow" link="."/>

        <overload name="operator|=" link="."/>
        <overload name="operator&amp;=" link="."/>
        <overload name="operator^=" link="."/>
        <overload name="operator|" link="."/>
        <overload name="operator&amp;" link="."/>
        <overload name="operator^" link="."/>
        <overload name="operator~" link="."/>
    </class>

    <class name="std::filesystem::copy_options" link="cpp/filesystem/copy_options" since="c++17">
        <const name="none" link="."/>
        <const name="skip_existing" link="."/>
        <const name="overwrite_existing" link="."/>
        <const name="update_existing" link="."/>
        <const name="recursive" link="."/>
        <const name="copy_symlinks" link="."/>
        <const name="skip_symlinks" link="."/>
        <const name="directories_only" link="."/>
        <const name="create_symlinks" link="."/>
        <const name="create_hard_links" link="."/>

        <overload name="operator|=" link="."/>
        <overload name="operator&amp;=" link="."/>
        <overload name="operator^=" link="."/>
        <overload name="operator|" link="."/>
        <overload name="operator&amp;" link="."/>
        <overload name="operator^" link="."/>
        <overload name="operator~" link="."/>
    </class>

    <class name="std::filesystem::directory_options" link="cpp/filesystem/directory_options" since="c++17">
        <const name="none" link="."/>
        <const name="follow_directory_symlink" link="."/>
        <const name="overwrite_existing" link="."/>
        <const name="skip_permission_denied" link="."/>

        <overload name="operator|=" link="."/>
        <overload name="operator&amp;=" link="."/>
        <overload name="operator^=" link="."/>
        <overload name="operator|" link="."/>
        <overload name="operator&amp;" link="."/>
        <overload name="operator^" link="."/>
        <overload name="operator~" link="."/>
    </class>

    <typedef name="std::filesystem::file_time_type" link="cpp/filesystem/file_time_type" since="c++17"/>

    <function name="std::filesystem::absolute" link="cpp/filesystem/absolute" since="c++17"/>
    <function name="std::filesystem::canonical" link="cpp/filesystem/canonical" since="c++17"/>
    <function name="std::filesystem::weakly_canonical" link="cpp/filesystem/canonical" since="c++17"/>
    <function name="std::filesystem::relative" link="cpp/filesystem/relative" since="c++17"/>
    <function name="std::filesystem::proximate" link="cpp/filesystem/relative" since="c++17"/>
    <function name="std::filesystem::copy" link="cpp/filesystem/copy" since="c++17"/>
    <function name="std::filesystem::copy_file" link="cpp/filesystem/copy_file" since="c++17"/>
    <function name="std::filesystem::copy_symlink" link="cpp/filesystem/copy_symlink" since="c++17"/>
    <function name="std::filesystem::create_directory" link="cpp/filesystem/create_directory" since="c++17"/>
    <function name="std::filesystem::create_directories" link="cpp/filesystem/create_directory" since="c++17"/>
    <function name="std::filesystem::create_hard_link" link="cpp/filesystem/create_hard_link" since="c++17"/>
    <function name="std::filesystem::create_symlink" link="cpp/filesystem/create_symlink" since="c++17"/>
    <function name="std::filesystem::create_directory_symlink" link="cpp/filesystem/create_symlink" since="c++17"/>
    <function name="std::filesystem::current_path" link="cpp/filesystem/current_path" since="c++17"/>
    <function name="std::filesystem::exists" link="cpp/filesystem/exists" since="c++17"/>
    <function name="std::filesystem::equivalent" link="cpp/filesystem/equivalent" since="c++17"/>
    <function name="std::filesystem::file_size" link="cpp/filesystem/file_size" since="c++17"/>
    <function name="std::filesystem::hard_link_count" link="cpp/filesystem/hard_link_count" since="c++17"/>
    <function name="std::filesystem::last_write_time" link="cpp/filesystem/last_write_time" since="c++17"/>
    <function name="std::filesystem::permissions" link="cpp/filesystem/permissions" since="c++17"/>
    <function name="std::filesystem::read_symlink" link="cpp/filesystem/read_symlink" since="c++17"/>
    <function name="std::filesystem::remove" link="cpp/filesystem/remove" since="c++17"/>
    <function name="std::filesystem::remove_all" link="cpp/filesystem/remove" since="c++17"/>
    <function name="std::filesystem::rename" link="cpp/filesystem/rename" since="c++17"/>
    <function name="std::filesystem::resize_file" link="cpp/filesystem/resize_file" since="c++17"/>
    <function name="std::filesystem::space" link="cpp/filesystem/space" since="c++17"/>
    <function name="std::filesystem::status" link="cpp/filesystem/status" since="c++17"/>
    <function name="std::filesystem::symlink_status" link="cpp/filesystem/status" since="c++17"/>
    <function name="std::filesystem::temp_directory_path" link="cpp/filesystem/temp_directory_path" since="c++17"/>
    <function name="std::filesystem::is_block_file" link="cpp/filesystem/is_block_file" since="c++17"/>
    <function name="std::filesystem::is_character_file" link="cpp/filesystem/is_character_file" since="c++17"/>
    <function name="std::filesystem::is_directory" link="cpp/filesystem/is_directory" since="c++17"/>
    <function name="std::filesystem::is_empty" link="cpp/filesystem/is_empty" since="c++17"/>
    <function name="std::filesystem::is_fifo" link="cpp/filesystem/is_fifo" since="c++17"/>
    <function name="std::filesystem::is_other" link="cpp/filesystem/is_other" since="c++17"/>
    <function name="std::filesystem::is_regular_file" link="cpp/filesystem/is_regular_file" since="c++17"/>
    <function name="std::filesystem::is_socket" link="cpp/filesystem/is_socket" since="c++17"/>
    <function name="std::filesystem::is_symlink" link="cpp/filesystem/is_symlink" since="c++17"/>
    <function name="std::filesystem::status_known" link="cpp/filesystem/status_known" since="c++17"/>

    <!--=======================================================================-->
    <!-- experimental -->

    <!-- library extensions -->
    <class name="std::experimental::optional" link="cpp/experimental/optional" since="c++14">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="operator*"/>
        <function name="operator->" link="operator*"/>
        <function name="operator bool" link="operator_bool"/>
        <function name="value"/>
        <function name="value_or"/>
        <function name="swap"/>
        <function name="emplace"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="std::swap" link="swap2"/>
        <specialization name="std::hash" link="hash"/>
    </class>

    <function name="std::experimental::make_optional" link="cpp/experimental/optional/make_optional" since="exp"/>

    <class name="std::experimental::nullopt_t" link="cpp/experimental/optional/nullopt_t" since="exp"/>
    <const name="std::experimental::nullopt" link="cpp/experimental/optional/nullopt" since="exp"/>
    <class name="std::experimental::in_place_t" link="cpp/experimental/optional/in_place_t" since="exp"/>
    <const name="std::experimental::in_place" link="cpp/experimental/optional/in_place" since="exp"/>

    <class name="std::experimental::bad_optional_access" link="cpp/experimental/optional/bad_optional_access" since="exp">
        <inherits name="std::logic_error"/>
        <constructor link="."/>
    </class>

    <class name="std::experimental::any" link="cpp/experimental/any" since="exp">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="clear"/>
        <function name="empty"/>
        <function name="swap"/>
        <function name="type"/>
        <overload name="std::swap" link="swap2"/>
    </class>
    <function name="std::experimental::any_cast" link="cpp/experimental/any/any_cast" since="exp"/>
    <class name="std::experimental::bad_any_cast" link="cpp/experimental/any/bad_any_cast" since="exp"/>

    <class name="std::experimental::pmr::memory_resource" link="cpp/experimental/memory_resource" since="exp">
        <constructor/>
        <function name="allocate"/>
        <function name="deallocate"/>
        <function name="is_equal"/>
        <function name="do_allocate"/>
        <function name="do_deallocate"/>
        <function name="do_is_equal"/>

        <overload name="operator==" link="operator_eq"/>
        <overload name="operator!=" link="operator_eq"/>
    </class>

    <class name="std::experimental::pmr::monotonic_buffer_resource" link="cpp/experimental/monotonic_buffer_resource" since="exp">
        <constructor/>
        <destructor/>

        <function name="release"/>
        <function name="upstream_resource"/>

        <function name="do_allocate"/>
        <function name="do_deallocate"/>
        <function name="do_is_equal"/>
    </class>

    <class name="std::experimental::pmr::pool_options" link="cpp/experimental/pool_options" since="exp"/>

    <class name="std::experimental::pmr::synchronized_pool_resource" link="cpp/experimental/synchronized_pool_resource" since="exp">
        <constructor/>
        <destructor/>

        <function name="release"/>
        <function name="upstream_resource"/>
        <function name="options"/>

        <function name="do_allocate"/>
        <function name="do_deallocate"/>
        <function name="do_is_equal"/>
    </class>

    <class name="std::experimental::pmr::unsynchronized_pool_resource" link="cpp/experimental/unsynchronized_pool_resource" since="exp">
        <constructor/>
        <destructor/>

        <function name="release"/>
        <function name="upstream_resource"/>
        <function name="options"/>

        <function name="do_allocate"/>
        <function name="do_deallocate"/>
        <function name="do_is_equal"/>
    </class>

    <class name="std::experimental::pmr::polymorphic_allocator" link="cpp/experimental/polymorphic_allocator" since="exp">
        <constructor/>
        <function name="operator="/>

        <function name="allocate"/>
        <function name="deallocate"/>
        <function name="construct"/>
        <function name="destroy"/>
        <function name="select_on_container_copy_construction"/>
        <function name="resource"/>

        <overload name="operator==" link="operator_eq"/>
        <overload name="operator!=" link="operator_eq"/>
    </class>

    <class name="std::experimental::pmr::resource_adaptor" link="cpp/experimental/resource_adaptor" since="exp"/>

    <function name="std::experimental::pmr::new_delete_resource" link="cpp/experimental/new_delete_resource" since="exp"/>
    <function name="std::experimental::pmr::null_memory_resource" link="cpp/experimental/null_memory_resource" since="exp"/>
    <function name="std::experimental::pmr::get_default_resource" link="cpp/experimental/get_default_resource" since="exp"/>
    <function name="std::experimental::pmr::set_default_resource" link="cpp/experimental/set_default_resource" since="exp"/>

    <class name="std::experimental::function" link="cpp/experimental/function" since="exp"/>

    <class name="std::experimental::promise (Library Fundamentals TS)" link="cpp/experimental/lib_extensions/promise" since="exp"/>
    <class name="std::experimental::packaged_task (Library Fundamentals TS)" link="cpp/experimental/lib_extensions/packaged_task" since="exp"/>

    <function name="std::experimental::sample" link="cpp/experimental/sample" since="exp"/>

    <function name="std::experimental::search" link="cpp/experimental/search" since="exp"/>
    <class name="std::experimental::default_searcher" link="cpp/experimental/default_searcher" since="exp"/>
    <function name="std::experimental::make_default_searcher" link="cpp/experimental/default_searcher" since="exp"/>
    <class name="std::experimental::boyer_moore_horspool_searcher" link="cpp/experimental/boyer_moore_horspool_searcher" since="exp"/>
    <function name="std::experimental::make_boyer_moore_horspool_searcher" link="cpp/experimental/boyer_moore_horspool_searcher" since="exp"/>
    <class name="std::experimental::boyer_moore_searcher" link="cpp/experimental/boyer_moore_searcher" since="exp"/>
    <function name="std::experimental::make_boyer_moore_searcher" link="cpp/experimental/boyer_moore_searcher" since="exp"/>

    <function name="std::experimental::apply" link="cpp/experimental/apply" since="exp"/>
    <class name="std::experimental::erased_type" link="cpp/experimental/erased_type" since="exp"/>
    <class name="std::experimental::invocation_type" link="cpp/experimental/invocation_type" since="exp"/>
    <class name="std::experimental::raw_invocation_type" link="cpp/experimental/invocation_type" since="exp"/>

    <class name="std::experimental::basic_string_view" link="cpp/experimental/basic_string_view" since="exp">
        <constructor/>
        <function name="operator="/>

        <function name="begin"/>
        <function name="cbegin" link="begin"/>
        <function name="rbegin"/>
        <function name="crbegin" link="rbegin"/>
        <function name="end"/>
        <function name="cend" link="end"/>
        <function name="rend"/>
        <function name="crend" link="rend"/>

        <function name="operator[]" link="operator_at"/>
        <function name="at"/>
        <function name="front"/>
        <function name="back"/>

        <function name="data"/>
        <function name="size"/>
        <function name="length" link="size"/>
        <function name="max_size"/>
        <function name="empty"/>
        <function name="remove_prefix"/>
        <function name="remove_suffix"/>
        <function name="swap"/>

        <function name="to_string"/>
        <function name="operator basic_string" link="to_string"/>

        <function name="copy"/>
        <function name="substr"/>
        <function name="compare"/>
        <function name="find"/>
        <function name="rfind"/>
        <function name="find_first_of"/>
        <function name="find_last_of"/>
        <function name="find_first_not_of"/>
        <function name="find_last_not_of"/>
        <const name="npos"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
        <overload name="operator&lt;&lt;" link="operator_ltlt"/>
        <specialization name="std::hash" link="hash"/>
    </class>

    <typedef name="std::experimental::string_view" alias="std::experimental::basic_string_view" since="exp"/>
    <typedef name="std::experimental::wstring_view" alias="std::experimental::basic_string_view" since="exp"/>
    <typedef name="std::experimental::u16string_view" alias="std::experimental::basic_string_view" since="exp"/>
    <typedef name="std::experimental::u32string_view" alias="std::experimental::basic_string_view" since="exp"/>

    <variable name="std::experimental::is_void_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_null_pointer_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_integral_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_floating_point_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_array_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_pointer_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_lvalue_reference_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_rvalue_reference_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_member_object_pointer_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_member_function_pointer_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_enum_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_union_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_class_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_function_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_reference_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_arithmetic_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_fundamental_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_object_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_scalar_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_compound_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_member_pointer_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_const_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_volatile_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_trivial_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_trivially_copyable_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_standard_layout_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_pod_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_literal_type_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_empty_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_polymorphic_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_abstract_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_final_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_signed_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_unsigned_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_constructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_trivially_constructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_nothrow_constructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_default_constructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_trivially_default_constructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_nothrow_default_constructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_copy_constructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_trivially_copy_constructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_nothrow_copy_constructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_move_constructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_trivially_move_constructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_nothrow_move_constructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_assignable_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_trivially_assignable_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_nothrow_assignable_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_copy_assignable_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_trivially_copy_assignable_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_nothrow_copy_assignable_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_move_assignable_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_trivially_move_assignable_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_nothrow_move_assignable_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_destructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_trivially_destructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_nothrow_destructible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::has_virtual_destructor_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::alignment_of_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::rank_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::extent_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_same_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_base_of_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_convertible_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::ratio_equal_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::ratio_not_equal_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::ratio_less_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::ratio_less_equal_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::ratio_greater_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::ratio_greater_equal_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::tuple_size_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::treat_as_floating_point_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_error_code_enum_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_error_condition_enum_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_bind_expression_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::is_placeholder_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>
    <variable name="std::experimental::uses_allocator_v" link="cpp/experimental/type_trait_variable_templates" since="exp"/>

    <!-- library extensions v2 -->
    <class name="std::experimental::is_detected" link="cpp/experimental/is_detected" since="exp"/>
    <variable name="std::experimental::is_detected_v" link="cpp/experimental/is_detected" since="exp"/>
    <class name="std::experimental::detected_or" link="cpp/experimental/is_detected" since="exp"/>
    <typedef name="std::experimental::detected_or_t" link="cpp/experimental/is_detected" since="exp"/>
    <class name="std::experimental::is_detected_exact" link="cpp/experimental/is_detected" since="exp"/>
    <variable name="std::experimental::is_detected_exact_v" link="cpp/experimental/is_detected" since="exp"/>
    <class name="std::experimental::is_detected_convertible" link="cpp/experimental/is_detected" since="exp"/>
    <variable name="std::experimental::is_detected_convertible_v" link="cpp/experimental/is_detected" since="exp"/>

    <class name="std::experimental::nonesuch" link="cpp/experimental/nonesuch" since="exp"/>

    <class name="std::experimental::propagate_const" link="cpp/experimental/propagate_const" since="exp">
        <constructor/>
        <function name="operator="/>
        <function name="swap"/>

        <function name="get"/>
        <function name="operator bool" link="operator_bool"/>
        <function name="operator*"/>
        <function name="operator-&gt;" link="operator*"/>
        <function name="operator element_type*" link="operator_element_type*"/>
        <function name="operator const element_type*" link="operator_element_type*"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
        <overload name="swap" link="swap2"/>

        <specialization name="std::hash" link="hash"/>
        <specialization name="std::equal_to" link="cmp_func"/>
        <specialization name="std::not_equal_to" link="cmp_func"/>
        <specialization name="std::less" link="cmp_func"/>
        <specialization name="std::greater" link="cmp_func"/>
        <specialization name="std::less_equal" link="cmp_func"/>
        <specialization name="std::greater_equal" link="cmp_func"/>
    </class>
    <function name="std::experimental::get_underlying" link="cpp/experimental/propagate_const/get_underlying" since="exp"/>

    <function name="std::experimental::not_fn" link="cpp/experimental/not_fn" since="exp"/>

    <class name="std::experimental::observer_ptr" link="cpp/experimental/observer_ptr" since="exp">
        <constructor/>

        <function name="release"/>
        <function name="reset"/>
        <function name="swap"/>

        <function name="get"/>
        <function name="operator bool" link="operator_bool"/>
        <function name="operator*"/>
        <function name="operator->" link="operator*"/>

        <function name="operator element_type*" link="operator_pointer"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
        <overload name="swap" link="swap2"/>

        <specialization name="std::hash" link="hash"/>
    </class>
    <function name="std::experimental::make_observer" link="cpp/experimental/observer_ptr/make_observer" since="exp"/>

    <function name="std::experimental::make_array" link="cpp/experimental/make_array" since="exp"/>
    <function name="std::experimental::to_array" link="cpp/experimental/to_array" since="exp"/>

    <class name="std::experimental::ostream_joiner" link="cpp/experimental/ostream_joiner" since="exp">
        <constructor/>
        <function name="operator="/>
        <function name="operator*"/>
        <function name="operator++" link="operator_arith"/>
        <function name="operator++(int)" link="operator_arith"/>
    </class>
    <function name="std::experimental::make_ostream_joiner" link="cpp/experimental/ostream_joiner/make_ostream_joiner" since="exp"/>

    <function name="std::experimental::gcd" link="cpp/experimental/gcd" since="exp"/>
    <function name="std::experimental::lcm" link="cpp/experimental/lcm" since="exp"/>

    <class name="std::experimental::source_location" link="cpp/experimental/source_location" since="exp">
        <constructor/>
        <function name="current"/>

        <function name="line"/>
        <function name="column"/>
        <function name="file_name"/>
        <function name="function_name"/>
    </class>

    <function name="std::experimental::randint" link="cpp/experimental/randint" since="exp"/>
    <function name="std::experimental::reseed" link="cpp/experimental/reseed" since="exp"/>
    <function name="std::experimental::shuffle" link="cpp/experimental/shuffle" since="exp"/>

    <typedef name="std::experimental::void_t" link="cpp/experimental/void_t" since="exp"/>
    <class name="std::experimental::conjunction" link="cpp/experimental/conjunction" since="exp"/>
    <const name="std::experimental::conjunction_v" link="cpp/experimental/conjunction" since="exp"/>
    <class name="std::experimental::disjunction" link="cpp/experimental/disjunction" since="exp"/>
    <const name="std::experimental::disjunction_v" link="cpp/experimental/disjunction" since="exp"/>
    <class name="std::experimental::negation" link="cpp/experimental/negation" since="exp"/>
    <const name="std::experimental::negation_v" link="cpp/experimental/negation" since="exp"/>
    <function name="std::experimental::erase(std::basic_string)" link="cpp/experimental/basic_string/erase" since="exp"/>
    <function name="std::experimental::erase_if(std::basic_string)" link="cpp/experimental/basic_string/erase_if" since="exp"/>
    <function name="std::experimental::erase(std::deque)" link="cpp/experimental/deque/erase" since="exp"/>
    <function name="std::experimental::erase_if(std::deque)" link="cpp/experimental/deque/erase_if" since="exp"/>
    <function name="std::experimental::erase(std::vector)" link="cpp/experimental/vector/erase" since="exp"/>
    <function name="std::experimental::erase_if(std::vector)" link="cpp/experimental/vector/erase_if" since="exp"/>
    <function name="std::experimental::erase(std::forward_list)" link="cpp/experimental/forward_list/erase" since="exp"/>
    <function name="std::experimental::erase_if(std::forward_list)" link="cpp/experimental/forward_list/erase_if" since="exp"/>
    <function name="std::experimental::erase(std::list)" link="cpp/experimental/list/erase" since="exp"/>
    <function name="std::experimental::erase_if(std::list)" link="cpp/experimental/list/erase_if" since="exp"/>
    <function name="std::experimental::erase_if(std::map)" link="cpp/experimental/map/erase_if" since="exp"/>
    <function name="std::experimental::erase_if(std::multimap)" link="cpp/experimental/multimap/erase_if" since="exp"/>
    <function name="std::experimental::erase_if(std::set)" link="cpp/experimental/set/erase_if" since="exp"/>
    <function name="std::experimental::erase_if(std::multiset)" link="cpp/experimental/multiset/erase_if" since="exp"/>
    <function name="std::experimental::erase_if(std::unordered_map)" link="cpp/experimental/unordered_map/erase_if" since="exp"/>
    <function name="std::experimental::erase_if(std::unordered_multimap)" link="cpp/experimental/unordered_multimap/erase_if" since="exp"/>
    <function name="std::experimental::erase_if(std::unordered_set)" link="cpp/experimental/unordered_set/erase_if" since="exp"/>
    <function name="std::experimental::erase_if(std::unordered_multiset)" link="cpp/experimental/unordered_multiset/erase_if" since="exp"/>

    <!-- experimental/filesystem -->
    <class name="std::experimental::filesystem::path" link="cpp/experimental/fs/path" since="exp">
        <constructor/>
        <destructor/>
        <function name="operator="/>
        <function name="assign"/>

        <function name="append"/>
        <function name="operator/=" link="append"/>
        <function name="concat"/>
        <function name="operator+=" link="concat"/>

        <function name="clear"/>
        <function name="make_preferred"/>
        <function name="remove_filename"/>
        <function name="replace_filename"/>
        <function name="replace_extension"/>
        <function name="swap"/>

        <function name="c_str" link="native"/>
        <function name="native"/>
        <function name="operator string_type" link="native"/>

        <function name="string"/>
        <function name="wstring" link="string"/>
        <function name="u8string" link="string"/>
        <function name="u16string" link="string"/>
        <function name="u32string" link="string"/>

        <function name="generic_string"/>
        <function name="generic_wstring" link="generic_string"/>
        <function name="generic_u8string" link="generic_string"/>
        <function name="generic_u16string" link="generic_string"/>
        <function name="generic_u32string" link="generic_string"/>

        <function name="compare"/>
        <function name="root_name"/>
        <function name="root_directory"/>
        <function name="root_path"/>
        <function name="relative_path"/>
        <function name="parent_path"/>
        <function name="filename"/>
        <function name="stem"/>
        <function name="extension"/>

        <function name="empty"/>
        <function name="has_root_path" link="has_path"/>
        <function name="has_root_name" link="has_path"/>
        <function name="has_root_directory" link="has_path"/>
        <function name="has_relative_path" link="has_path"/>
        <function name="has_parent_path" link="has_path"/>
        <function name="has_filename" link="has_path"/>
        <function name="has_stem" link="has_path"/>
        <function name="has_extension" link="has_path"/>

        <function name="is_absolute" link="is_absrel"/>
        <function name="is_relative" link="is_absrel"/>

        <function name="begin"/>
        <function name="end" link="begin"/>

        <overload name="swap" link="swap2"/>
        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
        <overload name="operator/" link="operator_slash"/>
        <overload name="operator&lt;&lt;" link="operator_ltltgtgt"/>
        <overload name="operator&gt;&gt;" link="operator_ltltgtgt"/>
        <overload name="u8path"/>
    </class>

    <class name="std::experimental::filesystem::filesystem_error" link="cpp/experimental/fs/filesystem_error" since="exp">
        <constructor/>
        <function name="operator="/>
        <function name="path1" link="path"/>
        <function name="path2" link="path"/>
        <function name="what"/>
    </class>

    <class name="std::experimental::filesystem::directory_entry" link="cpp/experimental/fs/directory_entry" since="exp">
        <constructor/>
        <function name="operator="/>
        <function name="assign"/>
        <function name="replace_filename"/>

        <function name="path"/>
        <function name="status"/>
        <function name="symlink_status" link="status"/>

        <overload name="operator==" link="operator_cmp"/>
        <overload name="operator!=" link="operator_cmp"/>
        <overload name="operator&lt;" link="operator_cmp"/>
        <overload name="operator&lt;=" link="operator_cmp"/>
        <overload name="operator&gt;" link="operator_cmp"/>
        <overload name="operator&gt;=" link="operator_cmp"/>
    </class>

    <class name="std::experimental::filesystem::directory_iterator" link="cpp/experimental/fs/directory_iterator" since="exp">
        <constructor/>
        <function name="operator="/>
        <function name="operator*"/>
        <function name="operator->" link="operator*"/>
        <function name="increment"/>
        <function name="operator++" link="increment"/>

        <overload name="std::experimental::filesystem::begin" link="begin"/>
        <overload name="std::experimental::filesystem::end" link="begin"/>
    </class>

    <class name="std::experimental::filesystem::recursive_directory_iterator" link="cpp/experimental/fs/recursive_directory_iterator" since="exp">
        <constructor/>
        <function name="operator*"/>
        <function name="operator->" link="operator*"/>
        <function name="options"/>
        <function name="depth"/>
        <function name="recursion_pending"/>

        <function name="operator="/>
        <function name="increment"/>
        <function name="operator++" link="increment"/>
        <function name="pop"/>
        <function name="disable_recursion_pending"/>

        <overload name="std::experimental::filesystem::begin" link="begin"/>
        <overload name="std::experimental::filesystem::end" link="begin"/>
    </class>

    <class name="std::experimental::filesystem::file_status" link="cpp/experimental/fs/file_status" since="exp">
        <constructor/>
        <function name="operator="/>
        <function name="type"/>
        <function name="permissions"/>
    </class>

    <class name="std::experimental::filesystem::space_info" link="cpp/experimental/fs/space_info" since="exp">
        <variable name="capacity" link="."/>
        <variable name="free" link="."/>
        <variable name="available" link="."/>
    </class>

    <class name="std::experimental::filesystem::file_type" link="cpp/experimental/fs/file_type" since="exp">
        <const name="none" link="."/>
        <const name="not_found" link="."/>
        <const name="regular" link="."/>
        <const name="directory" link="."/>
        <const name="symlink" link="."/>
        <const name="block" link="."/>
        <const name="character" link="."/>
        <const name="fifo" link="."/>
        <const name="socket" link="."/>
        <const name="unknown" link="."/>
    </class>

    <class name="std::experimental::filesystem::perms" link="cpp/experimental/fs/perms" since="exp">
        <const name="none" link="."/>
        <const name="owner_read" link="."/>
        <const name="owner_write" link="."/>
        <const name="owner_exec" link="."/>
        <const name="owner_all" link="."/>
        <const name="group_read" link="."/>
        <const name="group_write" link="."/>
        <const name="group_exec" link="."/>
        <const name="group_all" link="."/>
        <const name="others_read" link="."/>
        <const name="others_write" link="."/>
        <const name="others_exec" link="."/>
        <const name="others_all" link="."/>
        <const name="all" link="."/>
        <const name="set_uid" link="."/>
        <const name="set_gid" link="."/>
        <const name="sticky_bit" link="."/>
        <const name="mask" link="."/>

        <const name="unknown" link="."/>
        <const name="add_perms" link="."/>
        <const name="remove_perms" link="."/>
        <const name="resolve_symlinks" link="."/>
    </class>

    <class name="std::experimental::filesystem::copy_options" link="cpp/experimental/fs/copy_options" since="exp">
        <const name="none" link="."/>
        <const name="skip_existing" link="."/>
        <const name="overwrite_existing" link="."/>
        <const name="update_existing" link="."/>
        <const name="recursive" link="."/>
        <const name="copy_symlinks" link="."/>
        <const name="skip_symlinks" link="."/>
        <const name="directories_only" link="."/>
        <const name="create_symlinks" link="."/>
        <const name="create_hard_links" link="."/>
    </class>
    <class name="std::experimental::filesystem::directory_options" link="cpp/experimental/fs/directory_options" since="exp">
        <const name="none" link="."/>
        <const name="follow_directory_symlink" link="."/>
        <const name="overwrite_existing" link="."/>
        <const name="skip_permission_denied" link="."/>
    </class>
    <typedef name="std::experimental::filesystem::file_time_type" link="cpp/experimental/fs/file_time_type" since="exp"/>

    <function name="std::experimental::filesystem::absolute" link="cpp/experimental/fs/absolute" since="exp"/>
    <function name="std::experimental::filesystem::system_complete" link="cpp/experimental/fs/absolute" since="exp"/>
    <function name="std::experimental::filesystem::canonical" link="cpp/experimental/fs/canonical" since="exp"/>
    <function name="std::experimental::filesystem::copy" link="cpp/experimental/fs/copy" since="exp"/>
    <function name="std::experimental::filesystem::copy_file" link="cpp/experimental/fs/copy_file" since="exp"/>
    <function name="std::experimental::filesystem::copy_symlink" link="cpp/experimental/fs/copy_symlink" since="exp"/>
    <function name="std::experimental::filesystem::create_directory" link="cpp/experimental/fs/create_directory" since="exp"/>
    <function name="std::experimental::filesystem::create_directories" link="cpp/experimental/fs/create_directory" since="exp"/>
    <function name="std::experimental::filesystem::create_hard_link" link="cpp/experimental/fs/create_hard_link" since="exp"/>
    <function name="std::experimental::filesystem::create_symlink" link="cpp/experimental/fs/create_symlink" since="exp"/>
    <function name="std::experimental::filesystem::create_directory_symlink" link="cpp/experimental/fs/create_symlink" since="exp"/>
    <function name="std::experimental::filesystem::current_path" link="cpp/experimental/fs/current_path" since="exp"/>
    <function name="std::experimental::filesystem::exists" link="cpp/experimental/fs/exists" since="exp"/>
    <function name="std::experimental::filesystem::equivalent" link="cpp/experimental/fs/equivalent" since="exp"/>
    <function name="std::experimental::filesystem::file_size" link="cpp/experimental/fs/file_size" since="exp"/>
    <function name="std::experimental::filesystem::hard_link_count" link="cpp/experimental/fs/hard_link_count" since="exp"/>
    <function name="std::experimental::filesystem::last_write_time" link="cpp/experimental/fs/last_write_time" since="exp"/>
    <function name="std::experimental::filesystem::permissions" link="cpp/experimental/fs/permissions" since="exp"/>
    <function name="std::experimental::filesystem::read_symlink" link="cpp/experimental/fs/read_symlink" since="exp"/>
    <function name="std::experimental::filesystem::remove" link="cpp/experimental/fs/remove" since="exp"/>
    <function name="std::experimental::filesystem::remove_all" link="cpp/experimental/fs/remove" since="exp"/>
    <function name="std::experimental::filesystem::rename" link="cpp/experimental/fs/rename" since="exp"/>
    <function name="std::experimental::filesystem::resize_file" link="cpp/experimental/fs/resize_file" since="exp"/>
    <function name="std::experimental::filesystem::space" link="cpp/experimental/fs/space" since="exp"/>
    <function name="std::experimental::filesystem::status" link="cpp/experimental/fs/status" since="exp"/>
    <function name="std::experimental::filesystem::symlink_status" link="cpp/experimental/fs/status" since="exp"/>
    <function name="std::experimental::filesystem::temp_directory_path" link="cpp/experimental/fs/temp_directory_path" since="exp"/>
    <function name="std::experimental::filesystem::is_block_file" link="cpp/experimental/fs/is_block_file" since="exp"/>
    <function name="std::experimental::filesystem::is_character_file" link="cpp/experimental/fs/is_character_file" since="exp"/>
    <function name="std::experimental::filesystem::is_directory" link="cpp/experimental/fs/is_directory" since="exp"/>
    <function name="std::experimental::filesystem::is_empty" link="cpp/experimental/fs/is_empty" since="exp"/>
    <function name="std::experimental::filesystem::is_fifo" link="cpp/experimental/fs/is_fifo" since="exp"/>
    <function name="std::experimental::filesystem::is_other" link="cpp/experimental/fs/is_other" since="exp"/>
    <function name="std::experimental::filesystem::is_regular_file" link="cpp/experimental/fs/is_regular_file" since="exp"/>
    <function name="std::experimental::filesystem::is_socket" link="cpp/experimental/fs/is_socket" since="exp"/>
    <function name="std::experimental::filesystem::is_symlink" link="cpp/experimental/fs/is_symlink" since="exp"/>
    <function name="std::experimental::filesystem::status_known" link="cpp/experimental/fs/status_known" since="exp"/>

    <!-- concurrency -->
    <class name="std::experimental::future" link="cpp/experimental/future" since="exp">
        <constructor/>
        <function name="is_ready"/>
        <function name="then"/>
        <function name="operator="/>
    </class>

    <class name="std::experimental::shared_future" link="cpp/experimental/shared_future" since="exp">
        <constructor/>
        <function name="is_ready"/>
        <function name="then"/>
        <function name="operator="/>
    </class>

    <class name="std::experimental::promise (Concurrency TS)" link="cpp/experimental/concurrency/promise" since="exp"/>

    <class name="std::experimental::packaged_task (Concurrency TS)" link="cpp/experimental/concurrency/packaged_task" since="exp"/>

    <function name="std::experimental::when_all" link="cpp/experimental/when_all" since="exp"/>
    <function name="std::experimental::when_any" link="cpp/experimental/when_any" since="exp"/>
    <function name="std::experimental::make_ready_future" link="cpp/experimental/make_ready_future" since="exp"/>
    <function name="std::experimental::make_exceptional_future" link="cpp/experimental/make_exceptional_future" since="exp"/>

    <class name="std::experimental::latch" link="cpp/experimental/latch" since="exp">
        <constructor/>
        <destructor/>
        <function name="count_down_and_wait"/>
        <function name="count_down"/>
        <function name="is_ready"/>
        <function name="wait"/>
    </class>

    <class name="std::experimental::barrier" link="cpp/experimental/barrier" since="exp">
        <constructor/>
        <destructor/>
        <function name="arrive_and_wait"/>
        <function name="arrive_and_drop"/>
    </class>

    <class name="std::experimental::flex_barrier" link="cpp/experimental/flex_barrier" since="exp">
        <constructor/>
        <destructor/>
        <function name="arrive_and_wait"/>
        <function name="arrive_and_drop"/>
    </class>

    <class name="std::experimental::atomic_shared_ptr" link="cpp/experimental/atomic_shared_ptr" since="exp">
        <constructor/>
        <function name="operator="/>
        <function name="is_lock_free"/>
        <function name="store"/>
        <function name="load"/>
        <function name="operator shared_ptr&lt;T&gt;" link="operator_shared_ptr"/>
        <function name="exchange"/>
        <function name="compare_exchange_weak" link="compare_exchange"/>
        <function name="compare_exchange_strong" link="compare_exchange"/>
    </class>

    <class name="std::experimental::atomic_weak_ptr" link="cpp/experimental/atomic_weak_ptr" since="exp">
        <constructor/>
        <function name="operator="/>
        <function name="is_lock_free"/>
        <function name="store"/>
        <function name="load"/>
        <function name="operator weak_ptr&lt;T&gt;" link="operator_weak_ptr"/>
        <function name="exchange"/>
        <function name="compare_exchange_weak" link="compare_exchange"/>
        <function name="compare_exchange_strong" link="compare_exchange"/>
    </class>

    <!-- parallelism -->
    <class name="std::experimental::parallel::sequential_execution_policy" link="cpp/experimental/execution_policy_tag_t" since="exp"/>
    <class name="std::experimental::parallel::parallel_execution_policy" link="cpp/experimental/execution_policy_tag_t" since="exp"/>
    <class name="std::experimental::parallel::parallel_vector_execution_policy" link="cpp/experimental/execution_policy_tag_t" since="exp"/>
    <const name="std::experimental::parallel::seq" link="cpp/experimental/execution_policy_tag" since="exp"/>
    <const name="std::experimental::parallel::par" link="cpp/experimental/execution_policy_tag" since="exp"/>
    <const name="std::experimental::parallel::par_vec" link="cpp/experimental/execution_policy_tag" since="exp"/>
    <class name="std::experimental::parallel::is_execution_policy" link="cpp/experimental/is_execution_policy" since="exp"/>
    <function name="std::experimental::parallel::reduce" link="cpp/experimental/reduce" since="exp"/>
    <function name="std::experimental::parallel::transform_reduce" link="cpp/experimental/transform_reduce" since="exp"/>
</index>
