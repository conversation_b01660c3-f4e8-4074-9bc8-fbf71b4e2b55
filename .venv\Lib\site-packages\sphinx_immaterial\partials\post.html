{#-
  This file was automatically generated - do not edit
-#}
<article class="md-post md-post--excerpt">
  <header class="md-post__header">
    {% if post.authors %}
      <nav class="md-post__authors md-typeset">
        {% for author in post.authors %}
          <span class="md-author">
            <img src="{{ author.avatar | url }}" alt="{{ author.name }}">
          </span>
        {% endfor %}
      </nav>
    {% endif %}
    <div class="md-post__meta md-meta">
      <ul class="md-meta__list">
        <li class="md-meta__item">
          <time datetime="{{ post.config.date.created }}">
            {{- post.config.date.created | date -}}
          </time>
          {#- Collapse whitespace -#}
        </li>
        {% if post.categories %}
          <li class="md-meta__item">
            {{ lang.t("blog.categories.in") }}
            {% for category in post.categories %}
              <a href="{{ category.url | url }}" class="md-meta__link">
                {{- category.title -}}
              </a>
              {%- if loop.revindex > 1 %}, {% endif -%}
            {% endfor -%}
          </li>
        {% endif %}
        {% if post.config.readtime %}
          {% set time = post.config.readtime %}
          <li class="md-meta__item">
            {% if time == 1 %}
              {{ lang.t("readtime.one") }}
            {% else %}
              {{ lang.t("readtime.other") | replace("#", time) }}
            {% endif %}
          </li>
        {% endif %}
      </ul>
      {% if post.config.draft %}
        <span class="md-draft">
          {{ lang.t("blog.draft") }}
        </span>
      {% endif %}
    </div>
  </header>
  <div class="md-post__content md-typeset">
    {{ post.content }}
    {% if post.more %}
      <nav class="md-post__action">
        <a href="{{ post.url | url }}">
          {{ lang.t("blog.continue") }}
        </a>
      </nav>
    {% endif %}
  </div>
</article>
