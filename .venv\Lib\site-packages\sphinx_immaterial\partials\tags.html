{#-
  This file was automatically generated - do not edit
-#}
{% if page.meta and page.meta.hide %}
  {% set hidden = "hidden" if "tags" in page.meta.hide %}
{% endif %}
{% if tags %}
  <nav class="md-tags" {{ hidden }}>
    {% for tag in tags %}
      {% set class = "md-tag" %}
      {% if config.extra.tags %}
        {% set class = class ~ " md-tag-icon" %}
        {% if tag.name in config.extra.tags %}
          {% set class = class ~ " md-tag--" ~ config.extra.tags[tag.name] %}
        {% endif %}
      {% endif %}
      {% if tag.url %}
        <a href="{{ tag.url | url }}" class="{{ class }}">
          {{- tag.name -}}
        </a>
      {% else %}
        <span class="{{ class }}">
          {{- tag.name -}}
        </span>
      {% endif %}
    {% endfor %}
  </nav>
{% endif %}
